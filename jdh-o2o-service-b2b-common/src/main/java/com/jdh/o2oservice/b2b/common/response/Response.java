package com.jdh.o2oservice.b2b.common.response;

import lombok.ToString;

import java.io.Serializable;
import java.util.HashMap;

/**
 * 通用返回结果
 *
 * @author: yang<PERSON>yu
 * @date: 2022/8/25 10:19 下午
 * @version: 1.0
 */
@ToString
public class Response<T> extends BaseResponse implements Serializable {
    /**
     *
     */
    private static final long serialVersionUID = 3281261508261282781L;
    /**
     *
     */
    private T data;

    /**
     *
     */
    public Response() {
    }

    /**
     *
     */
    public T getData() {
        return this.data;
    }

    /**
     *
     */
    public void setData(T data) {
        this.data = data;
    }


    public static <T> Response<T> buildNewResult(String errorCode, String errorMsg, T data) {
        Response<T> r = new Response<T>();
        r.setCode(errorCode);
        r.setMsg(errorMsg);
        r.setData(data);
        return r;
    }

    /**
     * 组装成功的 ApiResult
     *
     * @param data 成功实体
     * @param <T>
     * @return ApiResult
     */
    public static <T> Response<T> buildSuccessResult(T data) {
        return buildNewResult(SUCCESS, SUCCESS_MSG, data);
    }

    /**
     * 组装未知异常的 ApiResult
     *
     * @param <T>
     * @return ApiResult
     */
    public static <T> Response<T> buildUnknownErrorResult() {
        return buildNewResult(UNKNOWN_ERROR, UNKNOWN_ERROR_MSG, null);
    }

    public static <T> Response<T> buildErrorResult(String errorCode, String errorMsg) {
        return buildNewResult(errorCode, errorMsg, null);
    }

    public static <T> Response<T> buildHelpErrorResult(String errorCode, String errorMsg, String helpMessage) {
        Response<T> r = buildNewResult(errorCode, errorMsg, null);
        r.setHelpMessage(helpMessage);
        return r;
    }

    public static <T> Response<T> buildCustomizeErrorResult(String errorCode, String errorMsg, T data) {
        return buildNewResult(errorCode, errorMsg, data);
    }

    public static <T> Response<T> buildCustomizeHelpErrorResult(String errorCode, String errorMsg, String helpMessage, T data) {
        Response<T> r = buildNewResult(errorCode, errorMsg, data);
        r.setHelpMessage(helpMessage);
        return r;
    }

    public static <T> Response<T> buildCustomizeErrorResult(String errorCode, String errorMsg, HashMap<String, String> extMap, T data) {
        Response<T> r = buildNewResult(errorCode, errorMsg, data);
        r.setExtMap(extMap);
        return r;
    }
}
