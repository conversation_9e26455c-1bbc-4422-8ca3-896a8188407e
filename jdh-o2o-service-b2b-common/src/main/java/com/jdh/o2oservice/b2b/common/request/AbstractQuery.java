package com.jdh.o2oservice.b2b.common.request;
import java.util.Set;

/**
 * 抽象请求参数
 * @author: yang<PERSON><PERSON>
 * @date: 2022/8/25 10:30 下午
 * @version: 1.0
 */
public abstract class AbstractQuery extends AbstractRequest {
    /** 查询的结果要素 */
    private Set<String> elementCode;
    /** */
    public Set<String> getElementCode() {
        return elementCode;
    }
    /** */
    public void setElementCode(Set<String> elementCode) {
        this.elementCode = elementCode;
    }
}
