package com.jdh.o2oservice.b2b.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @Author: duanqiaona1
 * @Date: 2024/4/25 10:03 上午
 * @Description:
 */
@Data
@Component
@ConfigurationProperties(prefix = "uim")
public class UimB2bConfig {

    /**
     * 应用key
     */
    private String appKey;
    /**
     * 应用token
     */
    private String appToken;
    /**
     * 租户
     */
    private String tenantCode;

    /**
     * dimResCode
     */
    private String dimResCode;

    /**
     * 语言
     */
    private String lang;
}
