package com.jdh.o2oservice.b2b.common.request;

import lombok.Data;

/**
 * 抽象请求参数
 * @author: yang<PERSON>yu
 * @date: 2022/8/25 10:30 下午
 * @version: 1.0
 */
@Data
public abstract class  AbstractRequest {

    /** (垂直)业务身份 */
    protected String verticalCode;
    /** 服务类型 */
    protected String serviceType;
    /** 业务模式 */
    protected String businessMode;
    /** 客户端名称，不可为空 */
    protected String appName;
    /** 客户端ip，不可为空 */
    protected String clientIp;
    /** 授权的key */
    protected String accessKey;
    /**
     * 用户使用环境
     * ["jdhapp","jdhapp","jdheapp","jdmeapp","miniprogram","wxwork","wexin","qq","h5"]
     *  京东app    健康app   E企健康    京me      小程序         企业微信  微信     qq   h5
     */
    protected String envType;
    /** pin */
    protected String userPin;
    /** 企业id */
    private Long enterpriseId;
    /** 代管模式 */
    private Boolean escrowMode;
    /** cookie中erp账号 */
    private String escrowNo;
    /** 企业用户状态 1-启用 0-禁用 */
    private Integer enterpriseUserAvailable;

}
