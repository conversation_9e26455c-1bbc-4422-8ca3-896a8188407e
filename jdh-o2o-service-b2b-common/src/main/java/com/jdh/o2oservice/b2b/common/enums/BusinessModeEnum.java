package com.jdh.o2oservice.b2b.common.enums;
import lombok.Getter;
import java.util.Objects;

/**
 * 业务模式
 * @author: yang<PERSON>yu
 * @date: 2023/12/18 6:45 下午
 * @version: 1.0
 */
@Getter
public enum BusinessModeEnum {

    /**
     * 业务模式枚举
     */
    SELF_TEST("selfTest", "自检测"),
    ANGEL_TEST("angelTest", "服务者检测"),
    ANGEL_CARE("angelCare", "服务者护理"),
    ;

    /**
     *  BusinessModeEnum
     */
    BusinessModeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 按代码获取枚举
     *
     * @param code 代码
     * @return {@link BusinessModeEnum}
     */
    public static BusinessModeEnum getEnumByCode(String code){
        for (BusinessModeEnum value : BusinessModeEnum.values()) {
            if(Objects.equals(value.getCode(), code)){
                return value;
            }
        }
        return null;
    }


    /**
     * -- GETTER --
     *
     */
    private final String code;

    /**
     * -- GETTER --
     *
     */
    private final String name;

}
