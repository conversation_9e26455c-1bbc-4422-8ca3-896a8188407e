package com.jdh.o2oservice.b2b.export.enterprisevoucher.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Set;

/**
 * @Description 履约详情
 * @Date 2025/2/26 下午9:48
 * <AUTHOR>
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SyncPromiseStatusRequest implements Serializable {

    /**
     * 企业服务单id
     */
    private Set<Long> enterpriseVoucherIds;

}
