package com.jdh.o2oservice.b2b.export.enterprisevoucher.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description 服务者职业资质信息
 * @Date 2025/3/3 下午1:30
 * <AUTHOR>
 **/
@Data
public class AngelProfessionQualificationRelDto implements Serializable {

    /**
     * 服务者id
     */
    private Long angelId;

    /**
     * 职业code
     */
    private String professionCode;

    /**
     * 资质code
     */
    private String qualificationCode;

    /**
     * 资质名称
     */
    private String qualificationName;

    /**
     * 资质证图片链接
     */
    private String qualificationUrl;

    /**
     * 资质证图片链接
     */
    private List<String> qualificationUrlList;

    /**
     * 创建日期
     */
    private Date createTime;

    /**
     * 修改日期
     */
    private Date updateTime;

    /**
     * 有效标志 0 无效 1 有效
     */
    private Integer yn;
}
