package com.jdh.o2oservice.b2b.export.enterpriseremark.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 企业备注DTO
 *
 * <AUTHOR>
 * @date 2025/03/10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EnterpriseRemarkDto implements Serializable {

    /**
     * 企业备注ID
     */
    private Long enterpriseRemarkId;

    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 备注内容
     */
    private String content;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;
}
