package com.jdh.o2oservice.b2b.export.enterpriseaccount.query;
import com.jdh.o2oservice.b2b.common.request.AbstractQuery;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class B2bEnterpriseAccountRequest extends AbstractQuery implements Serializable {

    /**
     * 企业ID
     */
    private Long enterpriseId;
}
