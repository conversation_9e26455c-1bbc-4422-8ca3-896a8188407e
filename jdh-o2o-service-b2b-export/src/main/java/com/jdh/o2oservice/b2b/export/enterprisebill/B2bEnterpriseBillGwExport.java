package com.jdh.o2oservice.b2b.export.enterprisebill;

import com.jdh.o2oservice.b2b.common.response.PageDto;
import com.jdh.o2oservice.b2b.common.response.Response;
import com.jdh.o2oservice.b2b.export.enterprise.cmd.B2bEnterpriseContractCmd;
import com.jdh.o2oservice.b2b.export.enterprise.query.B2bEnterprisePageRequest;
import com.jdh.o2oservice.b2b.export.enterprise.query.B2bEnterpriseRequest;
import com.jdh.o2oservice.b2b.export.enterpriseaccount.dto.B2bEnterpriseAcountContractDto;
import com.jdh.o2oservice.b2b.export.enterpriseaccount.dto.B2bEnterpriseContractDetailDto;
import com.jdh.o2oservice.b2b.export.enterprisebill.dto.B2bEnterpriseBillDetailDto;
import com.jdh.o2oservice.b2b.export.enterprisebill.dto.B2bEnterpriseBillDto;
import com.jdh.o2oservice.b2b.export.enterprisebill.query.B2bEnterpriseBillRequest;

import java.util.Map;

/**
 * @ClassName:B2bEnterpriseBillGwExport
 * @Description:B2b企业账单Export
 * @Author: liwenming
 * @Date: 2025/2/27 15:14
 * @Vserion: 1.0
 **/
public interface B2bEnterpriseBillGwExport {

    /**
     * 查询企业月账单
     * @param param
     * @return
     */
    Response<B2bEnterpriseBillDto> queryEnterpriseBill(Map<String, String> param);

    /**
     * 查询企业月账单明细
     * @param param
     * @return
     */
    Response<PageDto<B2bEnterpriseBillDetailDto>> queryEnterpriseBillDetail(Map<String, String> param);

    /**
     * 查询企业月账单明细
     * @param b2bEnterpriseBillRequest
     * @return
     */
    Response<PageDto<B2bEnterpriseBillDetailDto>> queryEnterpriseBillDetailList(B2bEnterpriseBillRequest b2bEnterpriseBillRequest);

    /**
     * 导出企业月账单明细
     * @param param
     * @return
     */
    Response<Boolean> exportEnterpriseBillDetail(Map<String,String> param);

    /**
     * 企业月账单Job
     */
    Response<Boolean> enterpriseBillOfMonthJob();

    /**
     * 企业账单--确认账单
     * @param param
     * @return
     */
    Response<Boolean> configEnterpriseBill(Map<String, String> param);


    /**
     * 查询合同信息
     * @param request
     * @return
     */
    Response<B2bEnterpriseContractDetailDto> queryAccountInfoByContractNum(B2bEnterpriseRequest request);

    /**
     * 保存合同
     * @param cmd
     * @return
     */
    Response<Boolean> saveEnterpriseContract(B2bEnterpriseContractCmd cmd);

    /**
     * 删除合同
     * @param cmd
     * @return
     */
    Response<Boolean> deleteEnterpriseContract(B2bEnterpriseContractCmd cmd);

    Response<PageDto<B2bEnterpriseAcountContractDto>> queryPageEnterpriseContract(B2bEnterprisePageRequest pageRequest);
}
