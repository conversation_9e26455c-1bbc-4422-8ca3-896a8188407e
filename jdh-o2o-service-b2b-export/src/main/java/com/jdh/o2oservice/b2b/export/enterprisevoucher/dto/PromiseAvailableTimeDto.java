package com.jdh.o2oservice.b2b.export.enterprisevoucher.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description 可约时间
 * @Date 2025/2/26 下午8:51
 * <AUTHOR>
 **/
@Data
public class PromiseAvailableTimeDto implements Serializable {

    /**
     * 主键
     */
    private String id;

    /**
     * 标签
     */
    private String label;

    /**
     * 值
     */
    private String value;

    /**
     * 是否有效
     */
    private Boolean disabled;

    /**
     * 是否存在子节点
     */
    private Boolean existsChildren;
}
