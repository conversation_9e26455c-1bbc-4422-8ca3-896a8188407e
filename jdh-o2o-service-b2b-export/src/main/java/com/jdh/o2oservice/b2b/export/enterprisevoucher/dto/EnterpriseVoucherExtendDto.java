package com.jdh.o2oservice.b2b.export.enterprisevoucher.dto;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description 企业服务单拓展数据
 * @Date 2025/3/5 下午5:23
 * <AUTHOR>
 **/
@Data
public class EnterpriseVoucherExtendDto implements Serializable {

    /**
     * address
     */
    private PromiseAddressDto address;

    /**
     * patientList
     */
    private List<PromisePatientDto> patientList;

    /**
     * service
     */
    private PromiseSkuDto service;

    /**
     * appointmentTime
     */
    private PromiseAppointmentTimeDto appointmentTime;

    /**
     * 意向护士
     */
    private PromiseIntendedNurseDto intendedNurse;

    /**
     * 备注
     */
    private String remark;

    /**
     * 取消完成时间
     */
    private Date finishTime;
}
