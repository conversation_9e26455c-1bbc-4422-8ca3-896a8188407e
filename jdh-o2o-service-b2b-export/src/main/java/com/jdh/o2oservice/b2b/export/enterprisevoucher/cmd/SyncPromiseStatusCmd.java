package com.jdh.o2oservice.b2b.export.enterprisevoucher.cmd;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * SyncPromiseStatusCmd
 *
 * <AUTHOR>
 * @date 2025/02/27
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SyncPromiseStatusCmd {

    /**
     * 业务模式
     */
    private String verticalCode;

    /**
     * 履约单id
     */
    private Long promiseId;

    /**
     * sourceVoucherId
     */
    private String sourceVoucherId;

    /**
     * 状态
     */
    private Integer promiseStatus;

}
