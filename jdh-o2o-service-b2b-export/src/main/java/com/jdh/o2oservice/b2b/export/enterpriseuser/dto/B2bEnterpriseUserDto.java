package com.jdh.o2oservice.b2b.export.enterpriseuser.dto;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 企业用户
 */
@Data
public class B2bEnterpriseUserDto implements Serializable {

    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 用户id
     */
    private Long enterpriseUserId;

    /**
     * 用户pin
     */
    private String userPin;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户角色 1-管理员
     */
    private Integer userRole;

    /**
     * 用户状态 1-启用 0-禁用
     */
    private Integer available;

    /**
     * 最近启用日期
     */
    private Date lastAvailableTime;

    /**
     * 扩展信息
     */
    private String extend;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 更新时间
     */
    private Date updateTime;
}
