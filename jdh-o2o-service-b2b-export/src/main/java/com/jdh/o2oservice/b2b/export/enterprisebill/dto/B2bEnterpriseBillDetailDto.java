package com.jdh.o2oservice.b2b.export.enterprisebill.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName:B2b企业账单
 * @Description:
 * @Author: liwenming
 * @Date: 2025/2/25 15:14
 * @Vserion: 1.0
 **/
@Data
public class B2bEnterpriseBillDetailDto {

    /**
     * 账单Id
     */
    private Long billId;
    /**
     * 企业Id
     */
    private Long enterpriseId;

    /**
     * 企业服务单id
     */
    private Long enterpriseVoucherId;

    /**
     * 服务单id
     */
    private Long voucherId;

    /**
     * 履约单id
     */
    private Long promiseId;

    /**
     * 企业服务skuId
     */
    private Long enterpriseSkuId;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * sku名称
     */
    private String skuName;

    /**
     * sku短名称
     */
    private String skuShortName;

    /**
     * 履约单状态：待接单/已接单/护士已出发/开始服务/服务完成/已取消
     */
    private Integer promiseStatus;

    /**
     * 履约单状态：待接单/已接单/护士已出发/开始服务/服务完成/已取消
     */
    private String promiseStatusDesc;
    /**
     * 用户名称
     */
    private String enterpriseUserName;

    /**
     * 源订单号
     */
    private String sourceOrderId;

    /**
     * 源订单平台
     */
    private String sourceOrderPlatform;

    /**
     * 下单时间
     */
    private String orderTime;
    /**
     * 账单金额--结算金额
     */
    private BigDecimal billAmount;
    /**
     * 完成/取消时间
     */
    private String finishTime;
    /**
     * 收货人地址全称(省市县+收货人地址)
     */
    private String fullAddress;
    /**
     * 预约人姓名
     */
    private String nameList;
    /**
     * 预约开始时间
     * 格式 yyyy-MM-dd HH:mm
     */
    private String appointmentStartTime;

    /**
     * erp_pin
     */
    private String erpPin;


}
