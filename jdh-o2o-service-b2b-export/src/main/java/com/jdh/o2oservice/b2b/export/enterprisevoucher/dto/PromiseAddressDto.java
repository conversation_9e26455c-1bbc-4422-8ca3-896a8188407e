package com.jdh.o2oservice.b2b.export.enterprisevoucher.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description 预约地址
 * @Date 2025/3/5 下午5:24
 * <AUTHOR>
 **/
@Data
public class PromiseAddressDto implements Serializable {

    /**
     * 地址id
     */
    private String addressId;

    /**
     * 收货人省份
     */
    private Integer provinceId;

    /**
     * 收货人城市
     */
    private Integer cityId;

    /**
     * 县级地区
     */
    private Integer countyId;

    /**
     * 地区街道
     */
    private Integer townId;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 县级名称
     */
    private String countyName;

    /**
     * 乡镇街道名称
     */
    private String townName;

    /**
     * 收货人地址全称(省市县+收货人地址)
     */
    private String fullAddress;

    /**
     * 姓名
     */
    private String name;

    /**
     * 电话
     */
    private String mobile;
}
