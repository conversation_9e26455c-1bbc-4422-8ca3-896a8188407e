package com.jdh.o2oservice.b2b.export.enterprisevoucher.query;

import com.jdh.o2oservice.b2b.common.request.AbstractQuery;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import java.util.List;

/**
 * @ClassName TextParseAddressRequest
 * @Description
 * <AUTHOR>
 * @Date 2025/1/7 13:49
 **/
@Data
public class QueryIntendedAngelRequest extends AbstractQuery {

    /**
     * 服务id
     */
    @NotEmpty(message = "服务ID不能为空")
    private List<Long> serviceIds;

    /**
     * 地址id
     */
    private Long addressId;

    /**
     * fullAddress
     */
    private String fullAddress;

    /**
     * cps邀请码
     */
    @NotBlank(message = "CPS邀请码不能为空")
    private String cpsInviteCode;

}