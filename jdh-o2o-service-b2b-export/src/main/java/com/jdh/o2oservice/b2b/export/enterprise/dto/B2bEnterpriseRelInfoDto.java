package com.jdh.o2oservice.b2b.export.enterprise.dto;
import com.jdh.o2oservice.b2b.export.enterpriseaccount.dto.B2bEnterpriseAccountDto;
import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * @Description 企业信息
 * @Date 2025/2/26 上午10:54
 * <AUTHOR>
 **/
@Data
public class B2bEnterpriseRelInfoDto implements Serializable {

    /**
     * 企业信息
     */
    private B2bEnterpriseDto b2bEnterprise;

    /**
     * 企业sku
     */
    private List<B2bEnterpriseRelSkuDto> enterpriseRelSkuList;

    /**
     * 企业用户
     */
    private List<B2bEnterpriseRelUserDto> enterpriseRelUserList;

}
