package com.jdh.o2oservice.b2b.export.enterprise.query;
import com.jdh.o2oservice.b2b.common.request.AbstractPageQuery;
import lombok.Data;
import java.io.Serializable;

/**
 * @Description 分页查询企业
 * @Date 2025/2/24 下午8:37
 * <AUTHOR>
 **/
@Data
public class B2bEnterprisePageRequest extends AbstractPageQuery implements Serializable {

    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 企业名称
     */
    private String name;

    /**
     * 状态 1-启用 0-禁用
     */
    private Integer available;

    /**
     * 垂直业务身份编码
     */
    private String verticalCode;

    /**
     * 企业短名称
     */
    private String shortName;

    /**
     * 企业ID
     */
    private String searchEnterpriseId;
}
