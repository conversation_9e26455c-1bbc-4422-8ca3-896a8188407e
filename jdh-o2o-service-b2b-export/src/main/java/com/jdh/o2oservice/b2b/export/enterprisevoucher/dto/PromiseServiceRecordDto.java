package com.jdh.o2oservice.b2b.export.enterprisevoucher.dto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Description 服务记录
 * @Date 2025/2/26 下午9:40
 * <AUTHOR>
 **/
@Data
public class PromiseServiceRecordDto implements Serializable {

    /**
     * 医生着装图片
     */
    private List<String> clothingFileList;

    /**
     * 医疗废物处理图片
     */
    private List<String> wasteDestroyFileList;

    /**
     * 服务记录图片
     */
    private List<String> serviceRecordFileList;

    /**
     * 电子签名图片
     */
    private List<String> electSignatureFileList;

    /**
     * 知情同意书
     */
    private List<String> letterOfConsentFileList;
}
