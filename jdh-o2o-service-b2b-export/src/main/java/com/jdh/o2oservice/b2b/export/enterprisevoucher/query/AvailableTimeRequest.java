package com.jdh.o2oservice.b2b.export.enterprisevoucher.query;
import com.jdh.o2oservice.b2b.common.request.AbstractQuery;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Description 查询可约时间
 * @Date 2025/2/26 下午8:50
 * <AUTHOR>
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AvailableTimeRequest extends AbstractQuery implements Serializable {
    /**
     * 履约单ID
     */
    private Long promiseId;

    /**
     * 企业服务单ID
     */
    private Long enterpriseVoucherId;

    /**
     * 用户pin
     */
    private String userPin;

    /**
     * 地址id
     */
    private Long addressId;

    /**
     * 地址明细
     */
    private String fullAddress;

    /**
     * 父级日期id
     */
    private String parentDateId;

    /**
     * sku列表
     */
    private List<Long> skuIds;

    /**
     * 企业服务skuId列表
     */
    private List<Long> enterpriseSkuIds;

    /**
     * 1 展示可约时间 2展示不可约时间 3或null展示全部
     */
    private Integer showTimeType;
}
