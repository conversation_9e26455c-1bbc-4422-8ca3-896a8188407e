package com.jdh.o2oservice.b2b.export.enterpriseaccount.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description 企业合同信息
 * @Date 2025/4/29 下午8:15
 * <AUTHOR>
@Data
public class B2bEnterpriseContractDetailDto implements Serializable {

    /**
     * 企业ID
     */
    private Long enterpriseId;
    /**
     * 企业名称
     */
    private String enterName;
    /**
     * 合同编号
     */
    private String contractNumber;
    /**
     * 合同名称
     */
    private String name;
    /**
     * 合同主体
     */
    private Integer ou;
    /**
     * 有效标识：1/0
     */
    private Integer valid;
    /**
     * 合同开始时间
     */
    private String startTime;
    /**
     * 合同结束时间
     */
    private String endTime;
    /**
     * 合同状态
     * 1-拟定 2-待批 3-批准 4-已签署（终态）
     * 5-已拒绝 6-已取消（终态）9-已删除（终态）
     */
    private Integer status;
    /**
     * 合同状态
     * 1-拟定 2-待批 3-批准 4-已签署（终态）
     * 5-已拒绝 6-已取消（终态）9-已删除（终态）
     */
    private String statusDesc;
    /**
     * 结算周期:1-日 2-月 3-季度 4-年 5-周 6-半月 7-其他时间
     */
    private Integer settlementPeriod;
    /**
     * 结算周期:1-日 2-月 3-季度 4-年 5-周 6-半月 7-其他时间
     */
    private String settlementPeriodDesc;
    /**
     * 结算天数
     */
    private Integer settlementDays;
}
