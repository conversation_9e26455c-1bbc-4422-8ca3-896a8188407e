package com.jdh.o2oservice.b2b.export.enterprisevoucher;

import com.jdh.o2oservice.b2b.common.response.PageDto;
import com.jdh.o2oservice.b2b.common.response.Response;
import com.jdh.o2oservice.b2b.export.enterprisevoucher.dto.*;
import com.jdh.o2oservice.b2b.export.openapi.dto.AngelServiceRecordDTO;
import com.jdh.o2oservice.b2b.export.openapi.dto.EnterpriseVoucherActionLogDto;
import com.jdh.o2oservice.b2b.export.openapi.query.AngelServiceRecordRequest;
import com.jdh.o2oservice.b2b.export.openapi.query.QueryEnterpriseVoucherActionLog;

import java.util.List;
import java.util.Map;

/**
 * B2bEnterpriseVoucherGwExport
 *
 * <AUTHOR>
 * @date 2025/02/27
 */
public interface B2bEnterpriseVoucherGwExport {


    /**
     * 提交
     *
     * @param param param
     * @return {@link Response }<{@link Boolean }>
     */
    Response<Boolean> submitPromise(Map<String,String> param);

    /**
     * 可用时间
     *
     * @param param param
     * @return {@link Response }<{@link List }<{@link PromiseAvailableTimeDto }>>
     */
    Response<List<PromiseAvailableTimeDto>> queryAvailableTime(Map<String,String> param);

    /**
     * 修改预约时间
     *
     * @param param param
     * @return {@link Response }<{@link Boolean }>
     */
    Response<Boolean> modifyAppointmentTime(Map<String,String> param);

    /**
     * 作废履约服务
     *
     * @param param param
     * @return {@link Response }<{@link Boolean }>
     */
    Response<Boolean> invalidVoucher(Map<String,String> param);

    /**
     * 履约单详情
     *
     * @param param param
     * @return {@link Response }<{@link CompletePromiseDetailsDto }>
     */
    Response<CompletePromiseDetailsDto> queryPromiseDetail(Map<String,String> param);


    /**
     * 分页查询履约单列表
     *
     * @param param param
     * @return {@link Response }<{@link PageDto }<{@link CompletePromiseDetailsDto }>>
     */
    Response<PageDto<PromiseDetailsDto>> queryPromiseDetailPage(Map<String,String> param);

    /**
     * 导出履约单列表
     *
     * @param param param
     * @return {@link Response }<{@link PageDto }<{@link CompletePromiseDetailsDto }>>
     */
    Response<Boolean> exportPromiseDetailPage(Map<String,String> param);

    /**
     * 查询上传excel的模板url地址
     * @param param
     * @return
     */
    Response<UploadExcelTempUrlDto> queryUploadExcelTempUrl(Map<String,String> param);

    /**
     * submitPromiseByUpload
     *
     * @param param
     * @return
     */
    Response<Boolean> submitPromiseByUpload(Map<String,String> param);

    /**
     * 查询意向护士
     * @param param 参数
     * @return 意向护士信息
     */
    Response<IntendedAngelDto> queryIntendedAngel(Map<String, String> param);

    /**
     * 履约单状态同步
     * @param param
     * @return
     */
    Response<Boolean> syncPromiseStatus(Map<String,String> param);

    /**
     * 履约单状态同步任务
     * @return
     */
    Response<Boolean> syncPromiseStatusTask();

    /**
     * 修改履约单的原单信息
     * @param param
     * @return
     */
    Response<Boolean> modifyPromiseOrderPlatform(Map<String,String> param);

    Response<Long> queryEnterpriseId(Long enterpriseVoucherId);


    /**
     * 查询电子护理单
     * @param param AngelServiceRecordRequest 对象，包含查询条件。
     * @return AngelServiceRecordDTO 列表，表示查询到的电子护理单记录。
     */
    Response<AngelServiceRecordDTO> queryBAngelServiceRecordList(Map<String,String> param);


    /**
     * 查询企业服务单操作日志
     *
     * @param
     * @return
     */
    Response<List<EnterpriseVoucherActionLogDto>> queryEnterpriseVoucherActionLog(Map<String,String> param);

}
