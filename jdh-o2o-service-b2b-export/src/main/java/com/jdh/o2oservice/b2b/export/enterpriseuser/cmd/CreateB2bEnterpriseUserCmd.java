package com.jdh.o2oservice.b2b.export.enterpriseuser.cmd;
import lombok.Data;
import java.io.Serializable;

/**
 * 创建企业用户
 */
@Data
public class CreateB2bEnterpriseUserCmd implements Serializable {

    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 用户pin
     */
    private String userPin;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户角色 1-管理员
     */
    private Integer userRole;

    /**
     * 用户状态 1-启用 0-禁用
     */
    private Integer available;

    /**
     * 扩展信息
     */
    private String extend;

    /**
     * 操作人
     */
    private String operator;
}
