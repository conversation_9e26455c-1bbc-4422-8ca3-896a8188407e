package com.jdh.o2oservice.b2b.export.enterprisesku.query;
import com.jdh.o2oservice.b2b.common.request.AbstractQuery;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description 查询企业sku
 * @Date 2025/2/24 下午8:19
 * <AUTHOR>
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class B2bEnterpriseSkuRequest extends AbstractQuery implements Serializable {

    /**
     * 企业服务skuId
     */
    private Long enterpriseSkuId;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 合同编号
     */
    private String contractNumber;
}
