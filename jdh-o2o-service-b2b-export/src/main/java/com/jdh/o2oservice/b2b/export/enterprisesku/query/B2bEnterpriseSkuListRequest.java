package com.jdh.o2oservice.b2b.export.enterprisesku.query;

import com.jdh.o2oservice.b2b.common.request.AbstractQuery;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 企业SKU列表请求对象
 *
 * <AUTHOR>
 * @date 2025/03/15
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class B2bEnterpriseSkuListRequest extends AbstractQuery implements Serializable {

    /**
     * 搜索内容，支持SKU名称模糊搜索
     */
    private String content;
}
