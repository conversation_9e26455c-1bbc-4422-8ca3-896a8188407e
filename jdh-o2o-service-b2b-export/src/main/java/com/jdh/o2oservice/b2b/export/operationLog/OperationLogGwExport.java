package com.jdh.o2oservice.b2b.export.operationLog;

import com.jdh.o2oservice.b2b.common.response.PageDto;
import com.jdh.o2oservice.b2b.common.response.Response;
import com.jdh.o2oservice.b2b.export.operationLog.cmd.OperationLogCmd;
import com.jdh.o2oservice.b2b.export.operationLog.dto.B2bOperationLogDto;
import java.util.Map;

/**
 * 操作日志
 */
public interface OperationLogGwExport {

    /**
     * 分页查询操作日志
     * @param param
     * @return
     */
    Response<PageDto<B2bOperationLogDto>> queryPageOperationLog(Map<String,String> param);

    /**
     * 保存操作日志
     * @param operationLogCmd
     * @return
     */
    Response<Boolean> saveOperationLog(OperationLogCmd operationLogCmd);
}
