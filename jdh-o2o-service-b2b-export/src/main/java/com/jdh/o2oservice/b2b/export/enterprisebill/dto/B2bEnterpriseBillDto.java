package com.jdh.o2oservice.b2b.export.enterprisebill.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @ClassName:B2b企业账单
 * @Description:
 * @Author: liwenming
 * @Date: 2025/2/25 15:14
 * @Vserion: 1.0
 **/
@Data
public class B2bEnterpriseBillDto {

    /**
     * 账单Id
     */
    private Long billId;
    /**
     * 企业Id
     */
    private Long enterpriseId;

    /**
     * 账单金额
     */
    private BigDecimal billAmount;

    /**
     * 调账金额
     */
    private BigDecimal adjustAmount;

    /**
     * 调账说明
     */
    private String adjustDescribe;

    /**
     * 最终金额
     */
    private BigDecimal finalAmount;
    /**
     * 账单状态 1-待确认 2-已确认 3-已打款 4-已到账 5-已作废
     */
    private Integer billStatus;
    /**
     * 账单状态 1-待确认 2-已确认 3-已打款 4-已到账 5-已作废
     */
    private String billStatusDesc;
    /**
     * 账期
     */
    private String billDate;
    /**
     * 调账人
     */
    private String updateUser;
}
