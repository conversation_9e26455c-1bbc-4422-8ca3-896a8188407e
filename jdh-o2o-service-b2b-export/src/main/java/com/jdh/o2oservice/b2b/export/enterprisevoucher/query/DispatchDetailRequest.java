package com.jdh.o2oservice.b2b.export.enterprisevoucher.query;
import com.jdh.o2oservice.b2b.common.request.AbstractPageQuery;
import lombok.Data;
import java.io.Serializable;

/**
 * @Description //TODO
 * @Date 2025/3/5 下午4:13
 * <AUTHOR>
 **/
@Data
public class DispatchDetailRequest extends AbstractPageQuery implements Serializable {

    /**
     * 派单ID
     */
    private Long dispatchId;
    /**
     * 派单轮数
     */
    private Integer dispatchRound;
}
