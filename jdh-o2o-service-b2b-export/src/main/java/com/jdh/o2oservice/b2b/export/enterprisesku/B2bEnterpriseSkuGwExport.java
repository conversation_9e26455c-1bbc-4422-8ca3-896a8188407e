package com.jdh.o2oservice.b2b.export.enterprisesku;

import com.jdh.o2oservice.b2b.common.response.PageDto;
import com.jdh.o2oservice.b2b.common.response.Response;
import com.jdh.o2oservice.b2b.export.enterprisesku.dto.B2bEnterpriseSkuDto;

import java.util.List;
import java.util.Map;

/**
 * 企业SKU对外接口
 *
 * <AUTHOR>
 * @date 2025/03/15
 */
public interface B2bEnterpriseSkuGwExport {

    /**
     * 查询当前登录用户企业的SKU列表
     * 
     * @param param 参数，可通过content字段搜索，不传则返回所有
     * @return 企业SKU列表
     */
    Response<List<B2bEnterpriseSkuDto>> queryEnterpriseSkuList(Map<String, String> param);
    
    /**
     * 分页查询企业SKU
     * 
     * @param param 参数
     * @return 分页结果
     */
    Response<PageDto<B2bEnterpriseSkuDto>> queryPageEnterpriseSku(Map<String, String> param);
}
