package com.jdh.o2oservice.b2b.export.openapi.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/9/15
 */
@Data
public class AngelServiceRecordQuestionGroupDTO {
    /**
     * 节点名称
     */
    private String name;

    /**
     * 节点备注
     */
    private String desc;

    /**
     * 节点唯一code码
     */
    private String code;



    /**
     * 排序
     */
    private Integer sort;



    /**
     * 节点状态：0-未开始，1-进行中，2-已完成
     */
    private Integer status;

    /**
     * 题
     */
    private List<AngelServiceRecordQuestionDto> questionDTOS;


}
