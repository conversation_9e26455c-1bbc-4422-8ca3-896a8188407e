package com.jdh.o2oservice.b2b.export.enterpriseaccount.dto;
import lombok.Data;
import java.io.Serializable;
@Data
public class B2bEnterpriseAccountCreditAmountTipDto implements Serializable {

    /**
     * 提醒类型
     * 1-剩余信用额度 > 0
     * 2-剩余信用额度 ≤ 0
     */
    private Integer tipType;

    /**
     * 提示内容
     */
    private String tipContent;

    /**
     * 当企业的已使用额度达到信用额度的 80%及以上时，全局展示红色提醒
     * 1-超过80%
     * 2-未超过80%
     */
    private Integer tipRed;
}
