package com.jdh.o2oservice.b2b.export.enterprisevoucher.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/9/23
 */
@Data
public class EnterpriseVoucherExtendBDto {
    /**
     * address
     */
    private PromiseAddressBDto address;

    /**
     * patientList
     */
    private List<PromisePatientBDto> patientList;

    /**
     * service
     */
    private PromiseSkuDto service;

    /**
     * appointmentTime
     */
    private PromiseAppointmentTimeDto appointmentTime;


    /**
     * 备注
     */
    private String remark;

    /**
     * 取消完成时间
     */
    private Date finishTime;
}
