package com.jdh.o2oservice.b2b.export.enterpriseaccount.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description 企业信息
 * @Date 2025/2/24 下午8:15
 * <AUTHOR>
 **/
@Data
public class B2bEnterpriseAccountDto implements Serializable {


    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 企业名称
     */
    private String name;

    /**
     * 企业短名称
     */
    private String shortName;

    /**
     * 扩展信息
     */
    private String extend;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改人
     */
    private String updateUser;

    /**
     * 修改时间
     */
    private Date updateTime;

}
