package com.jdh.o2oservice.b2b.export.openapi.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/9/15
 */
@Data
public class AngelServiceRecordDTO {

    /**
     * 履约患者唯一ID
     */
    private Long promisePatientId;

    /**
     *  企业patientId
     */
    private String outPatientId;

    /**
     * 节点集合
     */
    private List<AngelServiceRecordQuestionGroupDTO> questionGroupDtoList;
}
