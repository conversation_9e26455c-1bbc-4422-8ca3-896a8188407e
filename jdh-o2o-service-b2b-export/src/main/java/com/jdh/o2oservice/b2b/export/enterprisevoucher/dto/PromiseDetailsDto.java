package com.jdh.o2oservice.b2b.export.enterprisevoucher.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description 履约单
 * @Date 2025/3/5 下午5:14
 * <AUTHOR>
 **/
@Data
public class PromiseDetailsDto implements Serializable {

    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 企业名称
     */
    private String enterpriseName;

    /**
     * 企业服务单id
     */
    private Long enterpriseVoucherId;

    /**
     * 服务单id
     */
    private Long voucherId;

    /**
     * 履约单id
     */
    private Long promiseId;

    /**
     * 企业服务skuId
     */
    private Long enterpriseSkuId;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * sku名称
     */
    private String skuName;

    /**
     * sku短标题
     */
    private String shortTitle;

    /**
     * 履约单状态：待接单/已接单/护士已出发/开始服务/服务完成/已取消
     */
    private Integer promiseStatus;

    /**
     * 用户pin
     */
    private String customerUserPin;

    /**
     * 企业用户id
     */
    private Long enterpriseUserId;

    /**
     * 用户名称
     */
    private String enterpriseUserName;

    /**
     * erp_pin
     */
    private String erpPin;

    /**
     * 源订单号
     */
    private String sourceOrderId;

    /**
     * 源订单平台
     */
    private String sourceOrderPlatform;

    /**
     * 预约人电话
     */
    private String appointmentPhone;

    /**
     * 预约人姓名
     */
    private String appointmentName;

    /**
     * 卡号
     */
    private String code;

    /**
     * 扩展信息
     */
    private EnterpriseVoucherExtendDto extend;

    /**
     * 完成/取消时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 履约单状态描述：待接单/已接单/护士已出发/开始服务/服务完成/已取消
     */
    private String promiseStatusName;

    /**
     * 履约单详情按钮
     */
    private PromiseDetailButtonDto promiseDetailButtonDto;

    /**
     * 允许指定意向护士 0-否 1-是
     */
    private Integer needIntendedNurse;

    /**
     * 订单备注信息
     */
    private String orderRemark;

    /**
     * 是否可修改详情
     */
    private PromiseDetailModifyDto PromiseDetailModifyDto;
}
