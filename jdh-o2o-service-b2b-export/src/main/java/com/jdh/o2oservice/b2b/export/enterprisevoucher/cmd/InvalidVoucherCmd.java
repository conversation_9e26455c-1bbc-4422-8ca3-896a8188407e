package com.jdh.o2oservice.b2b.export.enterprisevoucher.cmd;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description 作废服务单
 * @Date 2025/2/26 下午9:45
 * <AUTHOR>
 **/
@Data
public class InvalidVoucherCmd implements Serializable {

    /**
     * 1：以人维度作废；
     * 2：以服务维度作废；
     * 3：整单（履约单）作废
     */
    private Integer invalidType;

    /**
     * voucherId
     */
    private Long voucherId;

    /**
     * 原因
     */
    private String reason;

    /**
     * 操作人
     */
    private String operator;

    private Long enterpriseId;

    private Long enterpriseVoucherId;

}
