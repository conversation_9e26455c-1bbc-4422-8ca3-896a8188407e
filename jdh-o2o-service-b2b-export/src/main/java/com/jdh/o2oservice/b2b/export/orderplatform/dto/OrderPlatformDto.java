package com.jdh.o2oservice.b2b.export.orderplatform.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * OrderPlatformDto
 *
 * <AUTHOR>
 * @date 2025/02/25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderPlatformDto {

    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 源订单平台
     */
    private String sourceOrderPlatform;
}
