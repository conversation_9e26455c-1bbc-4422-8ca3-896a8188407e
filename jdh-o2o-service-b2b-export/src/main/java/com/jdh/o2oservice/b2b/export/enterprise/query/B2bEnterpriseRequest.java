package com.jdh.o2oservice.b2b.export.enterprise.query;
import com.jdh.o2oservice.b2b.common.request.AbstractQuery;
import lombok.Data;
import java.io.Serializable;

/**
 * @Description 查询企业
 * @Date 2025/2/24 下午8:16
 * <AUTHOR>
 **/
@Data
public class B2bEnterpriseRequest extends AbstractQuery implements Serializable {

    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 履约单号
     */
    private Long promiseId;
    /**
     * voucherId
     */
    private Long voucherId;
    /**
     * 来源单据类型：1:voucher, 2:账单打款
     */
    private Integer sourceReceiptType;

    /**
     * 类型：1-冻结 2-释放
     */
    private Integer freezeType;

    /**
     * 合同编号
     */
    private String contractNumber;
}
