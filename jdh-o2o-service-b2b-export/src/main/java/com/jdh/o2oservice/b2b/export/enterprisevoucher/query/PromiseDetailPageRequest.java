package com.jdh.o2oservice.b2b.export.enterprisevoucher.query;
import com.jdh.o2oservice.b2b.common.request.AbstractPageQuery;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;


/**
 * PromiseDetailPageRequest
 *
 * <AUTHOR>
 * @date 2025/03/03
 */
@Data
public class PromiseDetailPageRequest extends AbstractPageQuery implements Serializable {

    /**
     * 履约单id
     */
    private Long promiseId;

    /**
     * sku名称集合
     */
    private Set<Long> enterpriseSkuIdSet;

    /**
     * 履约状态
     */
    private Set<Integer> promiseStatusSet;

    /**
     * 创建时间开始
     */
    private Date createTimeStart;

    /**
     * 创建时间结束
     */
    private Date createTimeEnd;

    /**
     * 企业名称
     */
    private String enterpriseName;

    /**
     * 企业服务单id
     */
    private Long enterpriseVoucherId;

}
