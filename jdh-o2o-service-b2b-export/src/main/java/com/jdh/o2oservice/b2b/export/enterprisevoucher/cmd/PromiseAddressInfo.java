package com.jdh.o2oservice.b2b.export.enterprisevoucher.cmd;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * PromiseAddress
 *
 * <AUTHOR>
 * @date 2025/02/27
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PromiseAddressInfo {

    /**
     * 地址id
     */
    private String addressId;

    /**
     * 收货人省份
     */
    private Integer provinceId;

    /**
     * 收货人城市
     */
    private Integer cityId;

    /**
     * 县级地区
     */
    private Integer countyId;

    /**
     * 地区街道
     */
    private Integer townId;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 县级名称
     */
    private String countyName;

    /**
     * 乡镇街道名称
     */
    private String townName;

    /**
     * 收货人地址全称(省市县+收货人地址)
     */
    private String fullAddress;

    /**
     * 姓名
     */
    private String name;

    /**
     * 电话
     */
    private String mobile;


}
