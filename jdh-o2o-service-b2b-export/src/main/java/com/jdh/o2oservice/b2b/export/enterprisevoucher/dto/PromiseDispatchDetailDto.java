package com.jdh.o2oservice.b2b.export.enterprisevoucher.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description 运营端派单明细
 * @Date 2025/3/5 下午4:12
 * <AUTHOR>
 **/
@Data
public class PromiseDispatchDetailDto implements Serializable {

    /**
     * 派单任务明细ID
     */
    private Long dispatchDetailId;

    /**
     * 派单任务ID
     */
    private Long dispatchId;
    /**
     * 服务者ID
     */
    private Long angelId;

    /**
     * 服务者名称
     */
    private String angelName;

    /**
     * 外部服务者ID
     */
    private String outAngelId;
}
