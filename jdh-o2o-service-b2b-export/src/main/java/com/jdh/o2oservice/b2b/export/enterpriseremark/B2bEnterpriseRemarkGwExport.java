package com.jdh.o2oservice.b2b.export.enterpriseremark;

import com.jdh.o2oservice.b2b.common.response.PageDto;
import com.jdh.o2oservice.b2b.common.response.Response;
import com.jdh.o2oservice.b2b.export.enterpriseremark.dto.EnterpriseRemarkDto;

import java.util.List;
import java.util.Map;

/**
 * 企业备注对外接口
 *
 * <AUTHOR>
 * @date 2025/03/10
 */
public interface B2bEnterpriseRemarkGwExport {

    /**
     * 添加企业备注
     *
     * @param param 参数
     * @return 是否成功
     */
    Response<Boolean> addEnterpriseRemark(Map<String, String> param);

    /**
     * 查询企业备注
     *
     * @param param 参数
     * @return 企业备注
     */
    Response<EnterpriseRemarkDto> queryEnterpriseRemark(Map<String, String> param);

    /**
     * 分页查询企业备注
     *
     * @param param 参数
     * @return 分页结果
     */
    Response<PageDto<EnterpriseRemarkDto>> queryEnterpriseRemarkPage(Map<String, String> param);

    /**
     * 查询企业备注列表
     *
     * @param param 参数
     * @return 企业备注列表
     */
    Response<List<EnterpriseRemarkDto>> queryEnterpriseRemarkList(Map<String, String> param);

    /**
     * 删除企业备注
     *
     * @param param 参数
     * @return 是否成功
     */
    Response<Boolean> deleteEnterpriseRemark(Map<String, String> param);
}
