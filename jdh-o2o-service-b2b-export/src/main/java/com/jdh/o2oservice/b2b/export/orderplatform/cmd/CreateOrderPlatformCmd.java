package com.jdh.o2oservice.b2b.export.orderplatform.cmd;

import com.jdh.o2oservice.b2b.common.request.AbstractQuery;
import lombok.Data;

/**
 * CreateOrderPlatformCmd
 *
 * <AUTHOR>
 * @date 2025/02/25
 */
@Data
public class CreateOrderPlatformCmd extends AbstractQuery {

    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 源订单平台
     */
    private String sourceOrderPlatform;

    /**
     * 扩展信息
     */
    private String extend;

    /**
     * userPin
     */
    private String userPin;

}
