package com.jdh.o2oservice.b2b.export.enterprisesku.dto;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description 企业sku
 * @Date 2025/2/24 下午8:18
 * <AUTHOR>
 **/
@Data
public class B2bEnterpriseSkuDto implements Serializable {

    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 企业服务skuId
     */
    private Long enterpriseSkuId;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * sku名称
     */
    private String skuName;

    /**
     * sku短标题
     */
    private String shortTitle;

    /**
     * 渠道价
     */
    private BigDecimal channelPrice;

    /**
     * 加人加价 1-正常累加 2-单独计价
     */
    private Integer priceType;

    /**
     * 单独计价
     */
    private BigDecimal singlePrice;

    /**
     * 护士出门取消扣款比例
     */
    private BigDecimal nurseOutCancelDeduction;

    /**
     * 护士开始服务取消扣款比例
     */
    private BigDecimal nurseServedCancelDeduction;

    /**
     * 扩展信息
     */
    private String extend;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updateUser;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 合同编号
     */
    private String contractNumber;

    /**
     * 合同名称
     */
    private String contractName;

    /**
     * 合同状态 0-过期  1-有效期内
     */
    private Integer contractStatus;

    /**
     * 合同状态 0-过期  1-有效期内
     */
    private String contractStatusDesc;
}
