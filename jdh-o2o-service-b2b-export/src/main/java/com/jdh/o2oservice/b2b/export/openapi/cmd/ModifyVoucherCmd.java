package com.jdh.o2oservice.b2b.export.openapi.cmd;

import com.jdh.o2oservice.b2b.export.enterprisevoucher.cmd.PromiseAppointmentTimeInfo;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2025/9/15
 */
@Data
public class ModifyVoucherCmd {

    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 企业服务单ID
     */
    private Long enterpriseVoucherId;

    /**
     * 预约时间，修改预约时间时必传
     */
    private PromiseAppointmentTimeInfo appointmentTime;

    /**
     * 三方订单号，修改备注时必传
     */
    private String sourceOrderId;

    /**
     * 三方平台，修改备注时必传
     */
    private String sourceOrderPlatform;
}
