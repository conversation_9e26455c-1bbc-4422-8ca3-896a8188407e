package com.jdh.o2oservice.b2b.export.enterpriseremark.cmd;

import com.jdh.o2oservice.b2b.common.request.AbstractQuery;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 添加企业备注命令对象
 *
 * <AUTHOR>
 * @date 2025/03/10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AddEnterpriseRemarkCmd extends AbstractQuery implements Serializable {

    /**
     * 备注内容
     */
    private String content;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 用户PIN
     */
    private String userPin;
}
