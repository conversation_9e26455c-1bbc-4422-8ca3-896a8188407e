package com.jdh.o2oservice.b2b.export.enterprisevoucher.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description 预约人
 * @Date 2025/3/5 下午5:25
 * <AUTHOR>
 **/
@Data
public class PromisePatientDto implements Serializable {

    /**
     * 患者id
     */
    private Long patientId;

    /**
     * 用户pin
     */
    private String userPin;


    /**
     * 预约人姓名
     */
    private String name;

    /**
     * 性别【1男，2女】
     */
    private Integer gender;

    /**
     * 预约人手机号
     */
    private String phone;

    /**
     * 年龄
     */
    private Integer age;


    private String outPatientId;
}
