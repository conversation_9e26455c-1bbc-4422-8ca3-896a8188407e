package com.jdh.o2oservice.b2b.export.enterprisevoucher.cmd;
import lombok.Data;
import java.io.Serializable;

/**
 * @Description 重新派单
 * @Date 2025/3/3 上午11:33
 * <AUTHOR>
 **/
@Data
public class AfreshDispatchCmd implements Serializable {

    /**
     * 履约单ID
     */
    private Long promiseId;

    /**
     * 派单任务ID
     */
    private Long dispatchId;

    /**
     * 操作人（一般为服务者）
     */
    private String operator;

    /**
     * 1：AI 2：系统 3：医生Pin 4：运营 5：医生ID
     */
    private Integer roleType = 4;

    /**
     * 原因
     */
    private String reason;
}
