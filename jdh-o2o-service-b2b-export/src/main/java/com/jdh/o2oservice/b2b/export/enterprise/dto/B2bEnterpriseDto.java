package com.jdh.o2oservice.b2b.export.enterprise.dto;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description 企业信息
 * @Date 2025/2/24 下午8:15
 * <AUTHOR>
 **/
@Data
public class B2bEnterpriseDto implements Serializable {

    /**
     * 垂直业务身份编码
     */
    private String verticalCode;

    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 状态 1-启用 0-禁用
     */
    private Integer available;

    /**
     * 企业名称
     */
    private String name;

    /**
     * 企业短名称
     */
    private String shortName;

    /**
     * 信用额度
     */
    private BigDecimal creditAmount;

    /**
     * 允许指定意向护士 0-否 1-是
     */
    private Short needIntendedNurse;

    /**
     * 负责人
     */
    private String director;

    /**
     * 内部备注
     */
    private String internalRemark;

    /**
     * 项目资料
     */
    private String projectInformation;

    /**
     * 扩展信息
     */
    private String extend;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updateUser;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 关联服务数量
     */
    private Integer relServiceNum;

    /**
     * 冻结金额
     */
    private BigDecimal freezeAmount;

    /**
     * 剩余额度
     */
    private BigDecimal remainingAmount;

    /**
     * 展示电子护理单 0-否 1-是
     */
    private Integer needAngelServiceRecord;


    /**
     * 启用API 0-否 1-是
     */
    private Integer needApi;


    /**
     * 是否展示履约时间轴 0-否 1-是
     */
    private Integer needPromiseTimeline;

}
