package com.jdh.o2oservice.b2b.export.enterprisevoucher.cmd;

import com.jdh.o2oservice.b2b.common.request.AbstractQuery;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * SubmitPromiseCmd
 *
 * <AUTHOR>
 * @date 2025/02/27
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SubmitPromiseByUploadCmd extends AbstractQuery {

    /**
     * 企业ID
     */
    private Long enterpriseId;


    /**
     * 用户pin
     */
    private String customerUserPin;

    /**
     * 企业用户id
     */
    private Long enterpriseUserId;

    /**
     * 用户名称
     */
    private String enterpriseUserName;

    /**
     * erp_pin
     */
    private String erpPin;

    /**
     * 文件ID
     */
    private Long fileId;

}
