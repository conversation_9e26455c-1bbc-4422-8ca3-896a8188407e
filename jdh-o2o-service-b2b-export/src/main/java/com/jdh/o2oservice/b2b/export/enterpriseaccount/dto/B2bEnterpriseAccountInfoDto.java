package com.jdh.o2oservice.b2b.export.enterpriseaccount.dto;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class B2bEnterpriseAccountInfoDto implements Serializable {

    private Long id;

    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 账户Id
     */
    private Long accountId;

    /**
     * 信用额度
     */
    private BigDecimal creditAmount;

    /**
     * 冻结金额
     */
    private BigDecimal freezeAmount;

    /**
     * 扩展信息
     */
    private String extend;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 操作人
     */
    private String operator;
}
