package com.jdh.o2oservice.b2b.export.enterprisesku.cmd;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description 修改企业sku
 * @Date 2025/2/24 下午8:39
 * <AUTHOR>
 **/
@Data
public class UpdateB2bEnterpriseSkuCmd implements Serializable {

    /**
     * 企业服务skuId
     */
    private Long enterpriseSkuId;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 渠道价
     */
    private BigDecimal channelPrice;

    /**
     * 加人加价 1-正常累加 2-单独计价
     */
    private Integer priceType;

    /**
     * 单独计价
     */
    private BigDecimal singlePrice;

    /**
     * 护士出门取消扣款比例
     */
    private BigDecimal nurseOutCancelDeduction;

    /**
     * 护士开始服务取消扣款比例
     */
    private BigDecimal nurseServedCancelDeduction;

    /**
     * 合同编号
     */
    private String contractNumber;

    /**
     * 扩展信息
     */
    private String extend;

    /**
     * 操作人
     */
    private String operator;
}
