package com.jdh.o2oservice.b2b.export.enterpriseaccount;

import com.jdh.o2oservice.b2b.common.response.Response;
import com.jdh.o2oservice.b2b.export.enterprise.dto.B2bEnterpriseRelInfoDto;
import com.jdh.o2oservice.b2b.export.enterprise.query.B2bEnterpriseRequest;
import com.jdh.o2oservice.b2b.export.enterpriseaccount.dto.B2bEnterpriseAccountCreditAmountTipDto;
import com.jdh.o2oservice.b2b.export.enterpriseaccount.dto.B2bEnterpriseAcountContractDto;

import java.util.Map;


/**
 * @Description 企业账户服务
 * @Date 2025/3/11 下午3:22
 * <AUTHOR>
 **/
public interface B2bEnterpriseAccountGwExport {

    /**
     * 查询企业账户和合同信息
     * @param b2bEnterpriseRequest
     * @return
     */
    Response<B2bEnterpriseAcountContractDto> queryEnterpriseAccountAndContract(B2bEnterpriseRequest b2bEnterpriseRequest);

    /**
     * 查询信用额度提醒
     * @param param
     * @return
     */
    Response<B2bEnterpriseAccountCreditAmountTipDto> queryAccountCreditAmountTip(Map<String,String> param);

}
