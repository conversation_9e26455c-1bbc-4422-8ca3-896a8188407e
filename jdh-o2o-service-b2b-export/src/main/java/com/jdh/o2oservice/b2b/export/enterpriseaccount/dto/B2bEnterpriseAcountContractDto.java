package com.jdh.o2oservice.b2b.export.enterpriseaccount.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description 企业账户，合同信息
 * @Date 2025/2/24 下午8:15
 * <AUTHOR>
@Data
public class B2bEnterpriseAcountContractDto implements Serializable {
    /**
     * 合同ID
     */
    private Long contractId;
    /**
     * 企业ID
     */
    private Long enterpriseId;
    /**
     * 企业名称
     */
    private String enterName;
    /**
     * 合同编号
     */
    private String contractNumber;
    /**
     * 合同名称
     */
    private String contractName;
    /**
     * 服务金额
     */
    private BigDecimal serviceAmount;
    /**
     * 合同有效期
     */
    private String contractDuration;
    /**
     * 结算天数
     */
    private Integer settlementDays;
    /**
     * 结算周期描述
     */
    private String settlementPeriodDesc;

    /**
     * 合同主体
     */
    private Integer ou;
    /**
     * 有效标识：1/0
     */
    private Integer valid;
    /**
     * 有效标识：1/0
     */
    private String validDesc;
    /**
     * 合同状态
     */
    private String statusDesc;

}
