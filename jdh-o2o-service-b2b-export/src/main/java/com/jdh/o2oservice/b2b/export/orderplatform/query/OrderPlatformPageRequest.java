package com.jdh.o2oservice.b2b.export.orderplatform.query;

import com.jdh.o2oservice.b2b.common.request.AbstractQuery;
import lombok.Data;

/**
 * OrderPlatformPageRequest
 *
 * <AUTHOR>
 * @date 2025/02/25
 */
@Data
public class OrderPlatformPageRequest extends AbstractQuery {

    /** */
    private int pageNum = 1;

    /** */
    private int pageSize = 10;

    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * sourceOrderPlatform
     */
    private String sourceOrderPlatform;

    /**
     * userPin
     */
    private String userPin;
}
