package com.jdh.o2oservice.b2b.export.orderplatform;

import com.jdh.o2oservice.b2b.common.response.PageDto;
import com.jdh.o2oservice.b2b.common.response.Response;
import com.jdh.o2oservice.b2b.export.orderplatform.dto.OrderPlatformDto;

import java.util.Map;

/**
 * OrderPlatformGwExport
 *
 * <AUTHOR>
 * @date 2025/02/25
 */
public interface OrderPlatformGwExport {


    /**
     * createOrderPlatform
     *
     * @param param param
     * @return {@link Response }<{@link Boolean }>
     */
    Response<Boolean> createOrderPlatform(Map<String,String> param);


    /**
     * 分页查询订单平台
     *
     * @param param param
     * @return {@link Response }<{@link PageDto }<{@link OrderPlatformDto }>>
     */
    Response<PageDto<OrderPlatformDto>> queryOrderPlatformPage(Map<String,String> param);
}
