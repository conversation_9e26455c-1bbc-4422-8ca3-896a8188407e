package com.jdh.o2oservice.b2b.export.enterprisevoucher.cmd;
import lombok.Data;
import java.io.Serializable;

/**
 * @Description 修改预约时间
 * @Date 2025/2/26 下午8:58
 * <AUTHOR>
 **/
@Data
public class ModifyPromiseTimeCmd implements Serializable {

    /**
     * 履约单ID
     */
    private Long promiseId;

    /**
     * 提交基础信息
     */
    private PromiseAppointmentTimeInfo appointmentTime;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作角色
     */
    private Integer operatorRoleType;

}
