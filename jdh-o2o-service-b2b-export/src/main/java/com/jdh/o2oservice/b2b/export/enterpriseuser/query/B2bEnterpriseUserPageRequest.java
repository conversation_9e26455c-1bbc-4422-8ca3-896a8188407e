package com.jdh.o2oservice.b2b.export.enterpriseuser.query;
import com.jdh.o2oservice.b2b.common.request.AbstractPageQuery;
import lombok.Data;
import java.io.Serializable;

/**
 * 分页查询企业用户
 */
@Data
public class B2bEnterpriseUserPageRequest extends AbstractPageQuery implements Serializable {

    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 企业用户id
     */
    private Long enterpriseUserId;

    /**
     * 用户pin
     */
    private String userPin;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户状态 1-启用 0-禁用
     */
    private Integer available;
}
