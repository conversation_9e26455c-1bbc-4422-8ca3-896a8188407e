package com.jdh.o2oservice.b2b.export.enterprisevoucher.cmd;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * SubmitPromiseExtend
 *
 * <AUTHOR>
 * @date 2025/02/27
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SubmitPromiseExtend {

    /**
     * 时间
     */
    private PromiseAppointmentTimeInfo promiseAppointmentTime;

    /**
     * 地址
     */
    private PromiseAddressInfo promiseAddress;

    /**
     * 患者信息
     */
    private List<PromisePatientInfo> promisePatientList;

    /**
     * 意向护士
     */
    private PromiseIntendedNurseInfo intendedNurse;

    /**
     * 备注
     */
    private String remark;

}
