package com.jdh.o2oservice.b2b.export.enterprisebill.cmd;

import com.jdh.o2oservice.b2b.common.request.AbstractPageQuery;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @ClassName:B2bEnterpriseBillCmd
 * @Description:B2b企业账单Cmd
 * @Author: liwenming
 * @Date: 2025/2/25 15:14
 * @Vserion: 1.0
 **/
@Data
public class B2bEnterpriseBillCmd extends AbstractPageQuery {

    /**
     * 账单Id
     */
    private Long billId;
    /**
     * 企业Id
     */
    private Long enterpriseId;

    /**
     * 调账金额
     */
    private String adjustAmount;
    /**
     * 调账说明
     */
    private String adjustDescribe;

    /**
     * 发起调账用户pin
     */
    private String adjustUser;
    /**
     * 用户pin
     */
    private String userPin;
}
