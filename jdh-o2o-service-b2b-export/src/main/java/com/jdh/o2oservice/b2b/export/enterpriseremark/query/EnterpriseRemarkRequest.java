package com.jdh.o2oservice.b2b.export.enterpriseremark.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 企业备注请求对象
 *
 * <AUTHOR>
 * @date 2025/03/10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EnterpriseRemarkRequest implements Serializable {

    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 企业备注ID
     */
    private Long enterpriseRemarkId;
}
