package com.jdh.o2oservice.b2b.export.openapi;

import com.jdh.o2oservice.b2b.common.response.Response;
import com.jdh.o2oservice.b2b.export.enterprisesku.dto.B2bEnterpriseSkuDto;
import com.jdh.o2oservice.b2b.export.enterprisesku.query.B2bEnterpriseSkuListRequest;
import com.jdh.o2oservice.b2b.export.enterprisevoucher.cmd.InvalidVoucherCmd;
import com.jdh.o2oservice.b2b.export.enterprisevoucher.cmd.SubmitPromiseCmd;
import com.jdh.o2oservice.b2b.export.enterprisevoucher.dto.CompletePromiseDetailsDto;
import com.jdh.o2oservice.b2b.export.enterprisevoucher.dto.PromiseAvailableTimeAndStationDto;
import com.jdh.o2oservice.b2b.export.enterprisevoucher.query.AvailableTimeRequest;
import com.jdh.o2oservice.b2b.export.enterprisevoucher.query.PromiseDetailRequest;
import com.jdh.o2oservice.b2b.export.openapi.cmd.ModifyVoucherCmd;
import com.jdh.o2oservice.b2b.export.openapi.dto.*;
import com.jdh.o2oservice.b2b.export.openapi.query.AngelServiceRecordRequest;
import com.jdh.o2oservice.b2b.export.openapi.query.GetFileUrlRequest;
import com.jdh.o2oservice.b2b.export.openapi.query.QueryEnterpriseVoucherActionLog;

import java.util.List;

/**
 * @Description 开放API接口
 * @Date 2025/2/24 下午3:22
 * <AUTHOR>
 **/
public interface B2bEnterpriseOpenAPIJsfExport {

    /**
     * 提交预约
     *
     * @param cmd
     * @return
     */
    Response<Long> submitPromise(SubmitPromiseCmd cmd);

    /**
     * 查询企业sku列表
     *
     * @param request
     * @return
     */
    Response<List<B2bEnterpriseSkuDto>> queryEnterpriseSkuList(B2bEnterpriseSkuListRequest request);

    /**
     * 查询可约时间和是否可约
     *
     * @param request
     * @return
     */
    Response<PromiseAvailableTimeAndStationDto> queryAvailableTime(AvailableTimeRequest request);

    /**
     * 批量查询fileId对应URL
     *
     * @param request
     * @return
     */
    Response<List<FileUrlDto>> queryMultiFileUrl(GetFileUrlRequest request);

    /**
     * 查询企业服务单操作日志
     *
     * @param query
     * @return
     */
    Response<List<EnterpriseVoucherActionLogDto>> queryEnterpriseVoucherActionLog(QueryEnterpriseVoucherActionLog query);


    /**
     * 修改服务单信息。
     * @param modifyVoucherCmd 包含要修改的优惠券信息的命令对象。
     * @return 返回修改结果的DTO对象。
     */
    Response<ModifyVoucherResultDTO> modifyVoucher(ModifyVoucherCmd modifyVoucherCmd);


    /**
     * 作废服务单
     * @param cmd
     * @return
     */
    Response<InvalidVoucherResultDTO> invalidVoucher(InvalidVoucherCmd cmd);

    /**
     * 查询企业履约单详情
     * @param promiseDetailRequest 承诺详情请求对象，包含查询条件。
     * @return CompletePromiseDetailsDto 对象，表示查询到的承诺详情。
     */
    Response<CompletePromiseDetailsDto> queryPromiseDetail(PromiseDetailRequest promiseDetailRequest);

    /**
     * 查询电子护理单
     * @param request AngelServiceRecordRequest 对象，包含查询条件。
     * @return AngelServiceRecordDTO 列表，表示查询到的电子护理单记录。
     */
    //查询电子护理单
    Response<List<AngelServiceRecordDTO>> queryAngelServiceRecordList(AngelServiceRecordRequest request);




}
