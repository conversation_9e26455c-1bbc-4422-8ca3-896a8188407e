package com.jdh.o2oservice.b2b.export.enterpriseremark.query;

import com.jdh.o2oservice.b2b.common.request.AbstractPageQuery;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 企业备注分页请求对象
 *
 * <AUTHOR>
 * @date 2025/03/10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EnterpriseRemarkPageRequest extends AbstractPageQuery implements Serializable {

    /**
     * 用户PIN
     */
    private String userPin;
}
