package com.jdh.o2oservice.b2b.export.enterprisevoucher.cmd;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * SubmitPromiseCmd
 *
 * <AUTHOR>
 * @date 2025/02/27
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SubmitPromiseCmd {

    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 企业服务skuId
     */
    private Long enterpriseSkuId;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * sku名称
     */
    private String skuName;

    /**
     * sku短名称
     */
    private String skuShortName;

    /**
     * 用户pin
     */
    private String customerUserPin;

    /**
     * 用户pin
     */
    private String userPin;

    /**
     * 企业用户id
     */
    private Long enterpriseUserId;

    /**
     * 用户名称
     */
    private String enterpriseUserName;

    /**
     * erp_pin
     */
    private String erpPin;

    /**
     * 源订单号
     */
    private String sourceOrderId;

    /**
     * 源订单平台
     */
    private String sourceOrderPlatform;

    /**
     * 预约人电话
     */
    private String appointmentPhone;

    /**
     * 预约人姓名
     */
    private String appointmentName;

    /**
     * 卡号
     */
    private String code;

    /**
     * 扩展信息
     */
    private SubmitPromiseExtend extend;

    /**
     * 操作人
     */
    private String operator;


}
