package com.jdh.o2oservice.b2b.export.enterprisevoucher.dto;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2025/9/23
 */
@Data
public class PromiseDetailsBDto {
    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 企业名称
     */
    private String enterpriseName;

    /**
     * 企业服务单id
     */
    private Long enterpriseVoucherId;

    /**
     * 服务单id
     */
    private Long voucherId;

    /**
     * 履约单id
     */
    private Long promiseId;

    /**
     * 企业服务skuId
     */
    private Long enterpriseSkuId;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * sku名称
     */
    private String skuName;

    /**
     * sku短标题
     */
    private String shortTitle;

    /**
     * 履约单状态：待接单/已接单/护士已出发/开始服务/服务完成/已取消
     */
    private Integer promiseStatus;


    /**
     * 企业用户id
     */
    private Long enterpriseUserId;

    /**
     * 用户名称
     */
    private String enterpriseUserName;

    /**
     * erp_pin
     */
    private String erpPin;

    /**
     * 源订单号
     */
    private String sourceOrderId;

    /**
     * 源订单平台
     */
    private String sourceOrderPlatform;

    /**
     * 预约人电话
     */
    private String appointmentPhone;

    /**
     * 预约人姓名
     */
    private String appointmentName;

    /**
     * 卡号
     */
    private String code;

    /**
     * 扩展信息
     */
    private EnterpriseVoucherExtendBDto extend;


    /**
     * 履约单状态描述：待接单/已接单/护士已出发/开始服务/服务完成/已取消
     */
    private String promiseStatusName;

    /**
     * 履约单详情按钮
     */
    private PromiseDetailButtonDto promiseDetailButtonDto;


    /**
     * 是否可修改详情
     */
    private PromiseDetailModifyDto promiseDetailModifyDto;
}
