package com.jdh.o2oservice.b2b.export.enterprisevoucher.query;
import com.jdh.o2oservice.b2b.common.request.AbstractPageQuery;
import lombok.Data;
import java.io.Serializable;

/**
 * @Description 录音记录
 * @Date 2025/2/26 下午9:09
 * <AUTHOR>
 **/
@Data
public class RecordingRequest extends AbstractPageQuery implements Serializable {

    /**
     * 履约单id
     */
    private Long promiseId;

    /**
     * 通话ID，唯一确定一次通话
     */
    private String callId;
}
