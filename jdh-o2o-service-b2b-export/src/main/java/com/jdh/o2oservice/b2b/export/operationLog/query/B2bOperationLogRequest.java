package com.jdh.o2oservice.b2b.export.operationLog.query;
import com.jdh.o2oservice.b2b.common.request.AbstractPageQuery;
import lombok.Data;
import java.io.Serializable;
@Data
public class B2bOperationLogRequest extends AbstractPageQuery implements Serializable {

    /**
     * id
     */
    private Long id;

    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 操作类型 1-新增 2-删除 3-修改 4-查询 5-上传 6-下载
     */
    private Integer operateType;

    /**
     * 结果类型 0-未知 1-处理中 2-成功 3-失败 4-部分失败
     */
    private Integer resultType;

    /**
     * 操作人
     */
    private String operator;

}
