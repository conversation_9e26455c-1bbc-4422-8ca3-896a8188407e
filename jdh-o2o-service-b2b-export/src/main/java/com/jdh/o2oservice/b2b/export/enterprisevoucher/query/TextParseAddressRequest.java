package com.jdh.o2oservice.b2b.export.enterprisevoucher.query;

import com.jdh.o2oservice.b2b.common.request.AbstractQuery;
import lombok.Data;

/**
 * @ClassName TextParseAddressRequest
 * @Description
 * <AUTHOR>
 * @Date 2025/1/7 13:49
 **/
@Data
public class TextParseAddressRequest extends AbstractQuery {

    /**
     * 用户输入文本（必填）
     */
    private String text;

    /**
     * 请求坐标的经度（非必填）
     */
    private Double lat;

    /**
     * 请求坐标的纬度（非必填）
     */
    private Double lng;

    /**
     * 0:京标   1:国标  （非必填）
     */
    private int addressType;
}