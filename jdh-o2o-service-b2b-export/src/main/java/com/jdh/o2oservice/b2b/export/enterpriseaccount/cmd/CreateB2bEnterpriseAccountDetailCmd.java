package com.jdh.o2oservice.b2b.export.enterpriseaccount.cmd;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateB2bEnterpriseAccountDetailCmd implements Serializable {

    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 来源单据类型：1:voucher, 2:账单打款
     */
    private Integer sourceReceiptType;

    /**
     * 来源单据id
     */
    private Long sourceReceiptId;

    /**
     * 冻结金额
     */
    private BigDecimal freezeAmount;

    /**
     * 类型：1-冻结 2-释放
     */
    private Integer freezeType;

    /**
     * 扩展信息
     */
    private String extend;

    /**
     * 操作人
     */
    private String operator;
}
