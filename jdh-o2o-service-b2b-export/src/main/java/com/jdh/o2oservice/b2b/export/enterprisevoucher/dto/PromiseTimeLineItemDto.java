package com.jdh.o2oservice.b2b.export.enterprisevoucher.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * @Description 履约时间轴
 * @Date 2025/3/5 下午8:36
 * <AUTHOR>
 **/
@Data
public class PromiseTimeLineItemDto implements Serializable {

    /**
     * 标签
     */
    private String label;

    /**
     * 时间
     */
    private String time;

    /**
     * 内容
     */
    private String content;

    /**
     * 扩展信息
     */
    private Map<String,String> extendInfo;

    /**
     * 事件编码
     */
    private String eventCode;
}
