package com.jdh.o2oservice.b2b.export.enterprisesku.query;
import com.jdh.o2oservice.b2b.common.request.AbstractPageQuery;
import lombok.Data;
import java.io.Serializable;

/**
 * @Description 分页企业sku
 * @Date 2025/2/24 下午8:41
 * <AUTHOR>
 **/
@Data
public class B2bEnterpriseSkuPageRequest extends AbstractPageQuery implements Serializable {

    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 企业服务skuId
     */
    private Long enterpriseSkuId;

    /**
     * skuId
     */
    private Long skuId;

}
