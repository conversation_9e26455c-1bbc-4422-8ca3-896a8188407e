package com.jdh.o2oservice.b2b.export.openapi.query;

import com.jdh.o2oservice.b2b.common.request.AbstractQuery;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * Promise ID请求
 *
 * <AUTHOR>
 * @date 2024/01/05
 */
@Data
public class GetFileUrlRequest extends AbstractQuery {

    /**
     * 场景
     */
    private String scene;
    /**
     * 操作类型
     */
    private String operationType;
    /**
     * 用户pin
     */
    private String userPin;
    /**
     * 用户pin
     */
    private String taskId;

    /**
     * fileId
     */
    private Long fileId;

    /**
     * fileId
     */
    private List<Long> fileIdList;

    /**
     * 是否公开
     */
    private Boolean isPublic;

    /**
     * 过期时间
     */
    private Date expiration;

    /**
     * 操作人
     */
    private String operator;

}
