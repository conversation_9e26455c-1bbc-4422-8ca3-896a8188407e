package com.jdh.o2oservice.b2b.export.enterpriseuser.cmd;
import lombok.Data;
import java.io.Serializable;

/**
 * 更新企业用户
 */
@Data
public class UpdateB2bEnterpriseUserCmd implements Serializable {

    /**
     * 企业用户id
     */
    private Long enterpriseUserId;

    /**
     * 用户pin
     */
    private String userPin;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户角色 1-管理员
     */
    private Integer userRole;

    /**
     * 扩展信息
     */
    private String extend;

    /**
     * 操作人
     */
    private String operator;
}
