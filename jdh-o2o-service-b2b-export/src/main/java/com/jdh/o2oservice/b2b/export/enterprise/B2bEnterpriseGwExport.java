package com.jdh.o2oservice.b2b.export.enterprise;
import com.jdh.o2oservice.b2b.common.response.Response;
import com.jdh.o2oservice.b2b.export.enterprise.dto.B2bEnterpriseDto;
import com.jdh.o2oservice.b2b.export.enterprise.dto.B2bEnterpriseEscrowDto;
import com.jdh.o2oservice.b2b.export.enterprise.dto.B2bEnterpriseRelInfoDto;
import com.jdh.o2oservice.b2b.export.enterprise.query.B2bEnterpriseRequest;

import java.util.Map;

/**
 * @Description 企业信息服务
 * @Date 2025/2/24 下午3:22
 * <AUTHOR>
 **/
public interface B2bEnterpriseGwExport {

    /**
     * 查询企业关联信息
     * @param param
     * @return
     */
    Response<B2bEnterpriseRelInfoDto> queryEnterpriseRelInfo(Map<String,String> param);

    /**
     * 查询企业
     * @param request
     * @return
     */
    Response<B2bEnterpriseDto> queryEnterprise(B2bEnterpriseRequest request);
    /**
     * 查询企业
     * @param request
     * @return
     */
    Response<B2bEnterpriseDto> queryEnterpriseByPromiseId(B2bEnterpriseRequest request);
    /**
     * 企业代管
     * @param param
     * @return
     */
    Response<B2bEnterpriseEscrowDto> enterpriseEscrow(Map<String,String> param);
}
