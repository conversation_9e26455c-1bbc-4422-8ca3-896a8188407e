package com.jdh.o2oservice.b2b.export.enterprise.cmd;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description 修改企业
 * @Date 2025/2/24 下午8:35
 * <AUTHOR>
 **/
@Data
public class UpdateB2bEnterpriseCmd implements Serializable {

    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 企业名称
     */
    private String name;

    /**
     * 企业短名称
     */
    private String shortName;

    /**
     * 信用额度
     */
    private BigDecimal creditAmount;

    /**
     * 允许指定意向护士 0-否 1-是
     */
    private Integer needIntendedNurse;

    /**
     * 负责人
     */
    private String director;

    /**
     * 内部备注
     */
    private String internalRemark;

    /**
     * 项目资料
     */
    private String projectInformation;

    /**
     * 扩展信息
     */
    private String extend;

    /**
     * 操作人
     */
    private String operator;


    /**
     * 展示电子护理单 0-否 1-是
     */
    private Integer needAngelServiceRecord;


    /**
     * 启用API 0-否 1-是
     */
    private Integer needApi;


    /**
     * 是否展示履约时间轴 0-否 1-是
     */
    private Integer needPromiseTimeline;
}
