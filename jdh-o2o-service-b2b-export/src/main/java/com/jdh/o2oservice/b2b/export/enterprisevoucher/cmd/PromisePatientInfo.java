package com.jdh.o2oservice.b2b.export.enterprisevoucher.cmd;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PromisePatientInfo {

    /**
     * 患者id
     */
    private Long patientId;

    /**
     * 用户pin
     */
    private String userPin;


    /**
     * 预约人姓名
     */
    private String name;

    /**
     * 性别【1男，2女】
     */
    private Integer gender;

    /**
     * 预约人手机号
     */
    private String phone;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 外部患者id
     */
    private String outPatientId;
}
