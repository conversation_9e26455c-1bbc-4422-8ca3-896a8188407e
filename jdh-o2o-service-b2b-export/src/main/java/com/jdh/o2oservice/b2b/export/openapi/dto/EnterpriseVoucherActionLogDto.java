package com.jdh.o2oservice.b2b.export.openapi.dto;

import lombok.Data;

import java.util.Date;

/**
 * 文件预签名Dto
 * @author: yang<PERSON><PERSON>
 * @date: 2024/3/20 4:47 下午
 * @version: 1.0
 */
@Data
public class EnterpriseVoucherActionLogDto {
    /**
     * 操作时间
     */
    private Date actionTime;

    /**
     * 操作人
     */
    private String actionUser;

    /**
     * 操作类型编码
     */
    private String actionTypeCode;

    /**
     * 操作类型
     */
    private String actionType;

    /**
     * 操作详情
     */
    private String actionDesc;

}
