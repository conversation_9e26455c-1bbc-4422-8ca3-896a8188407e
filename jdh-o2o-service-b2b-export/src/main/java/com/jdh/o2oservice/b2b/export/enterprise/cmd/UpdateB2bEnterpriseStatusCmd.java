package com.jdh.o2oservice.b2b.export.enterprise.cmd;
import lombok.Data;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Description 修改企业状态
 * @Date 2025/2/24 下午8:36
 * <AUTHOR>
 **/
@Data
public class UpdateB2bEnterpriseStatusCmd implements Serializable {

    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 状态 1-启用 0-禁用
     */
    private Integer available;

    /**
     * 操作人
     */
    private String operator;
}
