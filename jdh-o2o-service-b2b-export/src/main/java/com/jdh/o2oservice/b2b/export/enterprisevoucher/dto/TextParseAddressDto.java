package com.jdh.o2oservice.b2b.export.enterprisevoucher.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName TextParseAddressDto
 * @Description
 * <AUTHOR>
 * @Date 2025/1/7 13:46
 **/
@Data
public class TextParseAddressDto implements Serializable {

    /**
     * 待解析文本
     */
    private String text;

    /**
     * 用户姓名
     */
    private String name;

    /**
     * 电话（座机/手机），优先手机，再是座机
     */
    private String phone;

    /**
     * 手机
     */
    private String mobilePhone;

    /**
     * 座机
     */
    private String telphone;

    /**
     * 地址相关的信息
     */
    private TextParseAddressDetailDto addressInfo;
}