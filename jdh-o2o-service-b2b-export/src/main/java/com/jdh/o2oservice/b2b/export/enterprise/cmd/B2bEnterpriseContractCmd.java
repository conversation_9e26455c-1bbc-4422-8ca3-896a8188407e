package com.jdh.o2oservice.b2b.export.enterprise.cmd;

import com.jdh.o2oservice.b2b.common.request.AbstractQuery;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description 查询企业
 * @Date 2025/2/24 下午8:16
 * <AUTHOR>
 **/
@Data
public class B2bEnterpriseContractCmd extends AbstractQuery implements Serializable {
    /**
     * 企业ID
     */
    private Long enterpriseId;
    /**
     * 企业名称
     */
    private String enterName;
    /**
     * 合同ID
     */
    private Long contractId;
    /**
     * 合同编号
     */
    private String contractNumber;
    /**
     * 合同名称
     */
    private String name;
    /**
     * 合同主体
     */
    private Integer ou;
    /**
     * 有效标识：1/0
     */
    private Integer valid;
    /**
     * 合同开始时间
     */
    private String startTime;
    /**
     * 合同结束时间
     */
    private String endTime;
    /**
     * 合同状态
     */
    private Integer status;
    /**
     * 结算周期:1-日 2-月 3-季度 4-年 5-周 6-半月 7-其他时间
     */
    private Integer settlementPeriod;
    /**
     * 结算天数
     */
    private Integer settlementDays;
    /**
     * 操作人
     */
    private String operator;
}
