package com.jdh.o2oservice.b2b.export.enterprisevoucher.dto;
import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * @Description 履约详情
 * @Date 2025/2/26 下午9:49
 * <AUTHOR>
 **/
@Data
public class CompletePromiseDetailsDto implements Serializable {

    /**
     * 企业订单号
     */
    private Long enterpriseVoucherId;

    /**
     * 履约单
     */
    private PromiseDetailsDto promiseDetailsDto;

    /**
     * angel信息
     */
    private PromiseAngelDto angelDto;

    /**
     * 服务记录
     */
    private PromiseTagDto tagInfo;

    /**
     * 履约时间轴
     */
    private List<PromiseTimeLineItemDto> promiseTimeLine;

}
