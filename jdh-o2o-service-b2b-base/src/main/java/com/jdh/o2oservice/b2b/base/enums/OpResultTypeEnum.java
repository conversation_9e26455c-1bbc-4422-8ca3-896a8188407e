package com.jdh.o2oservice.b2b.base.enums;

import lombok.Getter;

@Getter
public enum OpResultTypeEnum {

    UNKNOWN(0, "未知"),

    ADD(1, "处理中"),

    DEL(2, "成功"),

    UPDATE(3, "失败"),

    QUERY(4, "部分失败");

    /**
     * 类型
     */
    private final Integer type;

    /**
     * 描述
     */
    private final String desc;

    OpResultTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static String getDescOfType(Integer type){
        if (type == null){
            return "";
        }
        for (OpResultTypeEnum opResultTypeEnum:OpResultTypeEnum.values()){
            if(opResultTypeEnum.getType().equals(type)){
                return opResultTypeEnum.getDesc();
            }
        }
        return "";
    }

}
