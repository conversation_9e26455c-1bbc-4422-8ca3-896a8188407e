package com.jdh.o2oservice.b2b.base.util;

import com.alibaba.excel.converters.string.StringNumberConverter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.alibaba.excel.util.DateUtils;
import com.alibaba.excel.util.NumberDataFormatterUtils;
import com.alibaba.excel.util.NumberUtils;
import com.alibaba.excel.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

/**
 * MyStringNumberConverter
 *
 * <AUTHOR>
 * @version 2025/06/06 15:35
 **/
public class MyStringNumberConverter extends StringNumberConverter {

    private static final String timePattern = "h:mm:ss";

    private static final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(timePattern);


    @Override
    public String convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        // If there are "DateTimeFormat", read as date
        if (contentProperty != null && contentProperty.getDateTimeFormatProperty() != null) {
            return DateUtils.format(cellData.getNumberValue(), contentProperty.getDateTimeFormatProperty().getUse1904windowing(), contentProperty.getDateTimeFormatProperty().getFormat());
        }
        // If there are "NumberFormat", read as number
        if (contentProperty != null && contentProperty.getNumberFormatProperty() != null) {
            return NumberUtils.format(cellData.getNumberValue(), contentProperty);
        }
        // Excel defines formatting
        boolean hasDataFormatData = cellData.getDataFormatData() != null && cellData.getDataFormatData().getIndex() != null && !StringUtils.isEmpty(cellData.getDataFormatData().getFormat());

        if (hasDataFormatData) {
            if (Objects.equals(cellData.getDataFormatData().getFormat(), timePattern)) {
                LocalDateTime localDateTime = DateUtils.getLocalDateTime(cellData.getNumberValue().doubleValue(), false);
                return dateTimeFormatter.format(localDateTime);
            }
            return NumberDataFormatterUtils.format(cellData.getNumberValue(), cellData.getDataFormatData().getIndex(), cellData.getDataFormatData().getFormat(), globalConfiguration);
        }
        // Default conversion number
        return NumberUtils.format(cellData.getNumberValue(), contentProperty);
    }
}