package com.jdh.o2oservice.b2b.base.util;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.jdh.o2oservice.b2b.base.exception.SystemErrorCode;
import com.jdh.o2oservice.b2b.base.exception.SystemException;
import org.apache.http.HttpEntity;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpRequestRetryHandler;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.*;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.DefaultHttpRequestRetryHandler;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.AbstractHttpMessage;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLSession;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * httpclient
 * <AUTHOR> yangxiyu
 * @date : 2024/04/22 20:42
 */
public class O2oHttpClient {

    /**
     *
     */
    private static final CloseableHttpClient HTTP_CLIENT;
    /**
     *
     */
    private static final PoolingHttpClientConnectionManager CM = new PoolingHttpClientConnectionManager();
    /**
     *
     */
    private static final Logger logger = LoggerFactory.getLogger(O2oHttpClient.class);

    /** 请求执行超时警告阈值 */
    private static final int WARNING_TIMES = 500;
    /** 最小的响应成功状态码 */
    private static final int SUCCESS_CODE_MIN = 200;
    /** 最大的响应成功状态码 */
    private static final int SUCCESS_CODE_MAX = 299;


    /**
     *
     */
    public O2oHttpClient() {
    }


    /**
     * get调用
     */
    public static String get(String url) {
        HttpGet httpGet = new HttpGet(url);
        buildHeader(httpGet, getJsonHeader());
        return execute(httpGet);
    }

    /**
     * get调用
     */
    public static String get(String url, Map<String, String> headers) {
        HttpGet httpGet = new HttpGet(url);
        buildHeader(httpGet, headers);
        return execute(httpGet);
    }
    /**
     * json类型的post调用
     */
    public static String postJson(String url, String reqStr) {
        HttpPost httpPatch = new HttpPost(url);
        buildHeader(httpPatch, getJsonHeader());
        httpPatch.setEntity(new StringEntity(reqStr, "UTF-8"));
        return execute(httpPatch);

    }

    /**
     * post调用
     * @param url
     * @param headers
     * @param reqStr
     * @return
     */
    public static String postJson(String url, Map<String, String> headers, String reqStr) {
        HttpPost httpPatch = new HttpPost(url);
        buildHeader(httpPatch, headers);
        httpPatch.setEntity(new StringEntity(reqStr, "UTF-8"));
        return execute(httpPatch);
    }
    
    /**
     * post调用
     * @param url
     * @param headers
     * @param reqStr
     * @return
     */
    public static String postJson(String url, Map<String, String> headers, String reqStr, RequestConfig requestConfig) {
        HttpPost httpPatch = new HttpPost(url);
        httpPatch.setConfig(requestConfig);
        buildHeader(httpPatch, headers);
        httpPatch.setEntity(new StringEntity(reqStr, "UTF-8"));
        return execute(httpPatch);
    }

    /**
     * json类型的post调用
     */
    public static String putJson(String url, String reqStr) {
        HttpPut httpPut = new HttpPut(url);
        buildHeader(httpPut, getJsonHeader());
        httpPut.setEntity(new StringEntity(reqStr, "UTF-8"));
        return execute(httpPut);

    }


    /**
     * json类型的post调用
     */
    public static String putJson(String url, Map<String, String> headers, String reqStr) {
        HttpPut httpPut = new HttpPut(url);
        buildHeader(httpPut, headers);
        httpPut.setEntity(new StringEntity(reqStr, "UTF-8"));
        return execute(httpPut);
    }

    /**
     * 执行http调用
     */
    private static String execute(HttpUriRequest request) {
        CloseableHttpResponse response = null;
        String var12;
        try{
            LocalDateTime startTime = LocalDateTime.now();

            logger.info("O2oHttpClient --> simplePost execute request={}", JSON.toJSONString(request));
            response = HTTP_CLIENT.execute(request);
            LocalDateTime endTime = LocalDateTime.now();
            Duration duration = Duration.between(startTime,endTime);
            long executeTime = duration.toMillis();
            if (executeTime > WARNING_TIMES) {
                String startStr = TimeUtils.localDateTimeToStr(startTime);
                String endStr = TimeUtils.localDateTimeToStr(startTime);
                logger.warn("O2oHttpClient --> simplePost request return, begin time:{}, cost time {}ms exceed warning threshold value {}ms, {}"
                        ,startStr, executeTime,  endStr, request.getURI());
            }

            int status = response.getStatusLine().getStatusCode();
            if (status < SUCCESS_CODE_MIN || status > SUCCESS_CODE_MAX) {
                throw new ClientProtocolException("Unexpected response status: " + status);
            }

            HttpEntity entity = response.getEntity();
            var12 = entity != null ? EntityUtils.toString(entity, "UTF-8") : null;
        }catch (IOException var17) {
            logger.error("O2oHttpClient --> IOException", var17);
            throw new SystemException(SystemErrorCode.IO_EXCEPTION);
        } catch (Exception var18) {
            logger.error("O2oHttpClient --> simplePost 未知异常", var18);
            throw new SystemException(SystemErrorCode.UNKNOWN_ERROR);
        } finally {
            closeResponse(response);
        }
        return var12;
    }
    /**
     * 构建header
     * @param httpMessage
     * @param headerMap
     */
    private static void buildHeader(AbstractHttpMessage httpMessage, Map<String, String> headerMap){
        if (headerMap != null && headerMap.size() > 0) {
            Iterator<String> iterator = headerMap.keySet().iterator();

            while(iterator.hasNext()) {
                String key = iterator.next();
                httpMessage.setHeader(key, headerMap.get(key));
            }
        }
    }

    /**
     * 封装jsonHeader
     * @return
     */
    private static Map<String, String> getJsonHeader(){
        Map<String, String> headerMap = Maps.newHashMap();
        headerMap.put("Content-Type", "application/json");
        return headerMap;
    }


    /**
     * @param response
     */
    private static void closeResponse(CloseableHttpResponse response) {
        if (response != null) {
            try {
                response.close();
            } catch (IOException var2) {
                logger.error("closeResponse execute error!!");
            }
        }

    }

    static {
        CM.setDefaultMaxPerRoute(30);
        CM.setMaxTotal(100);
        CM.setValidateAfterInactivity(5000);
        // 重试配置
        HttpRequestRetryHandler handler = new DefaultHttpRequestRetryHandler();
        /**
         * 默认的requestConfig,每个请求可以单独配置，公用的RequestConfig可以减少RequestConfig对象的创建。
         * 公用requestConfig劣势：公用RequestConfig就只能配置最大的超时时间，举例配置10S，假如大部分请求都能在1S内完成响应；
         * 1S完成响应是这些响应方的正常阈值，当某一天因为网络或者系统故障，导致请求全部超时；本来1S就应该中断请求的，现在需要10S，
         * 大量的请求堆积，会产生雪崩，影响系统稳定性。
         * 总结：默认的RequestConfig超时时间不宜过长，满足正常的大部分请求就行，当连接需要更小或更大的超时配置时，需要自定义。
         */
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(3000)
                .setSocketTimeout(5000)
                //从连接池中取的连接的最长时间
                .setConnectionRequestTimeout(5000)
                .setRedirectsEnabled(true).build();


        HttpClientBuilder builder = HttpClients.custom()
                .setConnectionManager(CM)
                .setDefaultRequestConfig(requestConfig)
                .evictExpiredConnections()
                .evictIdleConnections(50L, TimeUnit.SECONDS)
                .disableAutomaticRetries()
                .setRetryHandler(handler);

        HTTP_CLIENT = builder.build();
    }

    /**
     * DO_NOT_VERIFY
     */
    final static HostnameVerifier DO_NOT_VERIFY = new HostnameVerifier() {
        @Override
        public boolean verify(String hostname, SSLSession session) {
            return true;
        }
    };


    /**
     * 从输入流中获取字节数组
     * @param inputStream
     * @return
     * @throws IOException
     */
    public static byte[] readInputStream(InputStream inputStream) throws IOException {
        byte[] buffer = new byte[1024];
        int len = 0;
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        while((len = inputStream.read(buffer)) != -1) {
            bos.write(buffer, 0, len);
        }
        bos.close();
        return bos.toByteArray();
    }


}
