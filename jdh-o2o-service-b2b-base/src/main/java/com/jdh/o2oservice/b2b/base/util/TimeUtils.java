package com.jdh.o2oservice.b2b.base.util;

import cn.hutool.core.date.DateUtil;
import com.jdh.o2oservice.b2b.base.constant.CommonConstant;
import com.jdh.o2oservice.b2b.base.enums.TimeRangeEnum;

import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020-08-06 18:00
 * @description 时间工具类
 */
public class TimeUtils {
    /**
     *
     */
    private TimeUtils() {

    }

    /**
     * 获取默认时间格式: yyyy-MM-dd HH:mm:ss
     */
    private static final DateTimeFormatter DEFAULT_DATETIME_FORMATTER = TimeFormat.LONG_PATTERN_LINE.formatter;

    /**
     * 获取默认时间格式: yyyy-MM-dd HH:mm
     */
    private static final DateTimeFormatter DEFAULT_LONG_PATTERN_LINE_NO_S_FORMATTER = TimeFormat.LONG_PATTERN_LINE_NO_S.formatter;


    /**
     * 获取默认时间格式: yyyy-MM-dd
     */
    private static final DateTimeFormatter DEFAULT_DATE_FORMATTER = TimeFormat.SHORT_PATTERN_LINE.formatter;

    /**
     * 获取默认时间格式: yyyy-MM-dd HH:mm:ss
     */
    private static final DateTimeFormatter DEFAULT_DATETIME_WITH_MSES_FORMATTER = TimeFormat.LONG_PATTERN_WITH_MILSEC_LINE.formatter;

    /**
     * 获取当前时间
     *
     * @return
     */
    public static LocalDateTime getCurrentLocalDateTime() {
        return LocalDateTime.now();
    }

    /**
     * @return
     */
    public static String getCurrentDateTime() {
        return DEFAULT_DATETIME_FORMATTER.format(getCurrentLocalDateTime());
    }

    public static String getDateTimeStr(Date date) {
        return DEFAULT_LONG_PATTERN_LINE_NO_S_FORMATTER.format(dateToLocalDateTime(date));
    }

    /**
     * @return
     */
    public static String getCurrentDateTimeWithMses() {
        return DEFAULT_DATETIME_WITH_MSES_FORMATTER.format(getCurrentLocalDateTime());
    }

    /**
     * @return
     */
    public static LocalDate getCurrentLocalDate() {
        return LocalDate.now();
    }



    /**
     * @return
     */
    public static String getCurrentDate() {
        return DEFAULT_DATE_FORMATTER.format(getCurrentLocalDate());
    }

    /**
     * LocalDateTime Date 相互转换
     *
     * @param date
     * @return
     */
    public static LocalDateTime dateToLocalDateTime(Date date) {
        Instant instant = date.toInstant();
        ZoneId zone = ZoneId.systemDefault();
        return LocalDateTime.ofInstant(instant, zone);
    }

    /**
     * @param date
     * @return
     */
    public static LocalDate dateToLocalDate(Date date) {
        return dateToLocalDateTime(date).toLocalDate();
    }

    /**
     * @return
     */
    public static Date localDateTimeToDate() {
        LocalDateTime localDateTime = getCurrentLocalDateTime();
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDateTime.atZone(zone).toInstant();
        return Date.from(instant);
    }

    /**
     * @param localDateTime
     * @return
     */
    public static Date localDateTimeToDate(LocalDateTime localDateTime) {
        if(Objects.isNull(localDateTime)){
            return null;
        }
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDateTime.atZone(zone).toInstant();
        return Date.from(instant);
    }

    /**
     * @return
     */
    public static Date localDateToDate() {
        LocalDate localDate = getCurrentLocalDate();
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDate.atStartOfDay().atZone(zone).toInstant();
        return Date.from(instant);
    }

    /**
     * @param localDate
     * @return
     */
    public static Date localDateToDate(LocalDate localDate) {
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDate.atStartOfDay().atZone(zone).toInstant();
        return Date.from(instant);
    }

    /**
     * @param time
     * @return
     */
    public static String localDateTimeToStr(LocalDateTime time) {
        return DEFAULT_DATETIME_FORMATTER.format(time);
    }

    /**
     * @param time
     * @param format
     * @return
     */
    public static String localDateTimeToStr(LocalDateTime time, TimeFormat format) {
        return format.formatter.format(time);
    }

    public static String getAmPm(LocalDateTime dateTime){
        Date date = TimeUtils.localDateTimeToDate(dateTime);
        return getAmPm(date);
    }

    public static String getAmPm(Date date){
        //创建一个calendar对象，默认一个时间
        Calendar calendar = Calendar.getInstance();
        // 把time赋值给calendar
        calendar.setTime(date);
        //区分是上午还是下午  index 0为上午 1为下午
        int index = calendar.get(Calendar.AM_PM);
        if (0 == index) {
            return TimeRangeEnum.AM.getLabel();
        } else if (1 == index) {
            return TimeRangeEnum.PM.getLabel();
        }
        return null;
    }

    /**
     * @param time
     * @return
     */
    public static String localDateToStr(LocalDate time) {
        return DEFAULT_DATE_FORMATTER.format(time);
    }

    /**
     * @param date
     * @return
     */
    public static String localDateToStr(LocalDate date, TimeFormat format) {
        return format.formatter.format(date);
    }


    /**
     * @param data
     * @param isEpochMilli
     * @return
     */
    public static long dataToTimestamp(Date data, boolean isEpochMilli) {
        Instant instant = data.toInstant();
        if (isEpochMilli) {
            return instant.toEpochMilli();
        } else {
            return instant.getEpochSecond();
        }
    }

    /**
     * @param date
     * @param amount
     * @return
     */
    public static Date addDays(Date date, int amount) {
        return add(date, 5, amount);
    }

    /**
     * @param date
     * @param calendarField
     * @param amount
     * @return
     */
    public static Date add(Date date, int calendarField, int amount) {
        if (date == null) {
            throw new IllegalArgumentException("The date must not be null");
        } else {
            Calendar c = Calendar.getInstance();
            c.setTime(date);
            c.add(calendarField, amount);
            return c.getTime();
        }
    }

    /**
     *
     * @param date
     * @param format
     * @return
     */
    public static String dateTimeToStr(Date date, TimeFormat format) {
        if (date == null) {
            return "";
        }
        return dateToLocalDateTime(date).format(format.formatter);
    }

    /**
     * Date转时间字符串
     * @param date
     * @return
     */
    public static String dateTimeToStr(Date date) {
        if (date == null) {
            return "";
        }
        return dateToLocalDateTime(date).format(DEFAULT_DATETIME_FORMATTER);
    }

    /**
     * 获取几年后时间
     * @param date date
     * @param number number
     * @return
     */
    public static Date getPlusNumberYear(Date date, int number) {
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(date.getTime()), ZoneId.systemDefault());
        LocalDateTime startOfDay = localDateTime.plusYears(number);
        return Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取某天快结束的时间戳
     * date值为 2022-02-01 12:23:91 返回的值为 2022-02-01 23:59:59
     * @param date
     * @return
     */
    public static Date getDateEnding(Date date){
        LocalDate localDate = dateToLocalDate(date);
        LocalDateTime localDateTime = localDate.atTime(23, 59, 59);
        return localDateTimeToDate(localDateTime);
    }
    
    /**
     * 获取某天开始的时间戳
     * date值为 2022-02-01 12:23:91 返回的值为 2022-02-01 00:00:00
     * @param date
     * @return
     */
    public static Date getDateStart(Date date){
        LocalDate localDate = dateToLocalDate(date);
        LocalDateTime localDateTime = localDate.atTime(00, 00, 00);
        return localDateTimeToDate(localDateTime);
    }
    
    /**
     * 今天天0点
     * @return
     */
    public static Date getinitDateStart(){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY,0);
        calendar.set(Calendar.MINUTE,0);
        calendar.set(Calendar.SECOND,0);
        calendar.set(Calendar.MILLISECOND,0);
        return calendar.getTime();
    }
    
    /**
     * 昨天0点
     * @return
     */
    public static Date getYesterdayDateStart(){
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DATE,calendar.get(Calendar.DAY_OF_MONTH)-1);
        calendar.set(Calendar.HOUR_OF_DAY,0);
        calendar.set(Calendar.MINUTE,0);
        calendar.set(Calendar.SECOND,0);
        calendar.set(Calendar.MILLISECOND,0);
        return calendar.getTime();
    }

    /**
     * 注意
     * @param str
     * @param format
     * @return
     */
    public static Date timeStrToDate(String str, TimeFormat format) {
        LocalDateTime myDate = LocalDateTime.parse(str, format.formatter);
        return localDateTimeToDate(myDate);
    }

      /** 字符串转Date
       * @param str
       * @return
       */
    public static Date strToDate(String str) {
        LocalDate myDate = LocalDate.parse(str, DEFAULT_DATE_FORMATTER);
        return localDateToDate(myDate);
    }

    /**
     * 获取开始时间 后面加 00:00:00
     * @param date
     * @return
     */
    public static Date getStartTime(Date date) {
        if (null == date) {
            return null;
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
        try {
            return simpleDateFormat.parse(simpleDateFormat.format(date));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取开始时间 后面加 23:59:59
     *
     * @param date
     * @return
     */
    public static Date getEndTime(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, CommonConstant.ZERO);
        return calendar.getTime();
    }

    /**
     * 传入一个日期，判断时分是否在时间段范围内
     * @param time
     * @param timeRange
     * @param splitStr
     * @return
     */
    public static Boolean dateBetweenTime(Date time,String timeRange,String splitStr){
        String[] split = timeRange.split(splitStr);
        String startTimeStr = split[0];
        String endTimeStr = split[1];
        // 获取当前日期的年月日
        // 获取time的时分部分，并构建为今天的Date对象
        Date timeOnly = DateUtil.parse(DateUtil.format(time, "HH:mm"), "HH:mm");
        // 使用今天的日期构建开始时间和结束时间的Date对象
        Date startTime = DateUtil.parse(startTimeStr, "HH:mm");
        Date endTime = DateUtil.parse(endTimeStr, "HH:mm");
        // 比较时分
        return !timeOnly.before(startTime) && !timeOnly.after(endTime);
    }

    /**
     * 根据日期获取weekName
     * @param localDate
     * @return
     */
    public static String getWeekName(LocalDate localDate){
        DayOfWeek week = localDate.getDayOfWeek();
        switch (week){
            case MONDAY:
                return "周一";
            case TUESDAY:
                return "周二";
            case WEDNESDAY:
                return "周三";
            case THURSDAY:
                return "周四";
            case FRIDAY:
                return "周五";
            case SATURDAY:
                return "周六";
            case SUNDAY:
                return "周日";
            default:
                return null;
        }
    }
    public static List<String> getDateRange(Date startDate, Date endDate) {
        List<String> dateList = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        while (!calendar.getTime().after(endDate)) {
            dateList.add(sdf.format(calendar.getTime()));
            calendar.add(Calendar.DATE, 1);
        }

        return dateList;
    }

    /**
     * 向上取整点
     * @param date
     * @return
     */
        public static Date roundUpToHour(Date date) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);

            // 将分钟和秒数设为0
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);

            return calendar.getTime();
        }

    /**
     * 判断两个时段是否有交集
     * @param sourceStartTime
     * @param sourceEndTime
     * @param destStartTime
     * @param destEndTime
     * @return
     */
    public static boolean hasOverlap(LocalDateTime sourceStartTime, LocalDateTime sourceEndTime, LocalDateTime destStartTime, LocalDateTime destEndTime){
        return !(sourceEndTime.isBefore(destStartTime) || sourceEndTime.isEqual(destStartTime) || sourceStartTime.isAfter(destEndTime) || sourceStartTime.isEqual(destEndTime));
    }

    /**
     * 给定Date类型 要求向上取整秒 比如1718791200018 转化为Date类型的1718791200000
     * @param date
     * @return
     */
    public static Date roundUpToSecond(Date date) {
        long timeInMillis = date.getTime();
        long roundedTime = (timeInMillis / 1000) * 1000; // 向下取整到秒
        if (timeInMillis % 1000 != 0) {
            roundedTime += 1000; // 向上取整到秒
        }
        return new Date(roundedTime);
    }
    public static Date floorDateToMinute(Date date) {
        long timeInMillis = date.getTime();
        long roundedTimeInMillis = timeInMillis / 60000 * 60000;
        return new Date(roundedTimeInMillis);
    }

    public static String getCurrTimeInterval(){
        LocalTime nowTime = LocalTime.now();
        return MessageFormat.format("{0}-{1}",
                TimeFormat.DATE_PATTERN_HM_SIMPLE.formatter.format(nowTime),
                TimeFormat.DATE_PATTERN_HM_SIMPLE.formatter.format(nowTime.plusHours(CommonConstant.ONE)));
    }


}




