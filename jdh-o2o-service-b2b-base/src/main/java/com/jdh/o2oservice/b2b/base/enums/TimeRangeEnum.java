package com.jdh.o2oservice.b2b.base.enums;

import java.time.LocalTime;

public enum TimeRangeEnum {


    /**上午  */
    AM("上午"),
    /** 下午*/
    PM("下午"),
    ;

    /**
     * 上午默认时间
     */
    public static final LocalTime AM_TIME = LocalTime.of(9,0);
    /**
     * 下午默认时间
     */
    public static final LocalTime PM_TIME = LocalTime.of(14,0);
    private String label;

    TimeRangeEnum(String label) {
        this.label = label;
    }

    public String getLabel() {
        return label;
    }

    public static TimeRangeEnum matchTimeRange(String name){
        if(null == name || "".equals(name)){
            return null;
        }
        for (TimeRangeEnum value : TimeRangeEnum.values()) {
            if (name.equals(value.name())) {
                return value;
            }
        }
        return null;
    }
}