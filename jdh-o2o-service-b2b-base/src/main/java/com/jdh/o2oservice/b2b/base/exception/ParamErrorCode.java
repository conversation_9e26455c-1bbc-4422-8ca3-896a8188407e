package com.jdh.o2oservice.b2b.base.exception;

import lombok.ToString;

/**
 * ParamErrorCode
 *
 * <AUTHOR>
 * @date 2023/12/11
 */
@ToString
public enum ParamErrorCode implements AbstractErrorCode {

    /**
     * 成功
     */
    PARAM_IS_NULL("P0000", "参数为null"),
    APPOINT_ITEM_IS_NULL("P0001", "请选择预约项目"),
    APPOINT_ADDRESS_IS_NULL("P0002", "请选择预约地址"),
    APPOINT_NAME_IS_NULL("P0003", "请填写联系人"),
    APPOINT_MOBILE_NULL("P0004", "请填写手机号"),
    APPOINT_TIME_NULL("P0005", "请选择上门时间"),
    APPOINT_PATIENT_NULL("P0006", "至少填写一名被服务人"),
    APPOINT_PATIENT_NAME_NULL("P0007", "请填写被服务人姓名"),
    APPOINT_PATIENT_GENDER_NULL("P0008", "请填写被服务人性别"),
    APPOINT_PATIENT_AGE_NULL("P0009", "请填写被服务人年龄"),

    APPOINT_TIME_INVALID("P0010", "请重新选择上门时间"),
    APPOINT_ADDRESS_NOT_SERVICE("P0011", "该地址暂不支持预约，请更换地址后重新提交"),

    APPOINT_PATIENT_AGE_LIMIT("P00012", "被服务人年龄不符合预约项目年龄限制"),
    APPOINT_PATIENT_GENDER_LIMIT("P00013", "被服务人性别不符合预约项目年龄限制"),
    EXPORT_VOUCHER_NON_DATA("P00014", "暂无可下载数据"),
    APPOINT_TIME_NOT_AVAIABLE("P0015", "预约时间在可约时段外"),
    ;

    ParamErrorCode(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     *
     */
    private String code;
    /**
     *
     */
    private String desc;

    /**
     *
     */
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return desc;
    }
}
