package com.jdh.o2oservice.b2b.base.exception;

import lombok.ToString;

/**
 * 系统通用错误码
 * @author: yang<PERSON>yu
 * @date: 2023/8/11 4:46 下午
 * @version: 1.0
 */
@ToString
public enum SystemErrorCode implements AbstractErrorCode, FriendlyErrorCode {
    /**
     *
     */
    UNKNOWN_ERROR("S0001", "unknown exception msg:{}", "系统繁忙请稍后重试"),

    READ_TIME_OUT("S0002", "readTimeout", "系统繁忙请稍后重试"),
    IO_EXCEPTION("S0003", "IO-exception", "系统繁忙请稍后重试"),
    PRC_UNKNOWN_EXCEPTION("S0004", "rpc unknown exception", "系统繁忙请稍后重试"),
    UNEXPECTED_HTTP_CODE("S0005", "Unexpected code {}", "系统繁忙请稍后重试"),
    JMQ_SEND_ERROR("S0006", "JMQ-send error", "系统繁忙请稍后重试"),
    JSF_INVOKE_ERROR("S0007", "JSF RPC result error msg:{}", "系统繁忙请稍后重试"),

    GENERATE_ID_PRODUCTION_ERROR("S0008", "分布式ID生产失败", "系统繁忙请稍后重试"),
    JSON_SERIALIZE_ERROR("S0009", "json序列化失败", "系统繁忙请稍后重试"),
    JSON_DESERIALIZE_ERROR("S0010", "json反序列化失败", "系统繁忙请稍后重试"),
    PARAM_NULL_ERROR("S0011", "参数错误：{}", "系统繁忙请稍后重试"),
    EVENT_BODY_IS_NULL("S0012", "事件body内容为空", "系统繁忙请稍后重试"),
    CONFIG_ERROR("S0013", "配置错误:{}", "系统繁忙请稍后重试"),
    MULTI_ABILITY_CODE("S0014", "abilityCode重复，全局需要保证唯一", "系统繁忙请稍后重试"),
    ILLEGAL_OPERATION("S0015", "非法操作", "系统繁忙请稍后重试"),
    HTTP_REQUEST_METHOD_NOT_SUPPORTED("S0016", "HttpRequestMethodNotSupported", "系统繁忙请稍后重试"),
    OSS_PUT_ERROR("S0017", "oss文件上传失败", "系统繁忙请稍后重试"),
    SYSTEM_ERROR("S0018", "系统错误", "系统繁忙请稍后重试"),
    SYSTEM_EXT_EXECUTE_ERROR("S0019", "扩展点执行失败", "系统繁忙请稍后重试"),
    OSS_GENERATE_URL_ERROR("S0020", "oss生成预签名失败", "系统繁忙请稍后重试"),
    DATA_AUTHORITY("S0021", "无数据权限", "权限不足"),
    OSS_GET_ERROR("S0022", "oss文件获取失败", "系统繁忙请稍后重试"),
    CACHE_OPERATION_ERROR("S0023", "缓存操作失败", "系统繁忙请稍后重试"),
    LOGIN_FAILURE("S0024", "pin无效", "请重新登陆"),
    REDIS_LOCK_GET_FAIL("S0025", "redis锁获取失败", "系统繁忙请稍后重试"),
    NO_SUPPORT_PARAM_TYPE("S0026", "不支持的参数类型", "不支持的参数类型"),
    FEE_FILE_NOT_EXIST("S0027", "费项文件不能为空", "费项文件不能为空"),
    CONTRACT_NUM_NULL("S0028", "合同编号不能为空", "合同编号不能为空"),
    CONTRACT_NULL("S0029", "合同已删除，请确认", "合同已删除，请确认"),
    CONTRACT_EXPIRE("S0030", "合同当前不在有效期内", "合同当前不在有效期内"),
    CONTRACT_NOT_SIGN("S0031", "合同当前非已签署状态", "合同当前非已签署状态"),
    CONTRACT_IS_EXIST("S0032", "合同已存在", "合同已存在"),
    CONTRACT_SKU_IS_EXIST("S0033", "合同已关联sku,暂不能删除", "合同已关联sku，暂不能删除"),
    CONTRACT_NOT_EXIST("S0034", "合同查询失败或不存在", "合同查询失败或不存在"),

    ;

    SystemErrorCode(String code, String desc, String tips) {
        this.code = code;
        this.desc = desc;
        this.tips = tips;
    }

    /**
     *
     */
    private String code;
    /**
     *
     */
    private String desc;
    /**
     * 友好提示
     */
    private String tips;

    /**
     *
     */
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return desc;
    }

    @Override
    public String getTips() {
        return tips;
    }
}
