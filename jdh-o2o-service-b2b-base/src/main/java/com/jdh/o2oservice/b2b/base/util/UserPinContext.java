package com.jdh.o2oservice.b2b.base.util;

import lombok.extern.slf4j.Slf4j;

/**
 * 从ThreadLocal获取userPin
 *
 * <AUTHOR>
 * @date 2020/7/1 21:37
 */
@Slf4j
public class UserPinContext {

    /**
     * threadLocal变量
     */
    private static final ThreadLocal<String> THREAD_LOCAL = new ThreadLocal<>();

    /**
     * 需要方法体加上UserPinCheck注解
     * 将userPin放入ThreadLocal
     *
     * @param userPin 用户Pin
     */
    public static void put(String userPin) {
        THREAD_LOCAL.set(userPin);
    }

    /**
     * 从ThreadLocal获取uerPin
     *
     * @return String
     */
    public static String get() {
        return THREAD_LOCAL.get();
    }

    /**
     * 将userPin从ThreadLocal移除
     */
    public static void remove() {
        THREAD_LOCAL.remove();
    }

}
