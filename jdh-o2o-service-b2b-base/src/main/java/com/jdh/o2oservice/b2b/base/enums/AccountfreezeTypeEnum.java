package com.jdh.o2oservice.b2b.base.enums;

import lombok.Getter;

@Getter
public enum AccountfreezeTypeEnum {

    FREEZE(1, "冻结"),
    RELEASE(2, "释放"),
    ;

    private Integer type;

    private String desc;

    public void setType(Integer type) {
        this.type = type;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    AccountfreezeTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static AccountfreezeTypeEnum getEnumByType(Integer status) {
        if (null == status) {
            return null;
        }
        for (AccountfreezeTypeEnum freezeTypeEnum : AccountfreezeTypeEnum.values()) {
            if (freezeTypeEnum.getType().intValue() == status.intValue()) {
                return freezeTypeEnum;
            }
        }
        return null;
    }
}
