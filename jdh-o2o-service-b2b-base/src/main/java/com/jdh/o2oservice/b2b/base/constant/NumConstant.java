package com.jdh.o2oservice.b2b.base.constant;

/**
 * 数值常量
 *
 * <AUTHOR>
 * @date 2023/12/21
 */
public class NumConstant {

    /**
     * 常量0
     */
    public static final Integer NUM_0 = 0;

    /**
     * 常量1
     */
    public static final Integer NUM_1 = 1;

    /**
     * 常量2
     */
    public static final Integer NUM_2 = 2;

    /**
     * 常量3
     */
    public static final Integer NUM_3 = 3;

    /**
     * 常量4
     */
    public static final Integer NUM_4 = 4;

    /**
     * 常量5
     */
    public static final Integer NUM_5 = 5;

    /**
     * 常量10
     */
    public static final Integer NUM_10 = 10;

    /**
     * 常量6
     */
    public static final Integer NUM_6 = 6;
    /**
     * 常量7
     */
    public static final Integer NUM_7 = 7;

    /**
     * 常量8
     */
    public static final Integer NUM_8 = 8;

    /**
     * 常量9
     */
    public static final Integer NUM_9 = 9;
    /**
     * 常量11
     */
    public static final Integer NUM_11 = 11;
    /**
     * 常量12
     */
    public static final Integer NUM_12 = 12;
    /**
     * 常量13
     */
    public static final Integer NUM_13 = 13;
    /**
     * 常量14
     */
    public static final Integer NUM_14 = 14;

    /**
     * 常量100
     */
    public static final Integer NUM_100 = 100;


    /**
     * 常量200
     */
    public static final Integer NUM_200 = 200;

    /**
     * 常量3000
     */
    public static final Integer NUM_3000 = 3000;

    /**
     * 常量1000
     */
    public static final Integer NUM_1000 = 1000;

    /**
     * 常量500
     */
    public static final Integer NUM_500 = 500;

    /**
     * 常量50
     */
    public static final Integer NUM_50 = 50;


    /**
     * 常量5000
     */
    public static final Integer NUM_10000 = 10000;
    /**
     * 常量6000
     */
    public static final Integer NUM_6000 = 6000;

    /**
     * 常量20000
     */
    public static final Integer NUM_20000 = 20000;

    /**
     * 常量30
     */
    public static final Integer NUM_30 = 30;

    /**
     * 常量60
     */
    public static final Integer NUM_60 = 60;

    /**
     * 常量一天的秒数
     */
    public static final Integer ONE_DAY = 60 * 60 * 24;

    /**
     * 常量 1800 s
     */
    public static final Integer NUM_1800 = 30 * 60;
}
