package com.jdh.o2oservice.b2b.base.ducc;

import com.google.common.collect.Maps;
import com.jd.laf.binding.annotation.JsonConverter;
import com.jd.laf.config.spring.annotation.LafValue;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.assertj.core.util.Sets;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * @Date 2025/2/23 下午8:02
 * <AUTHOR>
 **/
@Data
@Slf4j
@Component
public class DuccConfig {

    /**
     * 预警的 code和ump集合
     */
    @JsonConverter
    @LafValue("excludeAlarmCode")
    private Map<String, List<String>> excludeAlarmCode = new HashMap<>();

    /**
     * 数据库查询最大限制
     */
    @LafValue("dbQueryMaxLimit")
    private Long dbQueryMaxLimit = 1000L;

    /**
     * 上传excel的模板地址url
     */
    @JsonConverter
    @LafValue("uploadExcelTempUrl")
    private Map<String,String> uploadExcelTempUrl = new HashMap<>();

    /**
     * 排除记录方法类,在logging下面
     */
    @LafValue("excludeRecordClassMethod")
    @JsonConverter
    private List<String> excludeRecordClassMethod = new ArrayList<>();

    /**
     * 信用额度提醒
     */
    @LafValue("creditAmountTipContent")
    private String creditAmountTipContent;

    /**
     * 企业代管权限配置
     */
    @LafValue("enterpriseEscrowAuthConfig")
    private String enterpriseEscrowAuthConfig;

    /**
     * 企业代管文案配置
     */
    @LafValue("escrowCopyWriting")
    private String escrowCopyWriting;

    /**
     * 履约单详情按钮配置
     */
    @LafValue("promiseDetailButtonConfig")
    private String promiseDetailButtonConfig;

    /**
     * 企业代管权限配置开关
     */
    @LafValue("enterpriseEscrowAuthConfigSwitch")
    private Boolean enterpriseEscrowAuthConfigSwitch = Boolean.TRUE;

    /**
     * 企业代管cookie有效期
     */
    @LafValue("escrowCookieEffective")
    private Integer escrowCookieEffective;

    /**
     * b端展示题目类型
     */
    @LafValue("bQuestionGroupType")
    @JsonConverter
    private Set<String> bQuestionGroupType = Sets.newHashSet();

    @LafValue("filterQuesId")
    @JsonConverter
    private Map<String,List<Long>> filterQuesId = Maps.newHashMap();

}
