package com.jdh.o2oservice.b2b.base.enums;

public enum AccountSourceReceiptTypeEnum {

    VOUCHER(1, "voucher"),
    BILL_PAYMENT(2, "账单打款"),
    ;

    private Integer type;

    private String desc;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    AccountSourceReceiptTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static AccountSourceReceiptTypeEnum getEnumByType(Integer status) {
        if (null == status) {
            return null;
        }
        for (AccountSourceReceiptTypeEnum accountTypeEnum : AccountSourceReceiptTypeEnum.values()) {
            if (accountTypeEnum.getType().intValue() == status.intValue()) {
                return accountTypeEnum;
            }
        }
        return null;
    }
}
