package com.jdh.o2oservice.b2b.base.alarm;

import com.jd.ump.profiler.CallerInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName AlarmReachMessage
 * @Description
 * <AUTHOR>
 * @Date 2024/5/31 12:05
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlarmReachMessage {

    /**
     * 接口入参列表
     */
    Object[] args;

    /**
     * 接口方法ID
     */
    String methodId;

    /**
     * 接口方法名称
     */
    String methodName;

    /**
     * 关键字
     */
    String keyword;

    /**
     * 错误描述
     */
    String message;

    /**
     * ump callerInfo
     */
    CallerInfo callerInfo;
}