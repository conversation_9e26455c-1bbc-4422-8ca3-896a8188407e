package com.jdh.o2oservice.b2b.base.annotation;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 日志和UMP 注解
 *
 * <AUTHOR>
 * @date 2023/09/12
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD})
public @interface LogAndAlarm {

    /**
     * log/ump key
     *
     * @return {@link String}
     */
    String jKey() default "";

    /**
     * 日志打印开关
     *
     * @return boolean
     */
    boolean logSwitch() default true;

    /**
     * ump预警开关
     * 2024-09-06 移除，预警开关由ump平台配置，采集点继续保留
     * @return
     */
    boolean umpSwitch() default true;

    /**
     * 预警策略
     * @return
     */
    AlarmPolicy[] alarmPolicy() default {};
}
