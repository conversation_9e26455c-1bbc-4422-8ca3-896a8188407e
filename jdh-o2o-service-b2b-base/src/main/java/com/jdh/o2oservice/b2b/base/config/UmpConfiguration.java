package com.jdh.o2oservice.b2b.base.config;

import com.jd.ump.annotation.JAnnotation;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * UMP 配置bean
 *
 * <AUTHOR>
 * @date 2023/10/22
 */
@Configuration
public class UmpConfiguration {

    /**
     * umpJAnnotation 配置
     *
     * @return {@link JAnnotation}
     */
    @Bean(initMethod="afterPropertiesSet")
    public JAnnotation umpJAnnotation() {
        JAnnotation jAnnotation = new JAnnotation();
        // jvm 监控key
        jAnnotation.setJvmKey("jdh-o2o-service-b2b.jvmKey");
        return jAnnotation;
    }
}