package com.jdh.o2oservice.b2b.base.enums;
import lombok.Getter;
import java.io.Serializable;

/**
 * 操作类型枚举
 *
 * <AUTHOR>
 * @date 2025/01/13
 */
@Getter
public enum OpTypeEnum implements Serializable {

    UNKNOWN(0, "未知"),

    ADD(1, "新增"),

    DEL(2, "删除"),

    UPDATE(3, "修改"),

    QUERY(4, "查询"),

    UPLOAD(5, "上传"),

    DOWNLOAD(6, "下载"),

    BATCH_APPOINTMENT(7, "批量预约"),

    DOWNLOAD_SERVICE_APPOINTMENT(8, "下载服务预约"),

    DOWNLOAD_BILL(9, "下载账单");

    /**
     * 类型
     */
    private final Integer type;

    /**
     * 描述
     */
    private final String desc;

    OpTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static String getDescOfType(Integer type){
        if (type == null){
            return "";
        }
        for (OpTypeEnum opTypeEnum:OpTypeEnum.values()){
            if(opTypeEnum.getType().equals(type)){
                return opTypeEnum.getDesc();
            }
        }
        return "";
    }

}
