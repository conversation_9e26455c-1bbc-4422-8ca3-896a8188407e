package com.jdh.o2oservice.b2b.base.alarm;

import com.alibaba.fastjson.JSON;
import com.jd.ump.profiler.proxy.Profiler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @ClassName alarm
 * @Description
 * <AUTHOR>
 * @Date 2024/5/31 11:58
 **/
@Service
@Slf4j
public class UmpAlarmReachRpcImpl implements AlarmReachRpc {

    /**
     *
     * @param message
     * @return
     */
    @Override
    public boolean sendAlarm(AlarmReachMessage message) {
        log.info("UmpAlarmReachRpcImpl -> sendAlarm message={}", JSON.toJSONString(message));
        try {
            Profiler.functionError(message.getCallerInfo());
            return true;
        } catch (Throwable e) {
            log.error("UmpAlarmReachRpcImpl -> errorMsg={}", e.getMessage(), e);
        }
        return false;
    }
}