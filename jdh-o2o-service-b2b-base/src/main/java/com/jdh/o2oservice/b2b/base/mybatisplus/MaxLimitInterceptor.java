package com.jdh.o2oservice.b2b.base.mybatisplus;
import com.baomidou.mybatisplus.core.toolkit.PluginUtils;
import com.baomidou.mybatisplus.extension.plugins.inner.InnerInterceptor;
import com.jdh.o2oservice.b2b.base.ducc.DuccConfig;
import com.jdh.o2oservice.b2b.base.util.SpringUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;

import java.sql.Connection;

@Data
@Slf4j
public class MaxLimitInterceptor implements InnerInterceptor {

    /**
     * 是否忽略本插件标识
     */
    private static final InheritableThreadLocal<Boolean> IGNORE = new InheritableThreadLocal<>();

    /**
     * 设置忽略本插件
     *
     * @return String
     */
    public static void ignore() {
        IGNORE.set(Boolean.TRUE);
    }

    /**
     * 关闭忽略
     */
    public static void close() {
        IGNORE.remove();
    }

    /**
     * {@link StatementHandler#prepare(Connection, Integer)} 操作前置处理
     * <p>
     * 改改sql啥的
     *
     * @param sh                 StatementHandler(可能是代理对象)
     * @param connection         Connection
     * @param transactionTimeout transactionTimeout
     */
    @Override
    public void beforePrepare(StatementHandler sh, Connection connection, Integer transactionTimeout) {
        //如果需要忽略本插件
        if (Boolean.TRUE.equals(IGNORE.get())){
            return;
        }

        PluginUtils.MPStatementHandler mpSh = PluginUtils.mpStatementHandler(sh);
        MappedStatement ms = mpSh.mappedStatement();
        SqlCommandType sct = ms.getSqlCommandType();
        //是查询sql
        if (sct == SqlCommandType.SELECT) {
            PluginUtils.MPBoundSql mpBs = mpSh.mPBoundSql();
            String sql = mpBs.sql();
            //如果sql 不包含limit ，追加limit
            if(StringUtils.isNotBlank(sql) && !sql.toLowerCase().contains("limit")) {
                try {
                    Long dbQueryMaxLimit = SpringUtil.getBean(DuccConfig.class).getDbQueryMaxLimit();
                    if(log.isDebugEnabled()) {
                        log.info("LimitInterceptor add dbQueryMaxLimit:{}", dbQueryMaxLimit);
                    }
                    // 添加 LIMIT 条件,默认查询语句最多查询 dbQueryMaxLimit 条记录 默认1000  ducc可调整
                    sql += " LIMIT " + dbQueryMaxLimit;
                    mpBs.sql(sql);
                } catch (Exception e) {
                    log.error("LimitInterceptor intercept add dbQueryMaxLimit exception", e);
                }
            }
        }

    }
}
