package com.jdh.o2oservice.b2b.base.util;

import cn.hutool.core.collection.CollectionUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;

import java.lang.invoke.MethodHandle;
import java.lang.invoke.MethodType;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;

import static java.lang.invoke.MethodHandles.lookup;
import static java.lang.invoke.MethodType.methodType;

/**
 * 实体工具方法
 * @author: yangxiyu
 * @date: 2023/12/25 7:58 下午
 * @version: 1.0
 */
public class EntityUtil {

    public static    <T>T putFiled(Supplier<T> supplier, Consumer<T> consumer){
        T t = supplier.get();
        if (t == null){
            return null;
        }else if(t instanceof String){
            if (StringUtils.isNotBlank((String)t)){
                consumer.accept(t);
            }
        }else if(t instanceof Collection){
            if (CollectionUtil.isNotEmpty((Collection<?>) t)){
                consumer.accept(t);
            }
        }else {
            if (Objects.nonNull(t)){
                consumer.accept(t);
            }
        }
        return t;
    }


    /**
     *  方法调用指令
     * （1）invokevirtual指令：用于调用对象的实例方法，根据对象的实际类型进行分派（虚方法分派），这也是Java语言中最常见的方法分派方式。
     * （2）invokeinterface指令：用于调用接口方法，它会在运行时搜索一个实现了这个接口方法的对象，找出适合的方法进行调用。
     * （3）invokespecial指令：用于调用一些需要特殊处理的实例方法，包括实例初始化方法、私有方法和父类方法。
     * （4）invokestatic指令：用于调用类静态方法（static方法）。
     * （5）invokedynamic指令：用于在运行时动态解析出调用点限定符所引用的方法。并执行该方法。前面四条调用指令的分派逻辑都固化在Java虚拟机
     * 内部，用户无法改变，而invokedynamic指令的分派逻辑是由用户所设定的引导方法决定的
     * @param bean
     * @param methodName
     * @return
     * @throws Throwable
     */
    public static Object invokeMethod(Object bean, String methodName) throws Throwable {
        // 虚方法是实例方法，需要绑定实例；
        MethodType virtualType = methodType(String.class);
        MethodHandle virtualMethod = null;
        virtualMethod = lookup()
                // 查找虚方法，使用invokevirtual指令
                .findVirtual(bean.getClass(), methodName, virtualType)
                .bindTo(bean);
        return virtualMethod.invoke();
    }

    /**
     * 执行静态方法
     * @param className
     * @param methodName
     * @return
     * @throws Throwable
     */
    public static MethodHandle invokeStaticMethod(String className, String methodName, Class rType, Class[] pTypes) throws Throwable {
        Class clazz = Class.forName(className);
        MethodHandle staticMethod = null;
        MethodType virtualType = methodType(rType, pTypes);

        staticMethod = lookup()
                .findStatic(clazz, methodName, virtualType);

        return staticMethod;
    }


    /**
     * 根据bean实体的属性填充url
     * @param url
     * @param bean
     * @return
     */
    public static String fillUrlByBean(String url, Object bean){
        url = url.trim();
        StringBuilder newUrl = new StringBuilder();
        StringBuilder keyWord = new StringBuilder();
        boolean isKeyWord = Boolean.FALSE;
        for (char c : url.toCharArray()) {
            if (c == '{'){
                isKeyWord = Boolean.TRUE;
            }else if (c == '}'){
                String filed = keyWord.toString();
                String value = getProperty(bean, filed);
                newUrl.append(value);
                keyWord = new StringBuilder();
                isKeyWord = Boolean.FALSE;
            }else if(isKeyWord){
                keyWord.append(c);
            }else{
                newUrl.append(c);
            }
        }
        return newUrl.toString();
    }


    /**
     * 获取实例的属性指，如果实例bean为null则直接返回null，避免空指针。
     * 举例需要获取患者的名称原来的写法
     *  JdhPromisePatient patient = new JdhPromisePatient();
     *  String name = null
     *  if(Objects.nonNull(patient.getUserName())){
     *      name = patient.getUserName().name();
     *  }
     *
     *  新的写法：
     *  JdhPromisePatient patient = new JdhPromisePatient();
     *  String name = EntityUtil.getFiledDefaultNull(patient.getUserName(), UserName::getName)
     * @param beans
     * @param function
     * @return
     * @param <T>
     * @param <R>
     */
    public static <T,R> List<R> getFiledDefaultNull(List<T> beans, Function<T, R> function){
        if (CollectionUtils.isEmpty(beans)){
            return null;
        }
        List<R> res = Lists.newArrayList();
        for (T bean : beans) {
            if (Objects.nonNull(bean)){
                res.add(function.apply(bean));
            }
        }
        return res;
    }

    public static <T,R>R getFiledDefaultNull(T bean, Function<T, R> function){
        if (Objects.isNull(bean)){
            return null;
        }
        return function.apply(bean);
    }



    /**
     * 根据表达式获取属性值
     * 对类型做格式化处理
     * @param bean
     * @param expression
     * @return
     */
    private static String getProperty(Object bean, String expression) {
        Object value = cn.hutool.core.bean.BeanUtil.getProperty(bean, expression);
        if (value == null){
            return "null";
        }
        if (value instanceof LocalDateTime){
            return TimeUtils.localDateTimeToStr((LocalDateTime)value);
        }
        if (value instanceof LocalDate){
            return TimeUtils.localDateToStr((LocalDate) value);
        }
        if (value instanceof LocalDate){
            return TimeUtils.localDateToStr((LocalDate) value);
        }

        return Objects.toString(value);
    }

}
