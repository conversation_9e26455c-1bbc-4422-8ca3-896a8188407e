package com.jdh.o2oservice.b2b.base.exception;


import lombok.ToString;

/**
 * 动态的errorCode
 * @author: yang<PERSON>yu
 * @date: 2022/6/7 9:25 下午
 * @version: 1.0
 */
@ToString
public class DynamicErrorCode implements ErrorCode {

    /**
     * 编号
     */
    private String code;
    /**
     * 描述
     */
    private String description;

    /**
     * constructor
     * @param code
     * @param description
     */
    public DynamicErrorCode(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     *
     * @return
     */
    @Override
    public String getCode() {
        return code;
    }
    /**
     *
     * @return
     */
    @Override
    public String getDescription() {
        return description;
    }
}
