package com.jdh.o2oservice.b2b.base.exception;
import lombok.ToString;

/**
 * 业务错误码
 *
 * <AUTHOR>
 * @date 2023/12/11
 */
@ToString
public enum BusinessErrorCode implements AbstractErrorCode {

    /**
     * 成功
     */
    SUCCESS("0000", "OK"),

    RPC_JSF_ERROR("8888", "RPC接口异常"),

    /**
     * 未知异常
     */
    UNKNOWN_ERROR("9999", "系统忙请稍后重试"),

    /**
     * 用户登录信息失败
     */
    UNKNOWN_USER_LOGIN("11111", "抱歉，您的登录信息已过期，请重新登录"),

    /**
     * 重试失败
     */
    RETRY_ERROR("31020", "重试失败"),



    ///////////////////////////////////////orderPlatform
    ORDER_PLATFORM_EXIT("20000", "订单平台已存在"),

    ///////////////////////////////////////enterpriseVoucher
    URL_TEMPLATE_TYPE_NOT_EXIT("30001", "URL模板类型不存在"),
    SUBMIT_VOUCHER_ERROR("30002", "提交预约失败"),
    USER_ADDRESS_TEXT_PARSE_ERROR("30003", "抱歉，没有识别成功噢"),

    ENTERPRISE_SAVE_FAIL("30004", "企业保存失败"),

    ENTERPRISE_UPDATE_FAIL("30005", "企业更新失败"),

    ENTERPRISE_NAME_EXIST("30006", "企业名称重复"),

    ENTERPRISE_NO_EXIST("30007", "企业信息不存在"),

    ENTERPRISE_ACCOUNT_SAVE_FAIL("30008", "企业账户保存失败"),

    ENTERPRISE_ACCOUNT_UPDATE_FAIL("30009", "企业账户更新失败"),

    ENTERPRISE_ACCOUNT_NO_EXIST("40001", "企业账户不存在"),

    ENTERPRISE_ACCOUNT_RELEASE_AMOUNT_OVER_LIMIT("40002", "企业账户释放金额超限"),

    ENTERPRISE_SKU_SAVE_FAIL("40003", "企业服务项目保存失败"),

    ENTERPRISE_SKU_UPDATE_FAIL("40004", "企业服务项目更新失败"),

    ENTERPRISE_SKU_DELETE_FAIL("40005", "企业服务项目删除失败"),

    ENTERPRISE_SKU_EXIST("40006", "企业服务项目SKU已存在"),

    ENTERPRISE_SKU_NO_EXIST("40007", "企业服务项目不存在"),

    JDH_SKU_NO_EXIST("40008", "商品SKU不存在"),

    ENTERPRISE_USER_SAVE_FAIL("40009", "企业用户保存失败"),

    ENTERPRISE_USER_UPDATE_FAIL("50001", "企业用户更新失败"),

    ENTERPRISE_USER_DELETE_FAIL("50002", "企业用户删除失败"),

    ENTERPRISE_USER_EXIST("50003", "企业用户已存在"),

    ENTERPRISE_USER_NO_EXIST("50004", "企业用户不存在"),

    ENTERPRISE_VOUCHER_NO_EXIST("50004", "企业服务单不存在"),

    PIN_ILLEGALITY("50005", "用户pin不合法"),

    SELECT_B_SKU("50006", "请选择B端SKU商品"),

    JDH_SKU_CHANNEL_NO_EXIST("50007", "商品SKU售卖渠道不存在"),

    VOUCHER_BALANCE_NOT_ENOUGH("50008", "余额不足"),

    ENTERPRISE_DISABLE("50009", "企业状态已禁用，可进行读操作，无法进行写操作"),

    ENTERPRISE_USER_DISABLE("60001", "您没有访问权限"),

    ENTERPRISE_SKU_NON_OWNER("40007", "当前企业下无此服务项目"),

    ;

    BusinessErrorCode(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     *
     */
    private String code;
    /**
     *
     */
    private String desc;

    /**
     *
     */
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return desc;
    }
}
