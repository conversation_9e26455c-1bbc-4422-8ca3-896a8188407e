package com.jdh.o2oservice.b2b.base.config;

import com.jd.jade.init.spring.InitializeBean;
import com.jd.laf.config.ConfiguratorManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * JadeConfig
 *
 * <AUTHOR>
 * @date 2024/12/03
 */
@Slf4j
@Configuration
public class JadeConfig {

    /**
     * 配置器管理器
     */
    @Resource
    private ConfiguratorManager configuratorManager;

    /**
     * jadeInitBean
     *
     * @return {@link InitializeBean }
     */
    @Bean
    public InitializeBean jadeInitBean() {
        // 这里内嵌定义一个 DUCC ConfiguratorManager，也可以直接 ref 已有实例，只要在 resource 列表中添加 jade 配置
        // 注意这里，如果 uri 中 config 不是命名为 jade，则 name 属性需要设置为 jade
        InitializeBean initBean = new InitializeBean();
        initBean.setConfigServiceProvider(configuratorManager);
        return initBean;
    }
}
