package com.jdh.o2oservice.b2b.base.util;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * AnnotationValidator AnnotationValidator
 *
 * <AUTHOR>
 * @version 2025/06/02 20:09
 **/
public class AnnotationValidator {

    public static List<String> validate(Object object) {
        List<String> errorMessages = new ArrayList<>();

        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();

        Set<ConstraintViolation<Object>> violations = validator.validate(object);

        for (ConstraintViolation<Object> violation : violations) {
            errorMessages.add(violation.getMessage());
        }

        return errorMessages;
    }
}
