package com.jdh.o2oservice.b2b.base.factory;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.jd.pfinder.profiler.sdk.PfinderContext;
import com.jdh.o2oservice.b2b.base.enums.ThreadPoolConfigEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Map;
import java.util.concurrent.*;

/**
 * <AUTHOR> @Description 线程池工厂类：用于创建线程池
 * @Date 2019/8/5 13:52
 */
@Slf4j
@Configuration
public class ExecutorPoolFactory {


    static{
        Runtime.getRuntime().addShutdownHook(new Thread(ExecutorPoolFactory::registerShutdownHook));
    }

    /**
     * 线程池集合：key-自定义的枚举类型，value-线程池的接口类型，初始化集合长度为枚举类的values长度
     */
    private static final ConcurrentHashMap<ThreadPoolConfigEnum, ExecutorService> POOL_FACTORY_MAP = new ConcurrentHashMap<>(ThreadPoolConfigEnum.values().length);

    /**
     * 从集合中获取线程池对象：根据枚举类型映射map集合中的自定义线程对象
     * @param poolEnum 枚举类
     * @return
     */
    public static ExecutorService get(ThreadPoolConfigEnum poolEnum) {
        ExecutorService executorService = POOL_FACTORY_MAP.get(poolEnum);

        if (executorService != null) {
            return executorService;
        }

        synchronized (ExecutorPoolFactory.class) {
            if (POOL_FACTORY_MAP.get(poolEnum) == null) {
                int poolSize = poolEnum.getPoolSize() > 0 ? poolEnum.getPoolSize() : 1;
                int capacity = poolEnum.getCapacity() > 0 ? poolEnum.getCapacity() : 256;
                // 获取拒绝策略
                RejectedExecutionHandler rejectedHandler = poolEnum.getRejectedHandler() != null ? poolEnum.getRejectedHandler() : getRejectedExecutionHandler();
                ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat(poolEnum.getPoolName() + "-%d").build();
                POOL_FACTORY_MAP.put(poolEnum, PfinderContext.executorServiceWrapper(new ThreadPoolExecutor(poolSize, poolSize,
                        0L, TimeUnit.MILLISECONDS,
                        new LinkedBlockingQueue<Runnable>(capacity),
                        threadFactory,
                        rejectedHandler
                )));
                log.info("ExecutorPoolFactory Success to create {}:{} executorService", poolEnum, poolSize);
            }
        }

        return POOL_FACTORY_MAP.get(poolEnum);
    }

    /**
     * 当提交任务数超过maxmumPoolSize+workQueue之和时，任务会交给RejectedExecutionHandler来处理，
     * 当没有更多的线程或队列插槽时，自定义如何处理超出能力范围之外的任务
     * @return
     */
    private static RejectedExecutionHandler getRejectedExecutionHandler() {
        return new RejectedExecutionHandler() {
            @Override
            public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
                if (!executor.isShutdown()) {
                    r.run();
                    log.info("main thread run task !");
                }
            }
        };
    }

    /**
     * 销毁线程池:销毁集合中的线程池
     */
    private static void registerShutdownHook() {
        if (MapUtils.isEmpty(POOL_FACTORY_MAP)) {
            return;
        }

        synchronized (ExecutorPoolFactory.class) {
            for (Map.Entry<ThreadPoolConfigEnum, ExecutorService> entry : POOL_FACTORY_MAP.entrySet()) {
                ExecutorService executorService = entry.getValue();
                ThreadPoolConfigEnum poolConfigs = entry.getKey();
                String poolName = poolConfigs.getPoolName();
                int poolSize = poolConfigs.getPoolSize();
                try {
                    if (executorService != null && !executorService.isShutdown()) {
                        executorService.shutdown();
                        log.info("Success to shutdown {}:{} executorService", poolName, poolSize);
                    }
                } catch (Exception e) {
                    log.error("Error to shutdown {}:{} executorService", poolName, poolSize);
                }
            }
        }

    }

    @Bean("defaultExecutorService")
    public ExecutorService executor(){
        try {
           return ExecutorPoolFactory.get(ThreadPoolConfigEnum.DEFAULT);
        } catch (Exception e) {
            log.error("====>Init ThreadPoolTaskExecutor fail.");
            throw e;
        }
    }

}
