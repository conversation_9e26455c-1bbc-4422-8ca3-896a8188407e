package com.jdh.o2oservice.b2b.base.config;

import com.jd.security.tde.spring.TdeClientFactoryBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Bean配置
 *
 * <AUTHOR>
 * @date 2023/09/14
 */
@Configuration
public class AcesConfig {

    /**
     * 令牌
     */
    @Value("${tde.token}")
    private String token;

    /**
     * rPath
     */
    @Value("${tde.rPath}")
    private String rPath;

    /**
     * isProd
     */
    @Value("${tde.isProd}")
    private Boolean isProd;

    /**
     * enableGM
     */
    @Value("${tde.enableGM}")
    private Boolean enableGm;

    /**
     * 获取TDE客户端
     *
     * @return {@link TdeClientFactoryBean}
     */
    @Bean("tdeClient")
    public TdeClientFactoryBean getTdeClient(){
        TdeClientFactoryBean tdeClientFactoryBean = new TdeClientFactoryBean();
        tdeClientFactoryBean.setToken(token);
        tdeClientFactoryBean.setEnableGM(enableGm);
        tdeClientFactoryBean.setrPath(rPath);
        tdeClientFactoryBean.setIsProd(isProd);
        return tdeClientFactoryBean;
    }

}
