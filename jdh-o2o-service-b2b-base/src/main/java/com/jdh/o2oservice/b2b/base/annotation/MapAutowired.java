package com.jdh.o2oservice.b2b.base.annotation;

import java.lang.annotation.*;

/**
 * @ClassName MapAutowired
 * @Description 自定义注入bean注解，逻辑与Autowired基本一致，只在注入成员变量时有所不同，
 * 当成员变量为Map结构时，如果map.value的bean实现了MapAutowiredKey接口，
 * 该bean的map.key可自定义（spring默认注入map时key为beanName，并不实用）
 * <AUTHOR>
 * @Date 2022/4/30 20:27
 **/
@Target({ElementType.CONSTRUCTOR, ElementType.METHOD, ElementType.PARAMETER, ElementType.FIELD, ElementType.ANNOTATION_TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface MapAutowired {
    
    /**
     * Declares whether the annotated dependency is required.
     * <p>Defaults to {@code true}.
     */
    boolean required() default true;
}