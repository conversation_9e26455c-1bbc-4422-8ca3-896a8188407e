package com.jdh.o2oservice.b2b.base.mybatisplus;
import com.jdh.o2oservice.b2b.base.util.SpringUtil;
import com.jdh.o2oservice.b2b.base.util.TdeClientUtil;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * AcesCipherIndexHandler
 *
 * <AUTHOR>
 * @date 2023/09/20
 */
public class AcesCipherIndexHandler extends BaseTypeHandler<String> {


    /**
     * 设置非空参数
     *
     * @param preparedStatement preparedStatement
     * @param i                 我
     * @param parameter         参数
     * @param jdbcType          JDBC类型
     * @throws SQLException SQLExcept
     */
    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, String parameter, JdbcType jdbcType) throws SQLException {
        preparedStatement.setString(i, SpringUtil.getBean(TdeClientUtil.class).obtainKeyWordIndex(parameter));
    }

    /**
     * 获取可为空结果
     *
     * @param resultSet  结果集
     * @param columnName 列名
     * @return {@link String}
     * @throws SQLException SQLExcept
     */
    @Override
    public String getNullableResult(ResultSet resultSet, String columnName) throws SQLException {
        return resultSet.getString(columnName);
    }

    /**
     * 获取可为空结果
     *
     * @param resultSet   结果集
     * @param columnIndex 列索引
     * @return {@link String}
     * @throws SQLException SQLExcept
     */
    @Override
    public String getNullableResult(ResultSet resultSet, int columnIndex) throws SQLException {
        return resultSet.getString(columnIndex);
    }

    /**
     * 获取可为空结果
     *
     * @param callableStatement 可调用语句
     * @param columnIndex       列索引
     * @return {@link String}
     * @throws SQLException SQLExcept
     */
    @Override
    public String getNullableResult(CallableStatement callableStatement, int columnIndex) throws SQLException {
        return callableStatement.getString(columnIndex);
    }
}
