package com.jdh.o2oservice.b2b.base.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;


/**
 * 用户PIN检查
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface PinB2bErp {

    /**
     * 允许无pin的情况
     *
     * @return boolean
     */
    boolean allowNoPin() default false;
}