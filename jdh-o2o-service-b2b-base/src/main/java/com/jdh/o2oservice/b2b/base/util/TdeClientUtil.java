package com.jdh.o2oservice.b2b.base.util;

import com.jd.security.tdeclient.TDEClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description:
 * @date 2023-03-02
 */
@Slf4j
@Service
public class TdeClientUtil {
    
    /**
     * tdeClient
     */
    @Autowired
    @Qualifier("tdeClient")
    private TDEClient tdeClient;
    
    /**
     * 是可解密
     * public static enum CipherStatus {
     *     Decryptable,    // 密文可以被解密
     *     Malformed,      // 无效密文，密文已损坏，格式错误
     *     Feasible,       // 高版本密钥加密的密文，但客户端需要尝试获取新密钥才可以解密
     *     UnDecryptable;  // 正确的密文，但客户端没有相应的密钥可以解密
     * }
     * @param encryptText encryptText
     * @return {@link Boolean}
     */
    public Boolean isDecryptable(String encryptText){
        Boolean res = Boolean.FALSE;
        try {
            if (this.tdeClient.isDecryptable(encryptText) == TDEClient.CipherStatus.Decryptable) {
                res =  Boolean.TRUE;
            }
        } catch (final Exception exp) {
            log.error("TdeClientUtil -> encrypt error: ", exp);
        }
        return res;
    }
    
    /**
     * 加密.
     * @param str 需要加密的字符串
     * @return 加密后的字符串
     */
    public String encrypt(final String str) {
        String res = null;
        try {
            res = this.tdeClient.encryptString(str);
        } catch (final Exception exp) {
            log.error("TdeClientUtil -> encrypt error: ", exp);
        }
        return res;
    }
    
    /**
     * 生成Wildcard查询索引
     *
     * @param str 应力
     * @return {@link String}
     */
    public String calculateWildCardKeyWord(final String str){
        try {
            return this.tdeClient.calculateWildCardKeyWord(str);
        }catch (Exception exp){
            log.error("TdeClientUtil -> calculateWildCardKeyWord error: ", exp);
        }
        return str;
    }

    /**
     * 生成Wildcard查询索引
     *
     * @param str 应力
     * @return {@link String}
     */
    public String calculateKeyWord(final String str){
        try {
            return this.tdeClient.calculateKeyWord(str);
        }catch (Exception exp){
            log.error("TdeClientUtil -> calculateKeyWord error: ", exp);
        }
        return str;
    }

    /**
     * 生成Wildcard密文索引
     *
     * @param str 应力
     * @return {@link String}
     */
    public String obtainWildCardKeyWordIndex(final String str){
        try {
            return this.tdeClient.calculateWildCardKeyWord(str);
        }catch (Exception exp){
            log.error("TdeClientUtil -> obtainWildCardKeyWordIndex error: ", exp);
        }
        return str;
    }


    /**
     * 生成Wildcard密文索引
     *
     * @param str 应力
     * @return {@link String}
     */
    public String obtainKeyWordIndex(final String str){
        try {
            return this.tdeClient.obtainKeyWordIndex(str);
        }catch (Exception exp){
            log.error("TdeClientUtil -> obtainKeyWordIndex error: ", exp);
        }
        return str;
    }
    
    /**
     * 解密.
     * @param str 需要解密的字符串
     * @return 解密后的字符串
     */
    public String decrypt(final String str) {
        String res = null;
        try {
            if (this.tdeClient.isDecryptable(str) == TDEClient.CipherStatus.Decryptable) {
                res = this.tdeClient.decryptString(str);
            } else {
                log.error("TdeClientUtil -> decrypt failed");
            }
        } catch (final Exception exp) {
            log.error("TdeClientUtil -> decrypt error: ", exp);
        }
        return res;
    }

    /**
     * 计算index
     * @param args
     * @throws Exception
     */
    public static void main(String[] args) throws Exception {
        String token = "eyJzaWciOiJUYkh6ZHJOWjUyay9Gc0pkSC9hc3UxNmlTY3hZVk9sYjZPNDc4ODVZVUFJZUxjV0hhTVA0OGZ5ZTZtYWdwaGl1cUNIc3pOdnRRcjFHUTdqdUYrRkFiREpWOEF4dk1ORndNNStGanFxVkZZNmlOZlJzNUduSUxFNGZFdUw3eDRTQ2ZjTnpTYUNzSXNEWE5VZnpGYWNvdVJhUnE1dnpZVm54RkhXQmI3RldaN2t1UGovSUcvZWo1VW5WS2g0RlMzQUQ3U0loZUVDQ0ZqN3FTUUJtSFNtRTdONEZkdytTbmJORlNyZTJzbHRTV1poMmgwRTVUMzhkbzI3N0RZWktBZkJuR0dMejlsNXBlS01qZHNBQ0pwQm80b3VwWWlwNzJGM3ZzMHUxL1UwOUp2T3ZuOU13UDc3d1V0ZytMS1pQcDc0eWNEYXVwdExRMHQyTzNJZWtqaGFLQ3c9PSIsImRhdGEiOnsiYWN0IjoiY3JlYXRlIiwiZWZmZWN0aXZlIjoxNjY4OTYwMDAwMDAwLCJleHBpcmVkIjoxNzMyMTE4NDAwMDAwLCJpZCI6Ik1qaGlPR1F6TXpRdFl6YzVZeTAwWkdJeExXRmxaall0WVRNNU0yUTNNMkZqT1dVMSIsImtleSI6ImxGKzNXVE5seTBIK2hNU2JGaGFoN21UVUU2QURKU3NJSXBCQXRTNnI5UVE9Iiwic2VydmljZSI6InBoeXNpY2FsZXhhbWluYXRpb25fRGpXdEp3Ym8iLCJzdHlwZSI6MX0sImV4dGVybmFsRGF0YSI6eyJ6b25lIjoiQ04tMCJ9fQ";
        TDEClient client = TDEClient.getInstance(token, true);
        String index = client.encryptString("");
    }
}
