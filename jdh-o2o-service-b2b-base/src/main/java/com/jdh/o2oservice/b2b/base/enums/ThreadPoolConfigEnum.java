package com.jdh.o2oservice.b2b.base.enums;

import java.util.concurrent.RejectedExecutionHandler;

/**
 * <AUTHOR>
 * @Description 权益提供方
 * @Date 2019/12/27 20:17
 */
public enum ThreadPoolConfigEnum {


    /**
     * 默认线程池大小，线程数时CPU的两倍
     */
    DEFAULT("DEFAULT", 8, 8, null),

    /**
     * ENTERPRISE_VOUCHER
     */
    ENTERPRISE_VOUCHER("ENTERPRISE_VOUCHER", 30, 30, null),


    /**
     * ENTERPRISE_SKU
     */
    ENTERPRISE_SKU("ENTERPRISE_SKU", 30, 30, null),

    /**
     * 操作日志记录线程池
     */
    OPLOG_SAVE_THREAD_POOL("OPLOG_SAVE_THREAD_POOL", 50, 50, null),


    ;
    
    /**
     * 线程池名字
     */
    private final String poolName;
    
    /**
     * 核心线程数量
     */
    private final int poolSize;
    /**
     * 阻塞任务队列的长度
     */
    private final int capacity;

    /**
     * 线程池的可用线程数和阻塞队列的容量全部打满后，执行此任务策略：
     * 1. CallerRunsPolicy ：这个策略重试添加当前的任务，他会自动重复调用 execute() 方法，直到成功。
     * 2. AbortPolicy ：对拒绝任务抛弃处理，并且抛出异常。
     * 3. DiscardPolicy ：对拒绝任务直接无声抛弃，没有异常信息。
     * 4. DiscardOldestPolicy ：对拒绝任务不抛弃，而是抛弃队列里面等待最久的一个线程，然后把拒绝任务加到队列。
     */
    private final RejectedExecutionHandler rejectedHandler;

    /**
     * @param poolName 线程池名字
     * @param poolSize 核心线程数量
     * @param capacity 阻塞任务队列的长度
     * @param rejectedHandler 拒绝策略
     */
    ThreadPoolConfigEnum(String poolName, int poolSize, int capacity, RejectedExecutionHandler rejectedHandler) {
        this.poolName = poolName;
        this.poolSize = poolSize;
        this.capacity = capacity;
        this.rejectedHandler = rejectedHandler;
    }

    public String getPoolName() {
        return poolName;
    }

    public int getPoolSize() {
        return poolSize;
    }

    public int getCapacity() {
        return capacity;
    }

    public RejectedExecutionHandler getRejectedHandler() {
        return rejectedHandler;
    }
}
