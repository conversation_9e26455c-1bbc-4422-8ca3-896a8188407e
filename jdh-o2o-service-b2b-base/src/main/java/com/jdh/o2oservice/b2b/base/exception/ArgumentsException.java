package com.jdh.o2oservice.b2b.base.exception;

/**
 * 参数异常
 * todo:是否需要将参数校验异常抽取出来，需要达成共识。 可以直接使用BusinessException，如果抽取出来，需要在参数校验时使用ArgumentsException，将参数校验和业务校验进行区分。
 * 拆的好处：职责清晰，可以沉淀像姓名、手机号校验这种通用属性的校验，本身业务属性不强。但是拆分后需要严格遵守约定，不然容易混用。
 *
 * @author: yang<PERSON>yu
 * @date: 2022/8/24 5:11 下午
 * @version: 1.0
 */
public class ArgumentsException extends BusinessException{

    /**
     * 参数异常
     * @param errorCode
     */
    public ArgumentsException(ErrorCode errorCode) {
        super(errorCode);
    }

    /**
     *
     * @param errorCode
     * @param errorMessage
     */
    public ArgumentsException(ErrorCode errorCode, String errorMessage) {
        super(errorCode, errorMessage);
    }
}
