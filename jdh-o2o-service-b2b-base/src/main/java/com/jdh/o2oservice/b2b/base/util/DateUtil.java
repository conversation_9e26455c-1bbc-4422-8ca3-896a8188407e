package com.jdh.o2oservice.b2b.base.util;

import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

public class DateUtil extends DateUtils {
    private static String[] parsePatterns = new String[]{"yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm"};

    public DateUtil() {
    }

    public static String getDate() {
        return getDate("yyyy-MM-dd");
    }

    public static String getShortDate() {
        return getDate("yyMMdd");
    }

    public static String getDate(String pattern) {
        return DateFormatUtils.format(new Date(), pattern);
    }

    public static String formatDate(Date date, String pattern) {
        String formatDate = null;
        if (date != null && pattern != null && pattern.length() > 0) {
            formatDate = DateFormatUtils.format(date, pattern);
        }

        return formatDate;
    }

    public static String getTime() {
        return formatDate(new Date(), "HH:mm:ss");
    }

    public static String getDateTime() {
        return formatDate(new Date(), "yyyy-MM-dd HH:mm:ss");
    }

    public static String getYear() {
        return formatDate(new Date(), "yyyy");
    }

    public static String getMonth() {
        return formatDate(new Date(), "MM");
    }

    public static String getDay() {
        return formatDate(new Date(), "dd");
    }

    public static String getWeek() {
        return formatDate(new Date(), "E");
    }

    public static Date parseDateWithPatterns(String str, String... myPatterns) {
        if (str == null) {
            return null;
        } else {
            try {
                return myPatterns.length > 0 ? parseDate(str, myPatterns) : parseDate(str, parsePatterns);
            } catch (ParseException var3) {
                return null;
            }
        }
    }

    public static Date addDays(Date d, int days) {
        Calendar c = Calendar.getInstance();
        c.setTime(d);
        c.add(5, days);
        Date newD = c.getTime();
        return newD;
    }

    public static Date addMinutes(Date d, int minutes) {
        Calendar c = Calendar.getInstance();
        c.setTime(d);
        c.add(12, minutes);
        Date newD = c.getTime();
        return newD;
    }

    public static long pastDays(Date date) {
        long t = System.currentTimeMillis() - date.getTime();
        return t / 86400000L;
    }

}
