package com.jdh.o2oservice.b2b.base.factory;

import java.util.Queue;

/**
 * 全局ID生产工厂
 * @author: yang<PERSON><PERSON>
 * @date: 2023/11/28 11:14 上午
 * @version: 1.0
 */
public interface GenerateIdFactory {

    /**
     * @return
     * @throws Exception
     */
    String getIdStr();

    /**
     * @return
     */
    Long getId();

    /**
     *
     * @param requiredNum
     * @return
     */
    Queue<Long> getBatchId(int requiredNum);

    /**
     * 获取报告id
     * @return
     */
    Long getReportId();
}
