package com.jdh.o2oservice.b2b.base.annotation;
import com.jdh.o2oservice.b2b.base.enums.AlarmPolicyTypeEnum;

import java.lang.annotation.*;

/**
 * @ClassName AlarmPolicy
 * @Description 报警策略
 * <AUTHOR>
 * @Date 2024/5/31 09:42
 **/
@Target(ElementType.ANNOTATION_TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Inherited
public @interface AlarmPolicy {
    /**
     * 方法id
     */
    String methodId() default "";

    /**
     * 方法名称
     */
    String methodName() default "";

    /**
     * 关键字
     */
    String keyword() default "";

    /**
     * 报警类型
     */
    AlarmPolicyTypeEnum type() default AlarmPolicyTypeEnum.UMP;
}