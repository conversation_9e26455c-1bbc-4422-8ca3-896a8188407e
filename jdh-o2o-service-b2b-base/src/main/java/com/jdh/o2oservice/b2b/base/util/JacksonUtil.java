package com.jdh.o2oservice.b2b.base.util;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import org.springframework.lang.Nullable;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

/**
 * JacksonUtil
 * <AUTHOR>
 * @date 2024/02/04
 */
public class JacksonUtil {

	/**
	 * objectMapper
	 */
	private static final ObjectMapper objectMapper = new ObjectMapper();


	/**
	 * jackson配置
	 *
	 * @param objectMapper 对象映射器
	 */
	static {
		//开启类型识别,写出@class。后续反序列化自动识别类型
		objectMapper.enableDefaultTyping(ObjectMapper.DefaultTyping.NON_FINAL, JsonTypeInfo.As.PROPERTY);

		//序列化只包含不为null的字段
		objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
		//反序列化时java不存在属性忽略
		objectMapper.configure(MapperFeature.USE_GETTERS_AS_SETTERS,false);
		objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES,false);

		//配置jdk8的 LocalDate系列
		JavaTimeModule javaTimeModule = new JavaTimeModule();
		javaTimeModule.addSerializer(LocalDate.class, new LocalDateSerializer(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
		javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
		javaTimeModule.addSerializer(LocalTime.class, new LocalTimeSerializer(DateTimeFormatter.ofPattern("HH:mm:ss")));

		javaTimeModule.addDeserializer(LocalDate.class, new LocalDateDeserializer(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
		javaTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
		javaTimeModule.addDeserializer(LocalTime.class, new LocalTimeDeserializer(DateTimeFormatter.ofPattern("HH:mm:ss")));

		objectMapper.registerModule(javaTimeModule);
	}

	/**
	 * toJSONString
	 *
	 * @param source 源
	 * @return {@link String }
	 */
	public static String toJSONString(Object source){
		if (source == null) {
			return null;
		}
		try {
			return objectMapper.writeValueAsString(source);
		} catch (JsonProcessingException e) {
			throw new RuntimeException("Could not write JSON: " + e.getMessage(), e);
		}
	}


	/**
	 * parseObject
	 *
	 * @param source 来源
	 * @param type   类型
	 * @return {@link T}
	 */
	@Nullable
	public static <T> T parseObject(@Nullable String source, Class<T> type){
		if ((source == null || source.isEmpty())) {
			return null;
		}
		try {
			return objectMapper.readValue(source, type);
		} catch (Exception ex) {
			throw new RuntimeException("Could not read JSON: " + ex.getMessage(), ex);
		}
	}


}