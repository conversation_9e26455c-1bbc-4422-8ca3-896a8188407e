package com.jdh.o2oservice.b2b.base.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 系统环境枚举
 * @author: yang<PERSON>yu
 * @date: 2022/4/13 10:35 上午
 * @version: 1.0
 */
public enum EnvEnum {
    /** */
    DEV_TEST("dev"),
    YFB("yfb"),
    PRODUCTION("production");

    /** */
    EnvEnum(String code) {
        this.code = code;
    }
    /** */
    private String code;
    /** */
    public String getCode() {
        return code;
    }


    public static Boolean isProduction(String env){
        return StringUtils.equals(env, PRODUCTION.getCode());
    }


    public static Boolean isYfb(String env){
        return StringUtils.equals(env, YFB.getCode());
    }

    public static Boolean isDev(String env){
        return StringUtils.equals(env, DEV_TEST.getCode());
    }
}
