package com.jdh.o2oservice.b2b.base.util;

import com.jdh.o2oservice.b2b.base.exception.BusinessErrorCode;
import com.jdh.o2oservice.b2b.base.exception.ErrorCode;
import com.jdh.o2oservice.b2b.common.response.Response;

/**
 * ResponseUtil
 *
 * <AUTHOR>
 * @date 2023/12/05
 */
public class ResponseUtil {

    /**
     * 构造函数
     */
    private ResponseUtil(){

    }

    /**
     * 构建响应
     *
     * @return {@link Response}
     */
    public static Response buildErrResponse(ErrorCode errorCode){
        Response response = new Response<>();
        response.setData(null);
        response.setCode(errorCode.getCode());
        response.setMsg(errorCode.getDescription());
        return response;
    }
    /**
     * 构建响应
     *
     * @return {@link Response}
     */
    public static Response buildErrResponse(ErrorCode errorCode, String msg){
        Response response = new Response<>();
        response.setData(null);
        response.setCode(errorCode.getCode());
        response.setMsg(msg);
        return response;
    }

    public static Response buildErrResponse(){
        Response response = new Response<>();
        response.setData(null);
        return response;
    }

    /**
     * 构建响应
     *
     * @return {@link Response}
     */
    public static <T> Response<T> buildSuccResponse(T data){
        Response<T> response = new Response<>();
        response.setData(data);
        response.setCode(BusinessErrorCode.SUCCESS.getCode());
        response.setMsg(BusinessErrorCode.SUCCESS.getDescription());
        return response;
    }

    public static Response buildErrResponse(String errorCode, String msg){
        Response response = new Response<>();
        response.setData(null);
        response.setCode(errorCode);
        response.setMsg(msg);
        return response;
    }

}
