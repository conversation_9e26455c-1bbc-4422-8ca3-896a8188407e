package com.jdh.o2oservice.b2b.base.util;
import cn.hutool.core.collection.CollectionUtil;
import com.jdh.o2oservice.b2b.base.exception.BusinessException;
import com.jdh.o2oservice.b2b.base.exception.ErrorCode;
import com.jdh.o2oservice.b2b.base.exception.SystemErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.Objects;

/**
 *  校验工具类 AssertUtils
 * <AUTHOR>
 * @date 2020-08-07 11:21
 */
@Slf4j
public class AssertUtils {

	/**
	 * 断言字符串不为null且不为空,否则抛出业务异常
	 *
	 * @param text      字符串
	 * @param errorCode 业务message信息
	 */
	public static void hasText(String text, ErrorCode errorCode) {
		if (StringUtils.isBlank(text)) {
			throw new BusinessException(errorCode);
		}
	}

	/**
	 * isFalse
	 *
	 * @param result    result
	 * @param errorCode 错误代码
	 */
	public static void isFalse(Boolean result, ErrorCode errorCode) {
		if (Boolean.FALSE.equals(result)) {
			throw new BusinessException(errorCode);
		}
	}

	/**
	 * isFalse
	 *
	 * @param result    result
	 * @param msg 错误代码
	 */
	public static void isFalse(Boolean result, String msg) {
		if (Boolean.FALSE.equals(result)) {
			ErrorCode errorCode = SystemErrorCode.PARAM_NULL_ERROR.formatDescription(msg);
			throw new BusinessException(errorCode);
		}
	}

	/**
	 * isTrue
	 *
	 * @param result    result
	 * @param errorCode 错误代码
	 */
	public static void isTrue(Boolean result, ErrorCode errorCode) {
		if (Boolean.TRUE.equals(result)) {
			throw new BusinessException(errorCode);
		}
	}

	/**
	 *
	 */
	public static void hasText(String param, String msg) {
		if (StringUtils.isBlank(param)) {
			ErrorCode errorCode = SystemErrorCode.PARAM_NULL_ERROR.formatDescription(msg);
			throw new BusinessException(errorCode);		}
	}
	public static void equalsText(String param, String target, ErrorCode errorCode) {
		if (!StringUtils.equals(param, target)) {
			throw new BusinessException(errorCode);
		}
	}

	/**
	 * 断言不为null，否则抛出异常
	 *
	 * @param obj       object
	 * @param errorCode 异常message
	 */
	public static void nonNull(Object obj, ErrorCode errorCode) {
		if (Objects.isNull(obj)) {
			throw new BusinessException(errorCode);
		}
	}

	/**
	 * 断言不为null，否则抛出异常
	 *
	 * @param obj object
	 */
	public static void nonNull(Object obj, String msg) {
		if (Objects.isNull(obj)) {
			ErrorCode errorCode = SystemErrorCode.PARAM_NULL_ERROR.formatDescription(msg);
			throw new BusinessException(errorCode);
		}
	}
	public static void equalsObject(Object param, Object target, ErrorCode errorCode) {
		if (!Objects.equals(param, target)) {
			throw new BusinessException(errorCode);
		}
	}

	/**
	 *
	 * @param param
	 * @param target
	 * @param errorCode
	 */
	public static void nonEqualsObject(Object param, Object target, ErrorCode errorCode) {
		if (!Objects.equals(param, target)) {
			throw new BusinessException(errorCode);
		}
	}


	/**
	 *
	 * @param param
	 * @param target
	 * @param errorCode
	 */
	public static void isEqualsObject(Object param, Object target, ErrorCode errorCode) {
		if (Objects.equals(param, target)) {
			throw new BusinessException(errorCode);
		}
	}

	/**
	 * @param num
	 */
	public static void greaterThanZero(Integer num, String msg) {
		if (Objects.isNull(num) || num <= 0) {
			ErrorCode errorCode = SystemErrorCode.PARAM_NULL_ERROR.formatDescription(msg);
			throw new BusinessException(errorCode);
		}
	}
	/**
	 * 判断集合是否为空
	 *
	 * @param collection
	 */
	public static void isNotEmpty(Collection collection) {
		isNotEmpty(collection, SystemErrorCode.PARAM_NULL_ERROR);
	}

	/**
	 * 判断集合是否为空
	 *
	 * @param collection
	 */
	public static void isNotEmpty(Collection collection, ErrorCode errorCode) {
		if (CollectionUtil.isEmpty(collection)) {
			throw new BusinessException(errorCode);
		}
	}

	/**
	 * 判断集合是否为空
	 *
	 * @param collection
	 */
	public static void isEmpty(Collection collection, ErrorCode errorCode) {
		if (CollectionUtil.isNotEmpty(collection)) {
			throw new BusinessException(errorCode);
		}
	}

	/**
	 * 判断集合是否为空
	 *
	 * @param collection
	 */
	public static void isNotEmpty(Collection collection, String msg) {
		if (CollectionUtil.isEmpty(collection)) {
			ErrorCode errorCode = SystemErrorCode.PARAM_NULL_ERROR.formatDescription(msg);
			throw new BusinessException(errorCode);
		}
	}

	/** */
	public static void collectionLength(Collection collection, Integer maxSize, String msg) {
		if (collection != null && collection.size() > maxSize) {
			ErrorCode errorCode = SystemErrorCode.PARAM_NULL_ERROR.formatDescription(msg);
			throw new BusinessException(errorCode);
		}
	}

}
