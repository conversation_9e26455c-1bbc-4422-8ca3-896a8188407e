package com.jdh.o2oservice.b2b.base.enums;

import java.util.Objects;

/**
 * 请填写类的描述
 * <AUTHOR> yintao
 * @date : 2019/11/29 20:42
 */
public enum YnStatusEnum {
    /** 有效 **/
    YES(1,"有效"),
    /** 失效 **/
    NO(0,"失效");

    /**
     * 有参构造器
     * @param code
     * @param desc
     */
    YnStatusEnum(Integer code, String desc){
        this.code = code;
        this.desc = desc;
    }

    /**
     * 状态码
     */
    private Integer code;

    /**
     * 描述
     */
    private String desc;
    /** */
    public Integer getCode() {
        return code;
    }

    /** */
    public String getDesc() {
        return desc;
    }

    /**
     *
     * @param code
     * @return
     */
    public static Boolean contains(Integer code){
        if (Objects.isNull(code)){
            return Boolean.FALSE;
        }
        for (YnStatusEnum value : values()) {
            if (Objects.equals(code, value.getCode())){
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

    /**
     *
     * @param code
     * @return
     */
    public static String getDescByCode(Integer code) {
        if (Objects.isNull(code)) {
            return "";
        }
        for (YnStatusEnum statusEnum : YnStatusEnum.values()) {
            if (Objects.equals(statusEnum.getCode(), code)) {
                return statusEnum.getDesc();
            }
        }
        return "";
    }
}
