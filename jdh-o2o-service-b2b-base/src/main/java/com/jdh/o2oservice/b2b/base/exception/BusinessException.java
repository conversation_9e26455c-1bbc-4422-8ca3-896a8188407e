package com.jdh.o2oservice.b2b.base.exception;


/**
 * 业务异常 通用
 * @author: yang<PERSON>yu
 * @date: 2023/11/23 6:14 下午
 * @version: 1.0
 */
public class BusinessException extends RuntimeException {

    private ErrorCode errorCode;

    public BusinessException(ErrorCode errorCode) {
        super(errorCode.toString());
        this.errorCode = errorCode;
    }

    public BusinessException(ErrorCode errorCode, String errorMessage) {
        super(errorCode.toString() + " - " + errorMessage);
        this.errorCode = errorCode;
    }

    private BusinessException(ErrorCode errorCode, String errorMessage,
                              Throwable cause) {
        super(errorCode.toString() + " - " + getMessage(errorMessage)
                + " - " + getMessage(cause), cause);

        this.errorCode = errorCode;
    }

    public static BusinessException asBusinessException(ErrorCode errorCode) {
        return new BusinessException(errorCode);
    }

    public static BusinessException asBusinessException(ErrorCode errorCode, String message) {
        return new BusinessException(errorCode, message);
    }

    public static BusinessException asBusinessException(ErrorCode errorCode, String message, Throwable cause) {
        if (cause instanceof BusinessException) {
            return (BusinessException) cause;
        }
        return new BusinessException(errorCode, message, cause);
    }

    public static BusinessException BusinessException(ErrorCode errorCode, Throwable cause) {
        if (cause instanceof BusinessException) {
            return (BusinessException) cause;
        }
        return new BusinessException(errorCode, null, cause);
    }

    public ErrorCode getErrorCode() {
        return this.errorCode;
    }


    private static String getMessage(Object obj) {
        if (obj == null) {
            return "";
        }

        if (obj instanceof Throwable) {
            return ((Throwable) obj).getMessage();
        } else {
            return obj.toString();
        }
    }
    
}
