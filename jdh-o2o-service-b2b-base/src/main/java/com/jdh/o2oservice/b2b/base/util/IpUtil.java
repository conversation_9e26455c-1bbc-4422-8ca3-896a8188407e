package com.jdh.o2oservice.b2b.base.util;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.net.*;
import java.util.Enumeration;
import java.util.concurrent.TimeUnit;

/**
 * IpUtil 类描述
 * <AUTHOR>
 * @version 2024/3/4 19:10
 **/
public class IpUtil {
    private static final String IP_SPLIT = "\\.";
    private static final String IPV6_SPLIT = ":";
    private static Cache<String, String> cache;

    public IpUtil() {
    }

    public static String getInet4Address() throws Exception {
        return cache.get("ipV4", () -> {
			Enumeration nis = NetworkInterface.getNetworkInterfaces();

			while(true) {
				Enumeration ias;
				do {
					NetworkInterface ni;
					do {
						do {
							do {
								if (!nis.hasMoreElements()) {
									return "";
								}

								ni = (NetworkInterface)nis.nextElement();
							} while(!ni.isUp());
						} while(ni.isLoopback());
					} while(ni.isVirtual());

					ias = ni.getInetAddresses();
				} while(!ias.hasMoreElements());

				while(ias.hasMoreElements()) {
					InetAddress ia = (InetAddress)ias.nextElement();
					if (!ia.isLoopbackAddress() && ia instanceof Inet4Address) {
						return ia.getHostAddress();
					}
				}
			}
		});
    }

    public static String getInet4AddressNoException() {
        try {
            return getInet4Address();
        } catch (Exception var1) {
            return "";
        }
    }

    public static String hexIp(String ip) {
        if (ip != null && ip.length() > 0 && ip.indexOf(":") > 0) {
            InetAddress inetAddr = null;

            try {
                inetAddr = InetAddress.getByName(ip);
            } catch (UnknownHostException var7) {
            }

            return inetAddr.getHostAddress();
        } else {
            StringBuilder sb = new StringBuilder();
            String[] arr$ = ip.split("\\.");
            int len$ = arr$.length;

            for(int i$ = 0; i$ < len$; ++i$) {
                String seg = arr$[i$];
                String h = Integer.toHexString(Integer.parseInt(seg));
                if (h.length() == 1) {
                    sb.append("0");
                }

                sb.append(h);
            }

            return sb.toString();
        }
    }

    public static String hexIpToString(String hexIp) {
        if (hexIp != null && hexIp.length() > 0 && hexIp.indexOf(":") > 0) {
            return hexIp;
        } else {
            StringBuilder sb = new StringBuilder();
            int i = 0;
            StringBuffer tmp = new StringBuffer();
            int len = hexIp.toCharArray().length;
            char[] arr$ = hexIp.toCharArray();
            int len$ = arr$.length;

            for(int i$ = 0; i$ < len$; ++i$) {
                char seg = arr$[i$];
                tmp.append(seg);
                ++i;
                if (i % 2 == 0) {
                    int val = Integer.parseInt(tmp.toString(), 16);
                    sb.append(val);
                    if (i < len) {
                        sb.append(".");
                    }

                    tmp = new StringBuffer();
                }
            }

            return sb.toString();
        }
    }

    public static boolean isIpV4(String ipStr) {
        if (ipStr != null && !ipStr.isEmpty()) {
            String regex = "^(((\\d{1,2})|(1\\d{2})|(2[0-4]\\d)|(25[0-5]))\\.){3}((\\d{1,2})|(1\\d{2})|(2[0-4]\\d)|(25[0-5]))$";
            return ipStr.matches(regex);
        } else {
            return false;
        }
    }

    private static boolean isReservedAddr(InetAddress inetAddr) {
        return inetAddr.isAnyLocalAddress() || inetAddr.isLinkLocalAddress() || inetAddr.isLoopbackAddress();
    }

    public static String getInet6Address() throws Exception {
        return cache.get("ipV6", () -> {
			Enumeration nis = NetworkInterface.getNetworkInterfaces();

			while(true) {
				Enumeration ias;
				do {
					NetworkInterface ni;
					do {
						do {
							do {
								if (!nis.hasMoreElements()) {
									return "";
								}

								ni = (NetworkInterface)nis.nextElement();
							} while(!ni.isUp());
						} while(ni.isLoopback());
					} while(ni.isVirtual());

					ias = ni.getInetAddresses();
				} while(!ias.hasMoreElements());

				while(ias.hasMoreElements()) {
					InetAddress ia = (InetAddress)ias.nextElement();
					if (!IpUtil.isReservedAddr(ia) && ia instanceof Inet6Address) {
						String ipAddr = ia.getHostAddress();
						int index = ipAddr.indexOf(37);
						if (index > 0) {
							ipAddr = ipAddr.substring(0, index);
						}

						return ipAddr;
					}
				}
			}
		});
    }

    public static String getIpAddr(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if(ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if(ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if(ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        if(ip != null && ip.length() > 0 && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }
        return ip;
    }

    public static String getInetAddressNoException(boolean preferIpV6) {
        try {
            String ipV4 = getInet4Address();
            String ipV6 = getInet6Address();
            if (preferIpV6 && StringUtils.isNoneBlank(new CharSequence[]{ipV6})) {
                return ipV6;
            }

            if (StringUtils.isNoneBlank(new CharSequence[]{ipV4})) {
                return ipV4;
            }

            if (StringUtils.isNoneBlank(new CharSequence[]{ipV6})) {
                return ipV6;
            }
        } catch (Exception var3) {
        }

        return "";
    }

    public static void main(String[] args) {
        System.out.println(hexIp("*************"));
        System.out.println(hexIp("2408:80e0:41fc:1093:4a7:b452:39cf:dd46"));
        System.out.println(hexIp("2408:80e0:41fc::4a7:b452:*************"));
        System.out.println("ipv6 perfer = " + getInetAddressNoException(true));
        System.out.println("ipv4 perfer = " + getInetAddressNoException(false));
    }

    static {
        cache = CacheBuilder.newBuilder().maximumSize(1L).expireAfterWrite(2L, TimeUnit.DAYS).build();
    }

}
