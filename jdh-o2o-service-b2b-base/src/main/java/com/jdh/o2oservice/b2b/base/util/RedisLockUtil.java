package com.jdh.o2oservice.b2b.base.util;

import cn.hutool.core.util.StrUtil;
import com.jd.jim.cli.Cluster;
import com.jdh.o2oservice.b2b.base.exception.SystemErrorCode;
import com.jdh.o2oservice.b2b.base.exception.SystemException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;


/**
 * Redis实现的简单分布式锁
 * <p></p>
 * Created by  tanwei3 on 2016/08/02.
 */
@Slf4j
@Component
public class RedisLockUtil {

    /**
     * jimClient
     */
    @Resource
    private Cluster jimClient;

    /**
     * 事物模板
     */
    @Resource
    private TransactionTemplate transactionTemplate;

    /** 默认重试次数 */
    private static final Integer DEFAULT_RETRY_TIMES = 3;

    /** 默认重试等待时间 */
    private static final Long DEFAULT_RETRY_MILLS = 1000L;

    /**
     * 非原子操作
     * 尝试获取分布式锁，最多重试retryTimes次
     *
     * @param lockKey    锁缓存key
     * @param value      锁缓存key
     * @param expireTime  锁缓存有效期
     * @param retryTimes  失败重试次数
     * @param retrySleepMills  失败重试单隔毫秒
     * @return boolean true:成功  false:失败
     */
    public boolean tryLockWithRetry(String lockKey, String value, long expireTime, int retryTimes, long retrySleepMills) {
        if(StrUtil.isBlank(lockKey) || StrUtil.isBlank(value) || expireTime <= 0 || retryTimes <= 0 || retrySleepMills <= 0){
            log.error("RedisLockImpl-->tryLockWithRetry| invalid parameter");
            return false;
        }
        while(retryTimes > 0){
            if(tryLock(lockKey, value, expireTime)){
                log.debug("RedisLockImpl-->tryLockWithRetry get lock success| retryTimes:{}", retryTimes);
                return true;
            }else{
                try {
                    TimeUnit.MILLISECONDS.sleep(retrySleepMills);
                } catch (InterruptedException e) {
                    log.error("RedisLockImpl-->tryLockWithRetry thread InterruptedException");
                }
            }

            retryTimes--;
        }

        //检测一下ttl是否是-1,表明key未设置过期时间
        try {
            Long ttlVal = jimClient.ttl(lockKey);
            if(ttlVal !=null && ttlVal == -1){
                jimClient.expire(lockKey, expireTime, TimeUnit.SECONDS);
            }
        } catch (Exception e) {
            log.error("RedisLockImpl-->tryLockWithRetry reset expire execute error! | lockKey={}", lockKey, e);
        }

        return false;
    }


    /**
     * 尝试获取分布式锁，最多重试retryTimes次
     * @param lockKey 锁缓存key
     * @param requestId 同一应用内的唯一ID,
     *                  使用com.jd.medicine.base.common.logging.rxlog.UniqRequestIdGen.generateReqId()方法生成
     *                  可以传任意非空和空串值
     *                  但【强烈建议】使用requestId,以便以后自己的锁只有自己能解
     * @param expireTime 锁缓存有效期
     * @param timeUnit 锁缓存有效期时间单位
     * @param retryTimes 失败重试次数
     * @param retrySleepMills 失败重试单隔【毫秒】
     * @return
     */
    public boolean tryLockWithRetry(String lockKey, String requestId, long expireTime, TimeUnit timeUnit, int retryTimes, long retrySleepMills) {
        if(StrUtil.isBlank(lockKey) || StrUtil.isBlank(requestId) || expireTime <= 0 || timeUnit == null
                || retryTimes <= 0 || retrySleepMills <= 0){
            log.error("RedisLockImpl --> tryLockWithRetry | invalid parameter");
            return false;
        }
        while(retryTimes > 0){
            if(tryLock(lockKey, requestId, expireTime, timeUnit)){
                log.debug("RedisLockImpl --> tryLockWithRetry get lock success | retryTimes:{}", retryTimes);
                return true;
            }else{
                try {
                    TimeUnit.MILLISECONDS.sleep(retrySleepMills);
                } catch (InterruptedException e) {
                    log.error("RedisLockImpl --> tryLockWithRetry thread InterruptedException");
                }
            }

            retryTimes--;
        }

        return false;
    }

    /**
     * 尝试获取分布式锁，最多重试retryTimes次 redis指令执行异常会抛出
     * @param lockKey 锁缓存key
     * @param requestId 同一应用内的唯一ID,
     *                  使用com.jd.medicine.base.common.logging.rxlog.UniqRequestIdGen.generateReqId()方法生成
     *                  可以传任意非空和空串值
     *                  但【强烈建议】使用requestId,以便以后自己的锁只有自己能解
     * @param expireTime 锁缓存有效期
     * @param timeUnit 锁缓存有效期时间单位
     * @param retryTimes 失败重试次数
     * @param retrySleepMills 失败重试单隔【毫秒】
     * @return
     */
    public boolean tryLockWithRetryWithThrow(String lockKey, String requestId, long expireTime, TimeUnit timeUnit, int retryTimes, long retrySleepMills) {
        if(StrUtil.isBlank(lockKey) || StrUtil.isBlank(requestId) || expireTime <= 0 || timeUnit == null
                || retryTimes <= 0 || retrySleepMills <= 0){
            log.error("RedisLock->tryLockWithRetryWithThrow|invalid parameter");
            return false;
        }
        while(retryTimes > 0){
            if(tryLockWithThrow(lockKey, requestId, expireTime, timeUnit)){
                log.debug("RedisLock->tryLockWithRetryWithThrow|get lock success|retryTimes:{}", retryTimes);
                return true;
            }else{
                try {
                    TimeUnit.MILLISECONDS.sleep(retrySleepMills);
                } catch (InterruptedException e) {
                    log.error("RedisLock->tryLockWithRetryWithThrow|thread InterruptedException");
                }
            }

            retryTimes--;
        }

        return false;
    }

    /**
     * 非原子操作
     * 尝试获取锁  如果锁可用   立即返回true，  否则返回false
     *  禁止使用永久锁,因为程序崩溃或者断电时有可能无法执行到finally,也就无法释放锁了
     * @param lockKey    锁缓存key
     * @param value      锁缓存key
     * @param expireTime  锁缓存有效期
     * @return 是否获得锁
     */
    public boolean tryLock(String lockKey, String value, long expireTime) {
        if(StrUtil.isBlank(lockKey) || StrUtil.isBlank(value) || expireTime <= 0){
            log.error("RedisLockImpl-->tryLock | invalid parameter");
            return false;
        }
        try {
            log.trace("RedisLockImpl-->tryLock | lockKey:{}, value:{}, expireTime:{}",lockKey,value,expireTime);

            boolean successFlag = jimClient.setNX(lockKey, value);
            if (successFlag) {
                boolean expireFlag = jimClient.expire(lockKey, expireTime, TimeUnit.SECONDS);
                log.debug("RedisLockImpl-->tryLock set expire| expireFlag:{}, lockKey:{}, value:{}, expireTime:{}", expireFlag,lockKey,value,expireTime);
                return true;
            } else {
                // 存在锁,此时有可能前一个节点已经释放锁,或者其他节点获取了锁
                if(log.isDebugEnabled()){
                    //里面有jimClient.get操作，所以加一下判断
                    log.debug("RedisLockImpl-->tryLock lock conflict| lockKey:{}, value:{}",lockKey,jimClient.get(lockKey));
                }
                return false;
            }

        } catch (RuntimeException e) {
            log.error("RedisLockImpl-->tryLock execute error", e);
        }
        return false;
    }

    /**
     * 尝试获取锁  如果锁可用   立即返回true，  否则返回false
     * @param lockKey
     * @param requestId 同一应用内的唯一ID,
     *                  使用com.jd.medicine.base.common.logging.rxlog.UniqRequestIdGen.generateReqId()方法生成
     *                  可以传任意非空值
     *                  但【强烈建议】使用requestId,以便以后自己的锁只有自己能解
     * @param expireTime 超时时间
     * @param timeUnit 超时时间单位
     * @return
     */
    public boolean tryLock(String lockKey, String requestId, long expireTime, TimeUnit timeUnit) {
        if(StrUtil.isBlank(lockKey) || StrUtil.isBlank(requestId) || expireTime <= 0 || timeUnit == null){
            log.error("RedisLockImpl --> tryLock | invalid parameter");
            return false;
        }
        try {
            log.trace("RedisLockImpl --> tryLock | lockKey:{}, value:{}, expireTime:{}, timeUnit:{}",
                    lockKey, requestId, expireTime, timeUnit);
            // exist:false  不存在lockKey时才能操作 lockKey
            return jimClient.set(lockKey, requestId, expireTime, timeUnit, false);
        } catch (Exception e) {
            log.error("RedisLockImpl --> tryLock execute error", e);
        }
        return false;
    }

    /**
     * 尝试获取锁  如果锁可用   立即返回true，如果执行redis指令异常则抛出，否则返回false
     * @param lockKey
     * @param requestId 同一应用内的唯一ID,
     *                  使用com.jd.medicine.base.common.logging.rxlog.UniqRequestIdGen.generateReqId()方法生成
     *                  可以传任意非空和空串值
     *                  但【强烈建议】使用requestId,以便以后自己的锁只有自己能解
     * @param expireTime 超时时间
     * @param timeUnit 超时时间单位
     * @return
     */
    public boolean tryLockWithThrow(String lockKey, String requestId, long expireTime, TimeUnit timeUnit) {
        if(StrUtil.isBlank(lockKey) || StrUtil.isBlank(requestId) || expireTime <= 0 || timeUnit == null){
            log.error("RedisLock->tryLock|invalid parameter");
            return false;
        }
        try {
            log.trace("RedisLock->tryLock|lockKey:{}, value:{}, expireTime:{}, timeUnit:{}",
                    lockKey, requestId, expireTime, timeUnit);
            // exist:false  不存在lockKey时才能操作 lockKey
            return jimClient.set(lockKey, requestId, expireTime, timeUnit, false);
        } catch (Exception e) {
            log.error("RedisLock->tryLock|execute error", e);
            throw e;
        }
    }


    /**
     * 释放锁
     * @param lockKey
     */
    public void unLock(String lockKey) {
        if(StrUtil.isBlank(lockKey)){
            log.error("RedisLockImpl-->unLock| invalid parameter");
            return;
        }

        try {
            jimClient.del(lockKey);
            if(!jimClient.exists(lockKey)) {
                //删除成功
                log.debug("RedisLockImpl-->unLock release lock success| lockKey:{}", lockKey);
            } else {
                log.error("RedisLockImpl-->unLock release lock error| lockKey:{}", lockKey);
            }
        } catch (RuntimeException e) {
            log.error("RedisLockImpl-->unLock execute error ", e);
        }
    }

    /**
     * 根据预期值释放锁
     *
     * @param lockKey       锁缓存key
     * @param expectValue   锁缓存期望值
     * @return  释放结果   不满足预期值则返回 -1，解锁成功(非法入参) 1，解锁失败 0
     */
    public int unLock(String lockKey, String expectValue) {
        try {
            if(StrUtil.isBlank(lockKey) || StrUtil.isBlank(expectValue)){
                log.error("RedisLock->unLock invalid parameter");
                return 1;
            }

            String value = jimClient.get(lockKey);

            if(StrUtil.isBlank(value)){
                log.warn("RedisLockImpl-->unLock, value is null,  key:{}, expect:{}", lockKey, expectValue);
                return 1;
            }

            if (!expectValue.equals(value)) {
                if (log.isWarnEnabled()) {
                    log.warn("RedisLockImpl-->unLock, unexpect value for key:{}, expect:{}, actual:{}", lockKey, expectValue, value);
                }
                return -1;
            }
            unLock(lockKey);
            return 1;
        } catch (Throwable throwable) {
            log.error("RedisLockImpl-->unLock execute error, key:{}, expect:{}", lockKey, expectValue, throwable);
            return 0;
        }
    }
    /**
     * 加锁执行supplier，没有获取到锁的资格，则抛出BaseErrorCode.SYSTEM_BUSY_ERROR
     * supplier会在一个事务中执行
     * @param supplier
     * @param redisKey
     * @param expireTime
     * @param <R>
     * @return
     */
    public <R>R lockExecuteInTransaction(Supplier<R> supplier, String redisKey, long expireTime){
        String uuid = UUID.randomUUID().toString();
        boolean lock = tryLock(redisKey, uuid, expireTime, TimeUnit.SECONDS);
        try {
            if (lock) {
                return transactionTemplate.execute(status -> supplier.get());
            }else{
                throw new SystemException(SystemErrorCode.REDIS_LOCK_GET_FAIL);
            }
        }finally {
            unLock(redisKey, uuid);
        }
    }


    /**
     * 加锁执行supplier，没有获取到锁的资格，则抛出BaseErrorCode.SYSTEM_BUSY_ERROR
     * supplier会在一个事务中执行，先加锁再获取事务，避免锁释放后，事务提交前这段事件出现的并发问题
     * @param supplier
     * @param redisKey
     * @param expireTime
     * @param retryTimes
     * @param retrySleepMills
     * @param <R>
     * @return
     */
    public <R>R lockExecuteInTransaction(Supplier<R> supplier, String redisKey, long expireTime, Integer retryTimes, Long retrySleepMills){
        String uuid = UUID.randomUUID().toString();
        retryTimes = Objects.isNull(retryTimes) ? DEFAULT_RETRY_TIMES : retryTimes;
        retrySleepMills = Objects.isNull(retrySleepMills) ? DEFAULT_RETRY_MILLS : retrySleepMills;
        boolean lock = tryLockWithRetry(redisKey, uuid, expireTime, TimeUnit.SECONDS, retryTimes, retrySleepMills);
        try {
            if (lock) {
                return transactionTemplate.execute(status -> supplier.get());
            }else{
                throw new SystemException(SystemErrorCode.REDIS_LOCK_GET_FAIL);
            }
        }finally {
            unLock(redisKey, uuid);
        }
    }

}
