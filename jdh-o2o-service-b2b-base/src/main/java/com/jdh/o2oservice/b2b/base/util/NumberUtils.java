package com.jdh.o2oservice.b2b.base.util;

import org.apache.commons.lang3.StringUtils;

import java.text.DecimalFormat;

/**
 * <AUTHOR>
 * @description
 * @date 2025/1/7
 */
public class NumberUtils {


    /**
     * 将给定的数字字符串转换为科学计数法表示形式。
     * @param numStr 需要转换的数字字符串。
     * @return 转换后的科学计数法表示形式的字符串。
     */
    public static String toScientificNotation(String numStr) {
        try {
            if (StringUtils.isBlank(numStr)){
                return numStr;
            }
            // 将字符串转换为浮点数
            double num = Double.parseDouble(numStr);
            // 创建一个DecimalFormat实例，并设置科学计数法格式
            DecimalFormat df = new DecimalFormat("0.00E0");
            // 格式化数字
            String formatted = df.format(num);

            // 将E替换为×10^，并将上标数字替换为相应的Unicode字符
            formatted = formatted.replace("E", "×10^");
            formatted = formatted.replace("+", ""); // 去掉正号
            formatted = convertToSuperscript(formatted);

            return formatted;
        } catch (NumberFormatException e) {
            return numStr;
        }
    }

    /**
     * 将字符串中的数字转换为上标形式，并去除'^'符号。
     * @param str 需要转换的字符串。
     * @return 转换后的字符串。
     */
    private static String convertToSuperscript(String str) {
        // 上标数字的Unicode字符
        char[] superscriptDigits = {'⁰', '¹', '²', '³', '⁴', '⁵', '⁶', '⁷', '⁸', '⁹'};
        StringBuilder result = new StringBuilder();
        boolean isExponentPart = false;
        for (char c : str.toCharArray()) {
            if (c == '^') {
                isExponentPart = true;
                result.append(c);
            } else if (isExponentPart && Character.isDigit(c)) {
                // 将数字字符转换为上标字符
                result.append(superscriptDigits[c - '0']);
            } else {
                result.append(c);
            }
        }
        return result.toString().replace("^", "");
    }

}
