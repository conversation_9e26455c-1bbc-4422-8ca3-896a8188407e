package com.jdh.o2oservice.b2b.base.config;

import com.jd.pioneer.hand.of.god.servlet.HandServlet;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 《上帝之手》配置
 *
 * <AUTHOR>
 * @date 2023/09/20
 */
@Configuration
public class HandOfGodConfig {

    /**
     * 上帝之手
     *
     * @return
     */
    @Bean
    public ServletRegistrationBean jdJspServletHandServlet() {
        ServletRegistrationBean registration = new ServletRegistrationBean();
        registration.setServlet(new HandServlet());
        registration.setName("handOfGod");
        registration.addUrlMappings("/hand-of-god/*");
        registration.addInitParameter("loginUsername", "PhTcp3lz@^A3VKZc");
        registration.addInitParameter("loginPassword", "O9f8Z^d8Mk6esLyn");
        registration.setLoadOnStartup(5);
        return registration;
    }

}
