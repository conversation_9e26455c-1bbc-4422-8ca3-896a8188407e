package com.jdh.o2oservice.b2b.base.config;

import com.jd.jim.cli.Cluster;
import com.jd.jim.cli.ReloadableJimClientFactory;
import com.jd.jim.cli.config.ConfigClient;
import com.jd.jim.cli.config.client.ConfigLongPollingClientFactoryBean;
import org.springframework.cache.annotation.CachingConfigurerSupport;
import org.springframework.cache.interceptor.CacheErrorHandler;
import org.springframework.cache.interceptor.LoggingCacheErrorHandler;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.core.env.PropertyResolver;

/**
 * redis配置
 *
 * <AUTHOR>
 * @date 2023/09/13
 */
@Configuration
public class RedisConfig extends CachingConfigurerSupport implements EnvironmentAware {

    /**
     * propertyResolver
     */
    private PropertyResolver propertyResolver;

    /**
     * 设置环境
     *
     * @param environment 环境
     */
    @Override
    public void setEnvironment(Environment environment) {
        this.propertyResolver = environment;
    }

    /**
     * 配置客户端
     *
     * @return {@link ConfigLongPollingClientFactoryBean}
     */
    @Bean("configClient")
    public ConfigClient configClient(){
        ConfigLongPollingClientFactoryBean factoryBean = new ConfigLongPollingClientFactoryBean();
        factoryBean.setServiceEndpoint(this.propertyResolver.getProperty("redis.endpoint"));
        return factoryBean.create();
    }

    /**
     * 客户端
     *
     * @return {@link Cluster}
     */
    @Bean(name = "jimClient")
    public Cluster jimClient() {
        ReloadableJimClientFactory factory = new ReloadableJimClientFactory();
        factory.setConfigClient(configClient());
        factory.setJimUrl(this.propertyResolver.getProperty("redis.jimUrl"));
        factory.setConfigId("0");
        return factory.getClient();
    }

    /**
     * 缓存错误处理程序,捕获SpringCache的异常，不影响主流
     *
     * @return {@link CacheErrorHandler}
     */
    @Bean
    @Override
    public CacheErrorHandler errorHandler() {
        return new LoggingCacheErrorHandler();
    }
}