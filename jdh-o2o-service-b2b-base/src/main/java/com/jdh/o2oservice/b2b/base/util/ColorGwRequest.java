package com.jdh.o2oservice.b2b.base.util;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Slf4j
public class ColorGwRequest {

    /**
     * request
     */
    private Map request;

    /**
     * jsf gw请求
     *
     * @param request 请求
     */
    public ColorGwRequest(Map request) {
        this.request = request;
    }

    /**
     * 用户pin
     */
    public String getPin() {
        return getString(GwConstants.PIN);
    }

    /**
     * 请求业务数据,json字符串
     */
    public String getBody() {
        return getString(GwConstants.BODY);
    }

    /**
     * 用户请求ip
     */
    public String getIp() {
        return getString(GwConstants.IP);
    }

    /**
     * 用户请求浏览器 User-Agent
     */
    public String getUserAgent() {
        return getString(GwConstants.USER_AGENT);
    }

    /**
     * 转发请求唯一标误
     */
    public String getRequestId() {
        return getString(GwConstants.REQUEST_ID);
    }

    /**
     * 用户请求cookie
     */
    public String getCookie() {
        return getString(GwConstants.COOKIE);
    }

    /**
     * 将body内容转成对象
     */
    public <T> T toDomain(Class<T> cls) {
        String body = getBody();
        return JSON.parseObject(body, cls);
    }


    /**
     * 通用key从request map中获取对应的String值,如果为空返回null
     */
    private String getString(String key) {
        if (key == null || request == null) {
            return null;
        }
        Object val = request.get(key);
        if (val == null) {
            return null;
        } else {
            return Convert.convert(String.class,val);
        }
    }

}
