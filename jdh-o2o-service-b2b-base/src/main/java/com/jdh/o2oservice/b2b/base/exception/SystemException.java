package com.jdh.o2oservice.b2b.base.exception;


/**
 * 系统异常抽象非业务异常
 * @author: yang<PERSON><PERSON>
 * @date: 2023/11/23 6:14 下午
 * @version: 1.0
 */
public class SystemException extends RuntimeException {

    private ErrorCode errorCode;

    public SystemException(ErrorCode errorCode) {
        super(errorCode.toString());
        this.errorCode = errorCode;
    }

    public SystemException(ErrorCode errorCode, String errorMessage) {
        super(errorCode.toString() + " - " + errorMessage);
        this.errorCode = errorCode;
    }

    private SystemException(ErrorCode errorCode, String errorMessage,
                            Throwable cause) {
        super(errorCode.toString() + " - " + getMessage(errorMessage)
                + " - " + getMessage(cause), cause);

        this.errorCode = errorCode;
    }


    public ErrorCode getErrorCode() {
        return this.errorCode;
    }


    private static String getMessage(Object obj) {
        if (obj == null) {
            return "";
        }

        if (obj instanceof Throwable) {
            return ((Throwable) obj).getMessage();
        } else {
            return obj.toString();
        }
    }
    
}
