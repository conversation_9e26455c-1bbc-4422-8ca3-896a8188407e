package com.jdh.o2oservice.b2b.base.model;

public enum DomainEnum implements DomainCode{
    /** 领域拆分 */
    BASE("base", 0,"基础支撑域"),
    PROMISE("promise", 1,"履约域"),
    PROVIDER("provider", 2,"商家域"),
    TRADE("trade", 3,"交易域"),
    ;

    /**
     * constructor
     */
    DomainEnum(String code, Integer index, String name) {
        this.code = code;
        this.name = name;
        this.index = index;
    }

    /** */
    private String code;
    /** */
    private Integer index;
    /** */
    private String name;

    /** */
    @Override
    public String getName() {
        return name;
    }

    /** */
    @Override
    public String getCode() {
        return code;
    }

    /** */
    public Integer getIndex() {
        return index;
    }

    /**
     * getByCode
     *
     * @param domainCode 域代码
     * @return {@link DomainEnum}
     */
    public static DomainEnum getByCode(String domainCode){
        for (DomainEnum domainEnum : DomainEnum.values()) {
            if(domainEnum.getCode().equals(domainCode)){
                return domainEnum;
            }
        }
        return null;
    }
}
