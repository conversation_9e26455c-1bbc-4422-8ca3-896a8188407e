package com.jdh.o2oservice.b2b.base.exception;

import org.slf4j.helpers.MessageFormatter;

/**
 * ErrorCode抽象类
 * @author: yang<PERSON>yu
 * @date: 2023/4/4 2:22 下午
 * @version: 1.0
 */
public interface AbstractErrorCode extends ErrorCode {


    /**
     * 格式化description
     * @param params
     */
    default ErrorCode formatDescription(Object... params){
        String description = MessageFormatter.arrayFormat(getDescription(), params).getMessage();
        return new DynamicErrorCode(getCode(), description);
    }

}
