package com.jdh.o2oservice.b2b.base.annotation;
import cn.hutool.core.bean.BeanPath;
import com.jdh.o2oservice.b2b.base.enums.OpTypeEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 日志记录注解
 *
 * <AUTHOR>
 * @date 2025/01/13
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD})
public @interface OperationLog {

    /**
     * 操作类型
     * @return true
     */
    OpTypeEnum operationType();

    /**
     * 适用新增、更新都是一个接口的情况
     * 如果对象中字段非空,表示更新,为空表示新增,比如save方法判断入参有主键新增,无主键更新
     * 操作类型动态判断表达式,属性通过cn.hutool.core.bean.BeanPath工具获取
     * @return true
     */
    String[] paramJudgeOperationTypeExpress() default {};

    /**
     * 操作描述
     * @return true
     */
    String operationDesc() default "";

    /**
     * 通过入参获取业务主键表达式,属性通过cn.hutool.core.bean.BeanPath工具获取
     * 如保存商品入参对象skuParam获取商品id,则表达式为skuParam.skuId
     * 如果入参存在多个场景获取，会顺序获取，只要值不为空就保存
     * @see BeanPath
     */
    String[] recordParamBizIdExpress() default {};

    /**
     * 通过出参获取业务主键表达式,属性通过cn.hutool.core.bean.BeanPath工具获取
     * 如保存商品出参对象skuRet获取商品id,则表达式为skuRet.skuId
     * @see BeanPath
     */
    String[] recordResultBizIdExpress() default {};

    /**
     * 适用结果个性化返回对象
     * 操作类型动态判断表达式,属性通过cn.hutool.core.bean.BeanPath工具获取
     * @return true
     */
    String[] resultJudgeExpress() default {};

    /**
     * 适用结果判断是否成功标识
     * 操作类型动态判断表达式,属性通过cn.hutool.core.bean.BeanPath工具获取
     * @return true
     */
    String[] resultSuccessCode() default {};
}
