package com.jdh.o2oservice.b2b.base.util;

import com.jd.jsf.gd.util.JsonUtils;
import com.jdh.o2oservice.b2b.base.constant.CommonConstant;
import com.jdh.o2oservice.b2b.base.exception.BusinessErrorCode;
import com.jdh.o2oservice.b2b.base.exception.BusinessException;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.retry.backoff.UniformRandomBackOffPolicy;
import org.springframework.retry.policy.SimpleRetryPolicy;
import org.springframework.retry.support.RetryTemplate;

import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import java.util.function.Predicate;
import java.util.function.Supplier;

/**
 * 重试工具
 * RetryableOperator
 * @date 2024-09-02 10:21
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class RetryableOperator<T> {

    /**
     * 日志
     */
    private static final Logger log = LoggerFactory.getLogger(RetryableOperator.class);

    /**
     * 异常
     */
    private Throwable t;

    /**
     * res
     */
    private T res;

    /**
     * condition
     */
    private boolean condition;

    /**
     * 日志
     */
    private Logger logger;

    /**
     * 重试
     * @param retryer
     * @param assertor
     * @param <T>
     * @return
     */
    public static <T> RetryableOperator<T> of(RetryPolicy retryPolicy,
                                                                              Supplier<T> retryer,
                                                                              Predicate<T> assertor) {
        RetryableOperator<T> ret = new RetryableOperator<>();
        AtomicInteger count = new AtomicInteger(0);
        try {
            ret.res = defaultRetryTemplate(retryPolicy).execute(
                    c -> {
                        if (ret.condition) {
                            log.info("[retry count ->]", count.incrementAndGet());
                        }
                        ret.t = null;
                        T t = retryer.get();
                        if (assertor != null) {
                            if (assertor.test(t)) {
                                ret.condition = true;
                                ret.res=t;
                                throw new BusinessException(BusinessErrorCode.RETRY_ERROR);
                            } else {
                                ret.condition = false;
                            }
                        }
                        ret.t = null;
                        return t;
                    },
                    c -> {
                        log.error("[total retry count->{}]", c.getRetryCount(), c.getLastThrowable());
                        ret.t = c.getLastThrowable();
                        return null;
                    }
            );
        } catch (Throwable t) {
            log.debug("debug-debug-debug", t);
            if (!BusinessException.class.isInstance(t) ) {
                ret.t = t;
            }
        }
        return ret;
    }

    /**
     * 是否成功
     */
    public boolean isSucc() {
        return this.t == null && !this.condition;
    }

    /**
     * get
     * @return
     */
    public T get() {
        if (isSucc()) {
            return res;
        } else {
            throw BusinessException.asBusinessException(BusinessErrorCode.RPC_JSF_ERROR,t.getMessage());
        }
    }

    /**
     * 判断结果是否成功，如果成功那么只需指定的操作
     */
    public RetryableOperator<T> onSucc(Consumer<? super T> consumer) {
        if (isSucc()) {
            if (logger != null && res != null) {
                logger.debug("[resp->{}]", JsonUtils.toJSONString(res));
            }
            consumer.accept(this.res);
        }
        return this;
    }

    /**
     * 如果出现调用失败或者异常
     */
    public RetryableOperator<T> onFail(Consumer<? super T> consumer) {
        if (this.condition) {
            if (logger != null) {
                logger.error("[retry condition is reached]");
            }
            consumer.accept(this.res);
        }
        return this;
    }

    /**
     * 如果出现调用异常
     */
    public RetryableOperator<T> onEx(Consumer<Throwable> consumer) {
        if (this.t != null) {
            consumer.accept(this.t);
        }
        return this;
    }

    /**
     * 如果调用出现异常，那么抛出异常
     */
    public RetryableOperator<T> throwOnEx() throws Throwable {
        if (this.t != null) {
            throw t;
        }
        return this;
    }

    /**
     * 记录成功信息
     */
    public RetryableOperator<T> logSucc(String format, Object... object) {
        if (isSucc() && logger != null) {
            logger.info(format, object);
        }
        return this;
    }

    /**
     * 记录失败信息
     */
    public RetryableOperator<T> logFail(String format, Object... object) {
        if (condition && logger != null) {
            logger.error(format, object);
        }
        return this;
    }

    /**
     * 记录失败信息
     */
    public RetryableOperator<T> logEx(String format, Object... object) {
        if (t != null && logger != null) {
            logger.error(format, object);
        }
        return this;
    }

    /**
     * RetryTemplate
     * @return
     */
    private static RetryTemplate defaultRetryTemplate(RetryPolicy retryPolicy) {
        //重试次数
        SimpleRetryPolicy policy = new SimpleRetryPolicy();
        if(Objects.nonNull(retryPolicy) && Objects.nonNull(retryPolicy.getRetryTimes())) {
            policy.setMaxAttempts(retryPolicy.getRetryTimes());
        }else {
          policy.setMaxAttempts(CommonConstant.THREE); //默认3次
        }
        // 设置间隔策略 500ms-1500ms随机
        UniformRandomBackOffPolicy backOffPolicy = new UniformRandomBackOffPolicy();

        // 初始化重试模板
        RetryTemplate template = new RetryTemplate();
        template.setRetryPolicy(policy);
        template.setBackOffPolicy(backOffPolicy);
        return template;
    }

    /**
     * 重试工具
     * RetryableOperator
     * @date 2024-09-02 10:21
     * <AUTHOR>
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RetryPolicy {

        /**
         * 重试次数
         */
        private Integer retryTimes;
    }

    public static void main(String[] args) {
        int i = 100;
       RetryableOperator<Integer> integerRetryableOperator =RetryableOperator.of(RetryPolicy.builder().retryTimes(60).build(),
                () -> Integer.valueOf(i),
                (r) -> r == 100);

        System.out.println(integerRetryableOperator.isSucc());

    }

}
