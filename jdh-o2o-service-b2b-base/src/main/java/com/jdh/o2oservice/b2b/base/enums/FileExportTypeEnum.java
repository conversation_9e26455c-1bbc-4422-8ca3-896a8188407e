package com.jdh.o2oservice.b2b.base.enums;

import com.google.common.collect.Maps;
import com.jdh.o2oservice.b2b.base.factory.FileOperationType;

import java.util.Map;

/**
 * 文件导出类型
 * @author: yang<PERSON><PERSON>
 * @date: 2024/3/20 5:15 下午
 * @version: 1.0
 */
public enum FileExportTypeEnum implements FileOperationType {

    ENTERPRISE_BILL_DETAIL_EXPORT("enterpriseBillDetailExport", "企业账单明细导出", 10000),
    ;

    FileExportTypeEnum(String type, String desc, Integer maxCount) {
        this.type = type;
        this.desc = desc;
        this.maxCount = maxCount;
    }

    private String type;
    private String desc;
    private Integer maxCount;

    @Override
    public String getType() {
        return type;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getMaxCount() {
        return maxCount;
    }

    private static final Map<String, FileExportTypeEnum> EXPORT_MAP = Maps.newHashMap();
    static {
        for (FileExportTypeEnum value : values()) {
            EXPORT_MAP.put(value.type, value);
        }
    }
    public static FileExportTypeEnum findType(String type){
        return  EXPORT_MAP.get(type);
    }
}
