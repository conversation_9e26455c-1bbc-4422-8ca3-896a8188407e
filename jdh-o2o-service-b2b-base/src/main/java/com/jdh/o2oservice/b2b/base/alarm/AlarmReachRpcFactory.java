package com.jdh.o2oservice.b2b.base.alarm;
import com.jdh.o2oservice.b2b.base.enums.AlarmPolicyTypeEnum;
import com.jdh.o2oservice.b2b.base.exception.BusinessErrorCode;
import com.jdh.o2oservice.b2b.base.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @ClassName AlarmReachRpcFactory
 * @Description
 * <AUTHOR>
 * @Date 2024/5/31 11:48
 **/
@Component
@Slf4j
public class AlarmReachRpcFactory {

    /**
     * 注入alarmReachMap
     */
    @Autowired
    private Map<String, AlarmReachRpc> alarmReachMap;

    /**
     * router
     * @param typeEnum
     * @return
     */
    public AlarmReachRpc createAlarmReachRpc(AlarmPolicyTypeEnum typeEnum) {
        if (alarmReachMap == null || typeEnum == null) {
            throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR);
        }
        // 如果策略类无实现，默认UMP报警
        return alarmReachMap.getOrDefault(typeEnum.getBeanName(), alarmReachMap.get(AlarmPolicyTypeEnum.UMP.getBeanName()));
    }

}