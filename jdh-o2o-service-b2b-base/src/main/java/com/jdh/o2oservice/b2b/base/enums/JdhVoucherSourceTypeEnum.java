package com.jdh.o2oservice.b2b.base.enums;

/**
 * JDH服务单来源类型枚举
 *  0 - 京东订单、1- 外部订单、2- 兑换
 * <AUTHOR>
 * @date 2023/12/12
 */
public enum JdhVoucherSourceTypeEnum {

    /**
     * 京东订单
     */
    JD_ORDER(0, "京东订单"),

    /**
     * 外部订单
     */
    EXT_ORDER(1, "外部订单"),

    /**
     * 兑换
     */
    EXCHANGE(2, "兑换"),
    ;

    /**
     * @param type
     * @param desc
     */
    JdhVoucherSourceTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    /**
     *
     */
    private Integer type;

    /**
     *
     */
    private String desc;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static Boolean checkType(Integer sourceType){
        for (JdhVoucherSourceTypeEnum value : JdhVoucherSourceTypeEnum.values()) {
            if(value.getType().equals(sourceType)){
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

}
