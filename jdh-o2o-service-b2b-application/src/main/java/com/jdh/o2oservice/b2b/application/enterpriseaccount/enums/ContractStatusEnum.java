package com.jdh.o2oservice.b2b.application.enterpriseaccount.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;


import java.util.Objects;

/**
 * 合同状态
 * @Author: lwm
 * @Date: 2025/5/13 3:33 下午
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum ContractStatusEnum {

    INIT(1, "拟定"),
    WAIT_(2, "待批"),
    SETTLED(3, "批准"),
    SIGNED(4, "已签署"),
    REJECTED(5, "已拒绝"),
    CANCELLED(6, "已取消"),
    DELETED(9, "已删除"),
    ;

    /**
     *
     */
    private Integer type;
    /**
     *
     */
    private String desc;

    /**
     *
     * @param type
     * @return
     */
    public static String getEnumDescByType(Integer type) {
        if (Objects.isNull(type)) {
            return "";
        }
        for (ContractStatusEnum statusEnum : ContractStatusEnum.values()) {
            if (Objects.equals(statusEnum.getType(), type)) {
                return statusEnum.getDesc();
            }
        }
        return "";
    }

}
