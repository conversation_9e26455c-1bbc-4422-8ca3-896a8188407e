package com.jdh.o2oservice.b2b.application.enterprisesku.impl;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.b2b.application.enterpriseaccount.B2bEnterpriseAccountApplication;
import com.jdh.o2oservice.b2b.application.enterprisesku.B2bEnterpriseSkuApplication;
import com.jdh.o2oservice.b2b.application.enterprisesku.convert.B2bEnterpriseSkuApplicationConverter;
import com.jdh.o2oservice.b2b.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.b2b.base.exception.BusinessErrorCode;
import com.jdh.o2oservice.b2b.base.exception.BusinessException;
import com.jdh.o2oservice.b2b.base.exception.DynamicErrorCode;
import com.jdh.o2oservice.b2b.base.exception.SystemErrorCode;
import com.jdh.o2oservice.b2b.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.b2b.common.response.PageDto;
import com.jdh.o2oservice.b2b.domain.enterprise.model.JdhB2bEnterpriseContract;
import com.jdh.o2oservice.b2b.domain.enterprise.repository.JdhB2bEnterpriseContractRepository;
import com.jdh.o2oservice.b2b.domain.enterprise.repository.query.JdhB2bEnterpriseContractQuery;
import com.jdh.o2oservice.b2b.domain.enterprisesku.model.JdhB2bEnterpriseSku;
import com.jdh.o2oservice.b2b.domain.enterprisesku.model.JdhEnterpriseSkuIdentifier;
import com.jdh.o2oservice.b2b.domain.enterprisesku.repository.JdhB2bEnterpriseSkuRepository;
import com.jdh.o2oservice.b2b.domain.enterprisesku.repository.query.JdhB2bEnterpriseSkuQuery;
import com.jdh.o2oservice.b2b.domain.enterprisesku.rpc.O2oProductJsfExportRpc;
import com.jdh.o2oservice.b2b.domain.enterprisesku.rpc.bo.JdhSkuResBo;
import com.jdh.o2oservice.b2b.export.enterprisesku.cmd.CreateB2bEnterpriseSkuCmd;
import com.jdh.o2oservice.b2b.export.enterprisesku.cmd.DeleteB2bEnterpriseSkuCmd;
import com.jdh.o2oservice.b2b.export.enterprisesku.cmd.UpdateB2bEnterpriseSkuCmd;
import com.jdh.o2oservice.b2b.export.enterprisesku.dto.B2bEnterpriseSkuDto;
import com.jdh.o2oservice.b2b.export.enterprisesku.dto.JdhSkuInfoDto;
import com.jdh.o2oservice.b2b.export.enterprisesku.query.B2bEnterpriseSkuListRequest;
import com.jdh.o2oservice.b2b.export.enterprisesku.query.B2bEnterpriseSkuPageRequest;
import com.jdh.o2oservice.b2b.export.enterprisesku.query.B2bEnterpriseSkuRequest;
import com.jdh.o2oservice.b2b.application.enterpriseuser.B2bEnterpriseUserApplication;
import com.jdh.o2oservice.export.product.enums.ProductSaleChannelEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description 企业sku
 * @Date 2025/2/24 下午3:09
 * <AUTHOR>
 **/
@Service
@Slf4j
public class B2bEnterpriseSkuApplicationImpl implements B2bEnterpriseSkuApplication {

    /**
     * JdhB2bEnterpriseSkuRepository
     */
    @Resource
    private JdhB2bEnterpriseSkuRepository jdhB2bEnterpriseSkuRepository;

    /**
     * JdhB2bEnterpriseContractRepository
     */
    @Autowired
    private JdhB2bEnterpriseContractRepository jdhB2bEnterpriseContractRepository;

    /**
     * GenerateIdFactory
     */
    @Resource
    private GenerateIdFactory generateIdFactory;

    /**
     * O2oProductJsfExportRpc
     */
    @Resource
    private O2oProductJsfExportRpc productJsfExportRpc;

    /**
     * B2bEnterpriseAccountApplication
     */
    @Resource
    private B2bEnterpriseAccountApplication b2bEnterpriseAccountApplication;

    /**
     * B2bEnterpriseUserApplication
     */
    @Resource
    private B2bEnterpriseUserApplication b2bEnterpriseUserApplication;

    /**
     * 创建企业sku
     * @param cmd
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.application.enterprisesku.impl.B2bEnterpriseSkuApplicationImpl.createEnterpriseSku")
    public Boolean createEnterpriseSku(CreateB2bEnterpriseSkuCmd cmd) {
        // 一个SKU ID 在一个企业下只能有一个
        this.checkEnterpriseSkuRepeat(cmd.getEnterpriseId(), cmd.getSkuId(), null);

        // 校验sku售卖渠道
        this.checkSkuChannel(cmd.getSkuId());

        // 校验企业合同有效性
        Boolean checkContractEffectiveFlag = b2bEnterpriseAccountApplication.checkEnterpriseContract(cmd.getEnterpriseId(), cmd.getContractNumber());
        log.info("B2bEnterpriseSkuApplicationImpl createEnterpriseSku checkContractEffectiveFlag={}", checkContractEffectiveFlag);
        if (!checkContractEffectiveFlag){
            throw new BusinessException(BusinessErrorCode.ENTERPRISE_SKU_SAVE_FAIL);
        }

        // 创业企业sku
        JdhB2bEnterpriseSku enterpriseSku = B2bEnterpriseSkuApplicationConverter.INS.cmd2Entity(cmd);
        enterpriseSku.setEnterpriseSkuId(generateIdFactory.getId());
        log.info("B2bEnterpriseSkuApplicationImpl createEnterpriseSku enterpriseSku={}", JSON.toJSONString(enterpriseSku));
        int count = jdhB2bEnterpriseSkuRepository.save(enterpriseSku);
        if (count < 0){
            throw new BusinessException(BusinessErrorCode.ENTERPRISE_SKU_SAVE_FAIL);
        }
        return Boolean.TRUE;
    }

    /**
     * 校验sku售卖渠道
     * @param skuId
     */
    private void checkSkuChannel(Long skuId) {
        Map<Long, JdhSkuResBo> skuResBoMap = productJsfExportRpc.batchQueryJdhSkuInfo(Collections.singletonList(skuId));
        if (MapUtils.isEmpty(skuResBoMap)){
            throw new BusinessException(BusinessErrorCode.JDH_SKU_NO_EXIST);
        }
        JdhSkuResBo skuResBo = skuResBoMap.get(skuId);
        if (Objects.isNull(skuResBo.getChannelId())){
            throw new BusinessException(BusinessErrorCode.JDH_SKU_CHANNEL_NO_EXIST);
        }
        if (!ProductSaleChannelEnum.B_EXTREMITY.getChannelId().equals(skuResBo.getChannelId())){
            throw new BusinessException(BusinessErrorCode.SELECT_B_SKU);
        }
    }

    private void checkEnterpriseSkuRepeat(Long enterpriseId, Long skuId, Long enterpriseSkuId) {
        JdhB2bEnterpriseSkuQuery enterpriseSkuQuery = JdhB2bEnterpriseSkuQuery.builder()
                .enterpriseId(enterpriseId)
                .skuId(skuId)
                .build();
        List<JdhB2bEnterpriseSku> dbEnterpriseSkuList = jdhB2bEnterpriseSkuRepository.findList(enterpriseSkuQuery);
        if (CollectionUtils.isNotEmpty(dbEnterpriseSkuList)){
            dbEnterpriseSkuList.forEach(enterpriseSku->{
                if (Objects.isNull(enterpriseSkuId)){
                    if (enterpriseId.equals(enterpriseSku.getEnterpriseId()) && skuId.equals(enterpriseSku.getSkuId())){
                        throw new BusinessException(BusinessErrorCode.ENTERPRISE_SKU_EXIST);
                    }
                }else {
                    if (enterpriseId.equals(enterpriseSku.getEnterpriseId()) && skuId.equals(enterpriseSku.getSkuId())
                            && !enterpriseSku.getEnterpriseSkuId().equals(enterpriseSkuId)){
                        throw new BusinessException(BusinessErrorCode.ENTERPRISE_SKU_EXIST);
                    }
                }
            });
        }
    }

    /**
     * 修改企业sku
     * @param cmd
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.application.enterprisesku.impl.B2bEnterpriseSkuApplicationImpl.updateEnterpriseSku")
    public Boolean updateEnterpriseSku(UpdateB2bEnterpriseSkuCmd cmd) {
        JdhB2bEnterpriseSku dbEnterpriseSku = jdhB2bEnterpriseSkuRepository.find(JdhEnterpriseSkuIdentifier.builder().enterpriseSkuId(cmd.getEnterpriseSkuId()).build());
        if (Objects.isNull(dbEnterpriseSku)){
            throw new BusinessException(BusinessErrorCode.ENTERPRISE_SKU_NO_EXIST);
        }

        if (Objects.nonNull(cmd.getSkuId())){
            // 一个SKU ID 在一个企业下只能有一个
            this.checkEnterpriseSkuRepeat(dbEnterpriseSku.getEnterpriseId(), cmd.getSkuId(), cmd.getEnterpriseSkuId());
            // 校验sku售卖渠道
            this.checkSkuChannel(cmd.getSkuId());
        }

        // 校验企业合同有效性
        if (StringUtils.isNotBlank(cmd.getContractNumber())){
            Boolean checkContractEffectiveFlag = b2bEnterpriseAccountApplication.checkEnterpriseContract(dbEnterpriseSku.getEnterpriseId(), cmd.getContractNumber());
            log.info("B2bEnterpriseSkuApplicationImpl updateEnterpriseSku checkContractEffectiveFlag={}", checkContractEffectiveFlag);
            if (!checkContractEffectiveFlag){
                throw new BusinessException(BusinessErrorCode.ENTERPRISE_SKU_UPDATE_FAIL);
            }
        }

        // 更新企业sku
        JdhB2bEnterpriseSku updateEnterpriseSku = B2bEnterpriseSkuApplicationConverter.INS.cmd2Entity(cmd);
        log.info("B2bEnterpriseSkuApplicationImpl updateEnterpriseSku updateEnterpriseSku={}", JSON.toJSONString(updateEnterpriseSku));
        int count = jdhB2bEnterpriseSkuRepository.update(updateEnterpriseSku);
        if (count < 0){
            throw new BusinessException(BusinessErrorCode.ENTERPRISE_SKU_UPDATE_FAIL);
        }
        return Boolean.TRUE;
    }

    /**
     * 删除企业sku
     * @param cmd
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.application.enterprisesku.impl.B2bEnterpriseSkuApplicationImpl.deleteEnterpriseSku")
    public Boolean deleteEnterpriseSku(DeleteB2bEnterpriseSkuCmd cmd) {
        JdhB2bEnterpriseSku enterpriseSku = B2bEnterpriseSkuApplicationConverter.INS.cmd2Entity(cmd);
        log.info("B2bEnterpriseSkuApplicationImpl deleteEnterpriseSku enterpriseSku={}", JSON.toJSONString(enterpriseSku));
        int count = jdhB2bEnterpriseSkuRepository.delete(enterpriseSku);
        if (count < 0){
            throw new BusinessException(BusinessErrorCode.ENTERPRISE_SKU_DELETE_FAIL);
        }
        return Boolean.TRUE;
    }

    /**
     * 查询企业sku
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.application.enterprisesku.impl.B2bEnterpriseSkuApplicationImpl.queryEnterpriseSku")
    public B2bEnterpriseSkuDto queryEnterpriseSku(B2bEnterpriseSkuRequest request) {
        JdhB2bEnterpriseSku dbEnterpriseSku = jdhB2bEnterpriseSkuRepository.find(JdhEnterpriseSkuIdentifier.builder().enterpriseSkuId(request.getEnterpriseSkuId()).build());
        log.info("B2bEnterpriseSkuApplicationImpl queryEnterpriseSku dbEnterpriseSku={}", JSON.toJSONString(dbEnterpriseSku));
        if (Objects.isNull(dbEnterpriseSku)){
            throw new BusinessException(BusinessErrorCode.ENTERPRISE_SKU_NO_EXIST);
        }
        if(Objects.nonNull(request.getEnterpriseId()) && !dbEnterpriseSku.getEnterpriseId().equals(request.getEnterpriseId())){
            throw new BusinessException(BusinessErrorCode.ENTERPRISE_SKU_NON_OWNER);
        }
        B2bEnterpriseSkuDto enterpriseSkuDto = B2bEnterpriseSkuApplicationConverter.INS.entity2EnterpriseSkuDto(dbEnterpriseSku);

        // 批量查询商品信息
        Map<Long, JdhSkuResBo> skuMap = productJsfExportRpc.batchQueryJdhSkuInfo(Collections.singletonList(dbEnterpriseSku.getSkuId()));
        log.info("B2bEnterpriseSkuApplicationImpl queryEnterpriseSku skuMap={}", JSON.toJSONString(skuMap));
        if (MapUtils.isEmpty(skuMap)){
            throw new BusinessException(BusinessErrorCode.JDH_SKU_NO_EXIST);
        }
        JdhSkuResBo skuResBo = skuMap.get(dbEnterpriseSku.getSkuId());
        enterpriseSkuDto.setSkuName(skuResBo.getSkuName());
        enterpriseSkuDto.setShortTitle(skuResBo.getShortTitle());

        if (StringUtils.isNotBlank(dbEnterpriseSku.getContractNumber())){
            JdhB2bEnterpriseContractQuery contractQuery = JdhB2bEnterpriseContractQuery.builder().contractNumber(dbEnterpriseSku.getContractNumber()).build();
            JdhB2bEnterpriseContract enterpriseContract = jdhB2bEnterpriseContractRepository.findJdhB2bEnterpriseContract(contractQuery);
            log.info("B2bEnterpriseSkuApplicationImpl queryEnterpriseSku enterpriseContract={}", JSON.toJSONString(enterpriseContract));
            if (Objects.nonNull(enterpriseContract)){
                enterpriseSkuDto.setContractNumber(enterpriseContract.getContractNumber());
                enterpriseSkuDto.setContractName(enterpriseContract.getName());
                boolean isExpired = new Date().after(enterpriseContract.getStartTime()) && new Date().before(enterpriseContract.getEndTime());
                log.info("B2bEnterpriseSkuApplicationImpl queryEnterpriseSku isExpired={}", isExpired);
                enterpriseSkuDto.setContractStatus(isExpired ? 1 : 0);
                enterpriseSkuDto.setContractStatusDesc(isExpired ? "有效期内" : "过期");
            }
        }
        return enterpriseSkuDto;
    }


    /**
     * 分页查询企业sku
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.application.enterprisesku.impl.B2bEnterpriseSkuApplicationImpl.queryPageEnterpriseSku")
    public PageDto<B2bEnterpriseSkuDto> queryPageEnterpriseSku(B2bEnterpriseSkuPageRequest request) {
        JdhB2bEnterpriseSkuQuery enterpriseSkuQuery = JdhB2bEnterpriseSkuQuery.builder()
                .enterpriseId(request.getEnterpriseId())
                .enterpriseSkuId(request.getEnterpriseSkuId())
                .skuId(request.getSkuId())
                .pageNum(request.getPageNum())
                .pageSize(request.getPageSize())
                .build();
        Page<JdhB2bEnterpriseSku> page = jdhB2bEnterpriseSkuRepository.queryPageEnterpriseSku(enterpriseSkuQuery);
        log.info("B2bEnterpriseSkuApplicationImpl queryPageEnterpriseSku enterpriseSkuQuery={}, page={}", JSON.toJSONString(enterpriseSkuQuery), JSON.toJSONString(page));
        PageDto<B2bEnterpriseSkuDto> result = new PageDto<>();
        result.setTotalPage(page.getPages());
        result.setPageNum(page.getCurrent());
        result.setPageSize(page.getSize());
        result.setTotalCount(page.getTotal());
        result.setList(B2bEnterpriseSkuApplicationConverter.INS.entity2EnterpriseSkuDtoList(page.getRecords()));

        if (CollectionUtils.isNotEmpty(result.getList())){
            List<Long> skuIds = result.getList().stream().map(B2bEnterpriseSkuDto::getSkuId).collect(Collectors.toList());
            // 批量查询商品信息
            Map<Long, JdhSkuResBo> skuMap = productJsfExportRpc.batchQueryJdhSkuInfo(skuIds);
            for (B2bEnterpriseSkuDto e : result.getList()) {
                JdhSkuResBo skuResBo = skuMap.get(e.getSkuId());
                if (Objects.nonNull(skuResBo)){
                    e.setSkuName(skuResBo.getSkuName());
                    e.setShortTitle(skuResBo.getShortTitle());
                }
                if (StringUtils.isNotBlank(e.getContractNumber())){
                    JdhB2bEnterpriseContractQuery contractQuery = JdhB2bEnterpriseContractQuery.builder().contractNumber(e.getContractNumber()).build();
                    JdhB2bEnterpriseContract enterpriseContract = jdhB2bEnterpriseContractRepository.findJdhB2bEnterpriseContract(contractQuery);
                    log.info("B2bEnterpriseSkuApplicationImpl queryPageEnterpriseSku enterpriseContract={}", JSON.toJSONString(enterpriseContract));
                    if (Objects.nonNull(enterpriseContract)){
                        e.setContractNumber(enterpriseContract.getContractNumber());
                        e.setContractName(enterpriseContract.getName());
                        boolean isExpired = new Date().after(enterpriseContract.getStartTime()) && new Date().before(enterpriseContract.getEndTime());;
                        log.info("B2bEnterpriseSkuApplicationImpl queryPageEnterpriseSku isExpired={}", isExpired);
                        e.setContractStatus(isExpired ? 1 : 0);
                        e.setContractStatusDesc(isExpired ? "有效期内" : "过期");
                    }
                }
            }
        }
        return result;
    }

    /**
     * 查询sku
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.application.enterprisesku.impl.B2bEnterpriseSkuApplicationImpl.querySku")
    public JdhSkuInfoDto querySku(B2bEnterpriseSkuRequest request) {
        Map<Long, JdhSkuResBo> skuMap = productJsfExportRpc.batchQueryJdhSkuInfo(Collections.singletonList(request.getSkuId()));
        if (MapUtils.isEmpty(skuMap)){
            throw new BusinessException(BusinessErrorCode.JDH_SKU_NO_EXIST);
        }
        return B2bEnterpriseSkuApplicationConverter.INS.toJdhSkuInfoDto(skuMap.get(request.getSkuId()));
    }

    /**
     * 查询企业sku列表
     * @param request 请求对象
     * @return 企业sku列表
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.application.enterprisesku.impl.B2bEnterpriseSkuApplicationImpl.queryEnterpriseSkuList")
    public List<B2bEnterpriseSkuDto> queryEnterpriseSkuList(B2bEnterpriseSkuListRequest request) {
        // 参数校验
        if (Objects.isNull(request)) {
            throw new BusinessException(SystemErrorCode.PARAM_NULL_ERROR.formatDescription("request"));
        }

        // 构建查询对象
        JdhB2bEnterpriseSkuQuery query = new JdhB2bEnterpriseSkuQuery();
        query.setEnterpriseId(request.getEnterpriseId());

        // 查询企业sku列表
        List<JdhB2bEnterpriseSku> enterpriseSkus = jdhB2bEnterpriseSkuRepository.findList(query);
        if (CollectionUtils.isEmpty(enterpriseSkus)) {
            return Collections.emptyList();
        }

        // 转换为DTO列表
        List<B2bEnterpriseSkuDto> result = B2bEnterpriseSkuApplicationConverter.INS.entity2EnterpriseSkuDtoList(enterpriseSkus);

        // 获取所有skuId
        List<Long> skuIds = new ArrayList<>();
        for (B2bEnterpriseSkuDto dto : result) {
            skuIds.add(dto.getSkuId());
        }

        // 批量查询sku信息
        Map<Long, JdhSkuResBo> skuMap = productJsfExportRpc.batchQueryJdhSkuInfo(skuIds);
        log.info("B2bEnterpriseSkuApplicationImpl queryEnterpriseSkuList skuMap={}", JSON.toJSONString(skuMap));

        // 如果有搜索内容，进行过滤
        if (StringUtils.isNotBlank(request.getContent())) {
            List<B2bEnterpriseSkuDto> filteredResult = new ArrayList<>();
            for (B2bEnterpriseSkuDto dto : result) {
                JdhSkuResBo skuResBo = skuMap.get(dto.getSkuId());
                if (Objects.nonNull(skuResBo)) {
                    dto.setSkuName(skuResBo.getSkuName());
                    dto.setShortTitle(skuResBo.getShortTitle());

                    // 如果内容匹配，添加到结果中
                    if ((StringUtils.isNotBlank(skuResBo.getSkuName()) &&
                        skuResBo.getSkuName().contains(request.getContent())) ||
                        (StringUtils.isNotBlank(skuResBo.getShortTitle()) &&
                        skuResBo.getShortTitle().contains(request.getContent()))) {
                        filteredResult.add(dto);
                    }
                }
            }
            return filteredResult;
        } else {
            // 补充sku信息
            for (B2bEnterpriseSkuDto dto : result) {
                JdhSkuResBo skuResBo = skuMap.get(dto.getSkuId());
                if (Objects.nonNull(skuResBo)) {
                    dto.setSkuName(skuResBo.getSkuName());
                    dto.setShortTitle(skuResBo.getShortTitle());
                }
            }
            return result;
        }
    }

    /**
     * 查询企业sku
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.application.enterprisesku.impl.B2bEnterpriseSkuApplicationImpl.queryEnterpriseSkusByCondition")
    public List<B2bEnterpriseSkuDto> queryEnterpriseSkusByCondition(B2bEnterpriseSkuRequest request) {
        JdhB2bEnterpriseSkuQuery enterpriseSkuQuery = JdhB2bEnterpriseSkuQuery.builder()
                .enterpriseSkuId(request.getEnterpriseSkuId())
                .skuId(request.getSkuId())
                .contractNumber(request.getContractNumber())
                .build();
        List<JdhB2bEnterpriseSku> enterpriseSkuList = jdhB2bEnterpriseSkuRepository.findList(enterpriseSkuQuery);
        return B2bEnterpriseSkuApplicationConverter.INS.entity2EnterpriseSkuDtoList(enterpriseSkuList);
    }
}
