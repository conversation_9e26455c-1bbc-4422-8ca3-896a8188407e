package com.jdh.o2oservice.b2b.application.support.impl;
import com.jdh.o2oservice.b2b.application.support.AuthApplication;
import com.jdh.o2oservice.b2b.domain.support.user.uim.bo.Uim2DimResourceBO;
import com.jdh.o2oservice.b2b.domain.support.user.uim.bo.Uim2MenuBO;
import com.jdh.o2oservice.b2b.domain.support.user.uim.bo.Uim2RoleBO;
import com.jdh.o2oservice.b2b.domain.support.user.uim.bo.UserBO;
import com.jdh.o2oservice.b2b.domain.support.user.uim.param.Uim2UserContext;
import com.jdh.o2oservice.b2b.domain.support.user.uim.rpc.MenuRpc;
import com.jdh.o2oservice.b2b.domain.support.user.uim.rpc.RoleRpc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: duanqiaona1
 * @Date: 2024/4/25 2:26 下午
 * @Description:
 */
@Slf4j
@Service
public class AuthApplicationImpl implements AuthApplication {

    @Autowired
    private MenuRpc menuRpc;

    /**
     * 角色信息
     */
    @Resource
    RoleRpc roleRpc;

    @Override
    public List<Uim2MenuBO> getMenuTree(Uim2UserContext userContext) {

        List<Uim2MenuBO> menuBOList = menuRpc.getMenuTree(userContext);
        return menuBOList;
    }


    @Override
    public UserBO getUserInfo(String erpCode) {

        UserBO userBO = menuRpc.getUserInfo(erpCode);
        return userBO;
    }

    /**
     * 查询用户拥有的全部角色
     *
     * @param erp erp
     * @return list
     */
    @Override
    public List<Uim2RoleBO> getRole(String erp) {
        return roleRpc.getRole(erp);
    }

    /**
     * getDimResource
     *
     * @param erp ERP
     * @return {@link List}<{@link Uim2DimResourceBO}>
     */
    @Override
    public List<Uim2DimResourceBO> getDimResource(String erp) {
        return roleRpc.getDimResource(erp);
    }
}
