package com.jdh.o2oservice.b2b.application.enterprisevoucher.impl;

import cn.hutool.core.util.BooleanUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.googlecode.aviator.AviatorEvaluator;
import com.jdh.o2oservice.b2b.application.enterprise.B2bEnterpriseApplication;
import com.jdh.o2oservice.b2b.application.enterpriseaccount.B2bEnterpriseAccountApplication;
import com.jdh.o2oservice.b2b.application.enterprisesku.B2bEnterpriseSkuApplication;
import com.jdh.o2oservice.b2b.application.enterpriseuser.B2bEnterpriseUserApplication;
import com.jdh.o2oservice.b2b.application.enterprisevoucher.B2bEnterpriseVoucherApplication;
import com.jdh.o2oservice.b2b.application.enterprisevoucher.converter.EnterpriseVoucherAppConvert;
import com.jdh.o2oservice.b2b.application.support.B2bOperationLogApplication;
import com.jdh.o2oservice.b2b.application.support.impl.OrderPlatformApplicationImpl;
import com.jdh.o2oservice.b2b.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.b2b.base.constant.CommonConstant;
import com.jdh.o2oservice.b2b.base.ducc.DuccConfig;
import com.jdh.o2oservice.b2b.base.enums.OpTypeEnum;
import com.jdh.o2oservice.b2b.base.enums.ThreadPoolConfigEnum;
import com.jdh.o2oservice.b2b.base.exception.*;
import com.jdh.o2oservice.b2b.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.b2b.base.util.AnnotationValidator;
import com.jdh.o2oservice.b2b.base.util.DateUtil;
import com.jdh.o2oservice.b2b.base.util.TimeUtils;
import com.jdh.o2oservice.b2b.common.response.PageDto;
import com.jdh.o2oservice.b2b.domain.enterprise.model.JdhB2bEnterprise;
import com.jdh.o2oservice.b2b.domain.enterprise.model.JdhEnterpriseIdentifier;
import com.jdh.o2oservice.b2b.domain.enterprise.repository.JdhB2bEnterpriseRepository;
import com.jdh.o2oservice.b2b.domain.enterprise.repository.query.JdhB2bEnterpriseQuery;
import com.jdh.o2oservice.b2b.domain.enterprisesku.model.JdhB2bEnterpriseSku;
import com.jdh.o2oservice.b2b.domain.enterprisesku.model.JdhEnterpriseSkuIdentifier;
import com.jdh.o2oservice.b2b.domain.enterprisesku.repository.JdhB2bEnterpriseSkuRepository;
import com.jdh.o2oservice.b2b.domain.enterprisesku.repository.query.JdhB2bEnterpriseSkuQuery;
import com.jdh.o2oservice.b2b.domain.enterprisesku.rpc.O2oProductJsfExportRpc;
import com.jdh.o2oservice.b2b.domain.enterprisesku.rpc.bo.JdhSkuResBo;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.*;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.man.*;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.ctx.QueryIntendedAngelCtx;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.ctx.SubmitPromiseCtx;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.enums.PromiseStatusEnum;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.model.EnterpriseVoucher;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.model.EnterpriseVoucherExtend;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.model.PromisePatient;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.query.EnterpriseVoucherPageQuery;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.query.EnterpriseVoucherQuery;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.repository.EnterpriseVoucherRepository;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.rpc.*;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.service.EnterpriseVoucherDomainService;
import com.jdh.o2oservice.b2b.domain.support.address.AddressRpc;
import com.jdh.o2oservice.b2b.domain.support.operationlog.mode.JdhB2bOperationLogResult;
import com.jdh.o2oservice.b2b.export.enterprise.dto.B2bEnterpriseDto;
import com.jdh.o2oservice.b2b.export.enterprise.query.B2bEnterpriseRequest;
import com.jdh.o2oservice.b2b.export.enterpriseaccount.cmd.CreateB2bEnterpriseAccountDetailCmd;
import com.jdh.o2oservice.b2b.export.enterpriseaccount.dto.B2bEnterpriseAccountInfoDto;
import com.jdh.o2oservice.b2b.export.enterpriseaccount.query.B2bEnterpriseAccountRequest;
import com.jdh.o2oservice.b2b.export.enterprisesku.dto.B2bEnterpriseSkuDto;
import com.jdh.o2oservice.b2b.export.enterprisesku.query.B2bEnterpriseSkuRequest;
import com.jdh.o2oservice.b2b.export.enterprisevoucher.cmd.*;
import com.jdh.o2oservice.b2b.export.enterprisevoucher.dto.*;
import com.jdh.o2oservice.b2b.export.enterprisevoucher.query.*;
import com.jdh.o2oservice.b2b.export.openapi.dto.AngelServiceRecordDTO;
import com.jdh.o2oservice.b2b.export.openapi.dto.AngelServiceRecordQuestionGroupDTO;
import com.jdh.o2oservice.b2b.export.openapi.query.AngelServiceRecordRequest;
import com.jdh.o2oservice.b2b.export.operationLog.cmd.OperationLogCmd;
import com.jdh.o2oservice.common.enums.QuestionGroupTypeEnum;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.support.FileManageJsfExport;
import com.jdh.o2oservice.export.support.JdhExportToolsExportService;
import com.jdh.o2oservice.export.support.dto.PutFileResultDto;
import com.jdh.o2oservice.export.support.query.FileInputStreamRequest;
import com.jdh.o2oservice.export.support.query.PutFileRequest;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * B2bEnterpriseVoucherApplicationImpl
 *
 * <AUTHOR>
 * @date 2025/02/27
 */
@Service
@Slf4j
public class B2bEnterpriseVoucherApplicationImpl implements B2bEnterpriseVoucherApplication {

    /**
     * EnterpriseVoucherDomainService
     */
    @Resource
    private EnterpriseVoucherDomainService enterpriseVoucherDomainService;

    /**
     * EnterpriseVoucherRepository
     */
    @Resource
    private EnterpriseVoucherRepository enterpriseVoucherRepository;

    /**
     * O2oManServicePromiseRpc
     */
    @Resource
    private O2oManServicePromiseRpc manServicePromiseRpc;

    /**
     * O2oServicePromiseRpc
     */
    @Resource
    private O2oServicePromiseRpc o2oServicePromiseRpc;

    /**
     * JdhB2bEnterpriseRepository
     */
    @Resource
    private JdhB2bEnterpriseRepository jdhB2bEnterpriseRepository;

    /**
     * AddressRpc
     */
    @Resource
    private AddressRpc addressRpc;

    /**
     * O2oServiceTradeRpc
     */
    @Resource
    private O2oServiceTradeRpc o2oServiceTradeRpc;

    /**
     * O2oServiceAngelWorkRpc
     */
    @Resource
    private O2oServiceAngelWorkRpc o2oServiceAngelWorkRpc;

    /**
     * JdhB2bEnterpriseSkuRepository
     */
    @Resource
    private JdhB2bEnterpriseSkuRepository jdhB2bEnterpriseSkuRepository;

    /**
     * B2bEnterpriseUserApplication
     */
    @Resource
    private B2bEnterpriseUserApplication b2bEnterpriseUserApplication;

    /**
     * O2oProductJsfExportRpc
     */
    @Resource
    private O2oProductJsfExportRpc productJsfExportRpc;

    /**
     * OrderPlatformApplicationImpl
     */
    @Resource
    private OrderPlatformApplicationImpl orderPlatformApplication;

    /**
     * DuccConfig
     */
    @Resource
    private DuccConfig duccConfig;

    /**
     * B2bEnterpriseAccountApplication
     */
    @Resource
    private B2bEnterpriseAccountApplication b2bEnterpriseAccountApplication;

    /**
     * B2bEnterpriseSkuApplication
     */
    @Resource
    private B2bEnterpriseSkuApplication b2bEnterpriseSkuApplication;

    @Resource
    private JdhExportToolsExportService jdhExportToolsExportService;

    @Resource
    private B2bOperationLogApplication operationLogApplication;

    @Resource
    private FileManageJsfExport fileManageJsfExport;

    @Autowired
    private ExecutorPoolFactory executorPoolFactory;

    /**
     * O2oAngelServiceRecordRpc成员变量，用于调用O2oAngelServiceRecordRpc服务，获取服务记录信息。
     */
    @Autowired
    private O2oAngelServiceRecordRpc o2oAngelServiceRecordRpc;

    @Autowired
    private B2bEnterpriseApplication b2bEnterpriseApplication;

    @Autowired
    private O2oQuestionRpc o2oQuestionRpc;

    /**
     * 提交
     *
     * @param cmd cmd
     * @return {@link Boolean }
     */
    @Override
    @LogAndAlarm
    public Long submitPromise(SubmitPromiseCmd cmd) {
        //0、参数校验
        submitPromiseParamCheck(cmd);

        //1、构建model
        SubmitPromiseCtx ctx = EnterpriseVoucherAppConvert.INS.cmd2Ctx(cmd);

        //2、查询可用余额是否满足
        B2bEnterpriseSkuRequest enterpriseSkuRequest = B2bEnterpriseSkuRequest.builder().enterpriseSkuId(ctx.getEnterpriseSkuId()).build();
        enterpriseSkuRequest.setEnterpriseId(cmd.getEnterpriseId());
        B2bEnterpriseSkuDto enterpriseSku = b2bEnterpriseSkuApplication.queryEnterpriseSku(enterpriseSkuRequest);
        ctx.setSkuId(enterpriseSku.getSkuId());
        BigDecimal promiseAmount = calcPromiseAmount(cmd, enterpriseSku);
        B2bEnterpriseAccountInfoDto accountInfoDto = b2bEnterpriseAccountApplication.queryEnterpriseAccountInfo(B2bEnterpriseAccountRequest.builder().enterpriseId(cmd.getEnterpriseId()).build());
        if (accountInfoDto.getCreditAmount().subtract(accountInfoDto.getFreezeAmount()).compareTo(promiseAmount) < 0) {
            throw new BusinessException(BusinessErrorCode.VOUCHER_BALANCE_NOT_ENOUGH);
        }

        // 校验sku上要求的patient数据有效性、排期有效性
        checkPatientBySkuLimit(ctx);

        //3、调用领域服务
        EnterpriseVoucher enterpriseVoucher = enterpriseVoucherDomainService.createAndSubmitPromise(ctx);
        enterpriseVoucher.setTotalAmount(promiseAmount);

        //4、创建原单平台
        if (StringUtils.isNotBlank(cmd.getSourceOrderPlatform())) {
            orderPlatformApplication.createOrderPlatform(EnterpriseVoucherAppConvert.INS.convertCreateOrderPlatformCmd(cmd));
        }

        //5、save 落库
        enterpriseVoucherRepository.save(enterpriseVoucher);

        //6、冻结余额
        CreateB2bEnterpriseAccountDetailCmd accountCmd = CreateB2bEnterpriseAccountDetailCmd.builder().enterpriseId(cmd.getEnterpriseId()).freezeAmount(promiseAmount).freezeType(1).sourceReceiptType(1).sourceReceiptId(enterpriseVoucher.getEnterpriseVoucherId()).operator(enterpriseVoucher.getErpPin()).build();
        b2bEnterpriseAccountApplication.createEnterpriseAccountDetail(accountCmd);
        return ctx.getEnterpriseVoucherId();
    }

    /**
     * 校验sku上要求的patient数据有效性
     *
     * @param ctx
     */
    private void checkPatientBySkuLimit(SubmitPromiseCtx ctx) {
        // 判断地址是否可服务
        AvailableTimeRequest availableTimeRequest = AvailableTimeRequest.builder().skuIds(Collections.singletonList(ctx.getSkuId())).fullAddress(ctx.getExtend().getAddress().getFullAddress()).showTimeType(1).build();
        List<PromiseAvailableTimeDto> promiseAvailableTimeDtos = this.queryAvailableTime(availableTimeRequest);
        if (CollectionUtils.isEmpty(promiseAvailableTimeDtos)) {
            throw new BusinessException(ParamErrorCode.APPOINT_ADDRESS_NOT_SERVICE);
        } else{
            if (Objects.nonNull(ctx.getExtend().getAppointmentTime())) {
                try {
                    Date appointEndTime = DateUtil.parseDate(ctx.getExtend().getAppointmentTime().getAppointmentEndTime(), "yyyy-MM-dd HH:mm");
                    availableTimeRequest.setParentDateId(DateUtil.formatDate(appointEndTime, "MMdd"));
                    List<PromiseAvailableTimeDto> subPromiseAvailableTimeDtos = this.queryAvailableTime(availableTimeRequest);
                    if(CollectionUtils.isNotEmpty(subPromiseAvailableTimeDtos)){
                        if(subPromiseAvailableTimeDtos.size() == 1){
                            // 只有一个可约时间，预约结束时间必须相等
                            if(!ctx.getExtend().getAppointmentTime().getAppointmentEndTime().equals(JSON.parseObject(subPromiseAvailableTimeDtos.get(0).getValue()).getString("appointmentEndTime"))){
                                throw new BusinessException(ParamErrorCode.APPOINT_TIME_NOT_AVAIABLE);
                            }
                        } else {
                            // 多个可约时间，预约结束时间必须在范围内
                            Date minAppointmentTime = DateUtil.parseDate(JSON.parseObject(subPromiseAvailableTimeDtos.get(0).getValue()).getString("appointmentEndTime"), "yyyy-MM-dd HH:mm");
                            Date maxAppointmentTime = DateUtil.parseDate(JSON.parseObject(subPromiseAvailableTimeDtos.get(subPromiseAvailableTimeDtos.size() - 1).getValue()).getString("appointmentEndTime"), "yyyy-MM-dd HH:mm");
                            if(appointEndTime.getTime() < minAppointmentTime.getTime() || appointEndTime.getTime() > maxAppointmentTime.getTime()){
                                throw new BusinessException(ParamErrorCode.APPOINT_TIME_NOT_AVAIABLE);
                            }
                        }
                    } else{
                        // 无可约时间，预约时间不可用
                        throw new BusinessException(ParamErrorCode.APPOINT_TIME_NOT_AVAIABLE);
                    }
                } catch (ParseException e){
                    log.error("B2bEnterpriseVoucherApplicationImpl checkPatientBySkuLimit ParseException", e);
                }
            }
        }
        // 判断patient有效性
        Map<Long, JdhSkuResBo> skuMap = productJsfExportRpc.batchQueryJdhSkuInfo(Collections.singletonList(ctx.getSkuId()));
        if (MapUtils.isNotEmpty(skuMap) && skuMap.containsKey(ctx.getSkuId())) {
            JdhSkuResBo skuResBo = skuMap.get(ctx.getSkuId());
            if (Objects.nonNull(skuResBo) && CollectionUtils.isNotEmpty(ctx.getExtend().getPatientList())) {
                ctx.getExtend().getPatientList().forEach(promisePatient -> {
                    if (Objects.nonNull(skuResBo.getMaxAge()) && (promisePatient.getAge() > skuResBo.getMaxAge() || promisePatient.getAge() < skuResBo.getMinAge())) {
                        throw new BusinessException(ParamErrorCode.APPOINT_PATIENT_AGE_LIMIT);
                    }
                    if (!skuResBo.getGenderLimit().contains(promisePatient.getGender())) {
                        throw new BusinessException(ParamErrorCode.APPOINT_PATIENT_GENDER_LIMIT);
                    }
                });
            }
        }
    }

    private BigDecimal calcPromiseAmount(SubmitPromiseCmd cmd, B2bEnterpriseSkuDto enterpriseSku) {
        int skuSize = cmd.getExtend().getPromisePatientList().size();
        if (skuSize == 1) {
            return enterpriseSku.getChannelPrice();
        } else {
            // 2-单独计价
            if (enterpriseSku.getPriceType().intValue() == 2) {
                return enterpriseSku.getChannelPrice().add(new BigDecimal(--skuSize).multiply(enterpriseSku.getSinglePrice()));
            } else {
                return enterpriseSku.getChannelPrice().multiply(new BigDecimal(skuSize));
            }
        }
    }

    /**
     * submitPromiseParamCheck
     *
     * @param cmd cmd
     */
    private void submitPromiseParamCheck(SubmitPromiseCmd cmd) {
        if (Objects.isNull(cmd)) {
            throw new BusinessException(ParamErrorCode.PARAM_IS_NULL);
        }
        if (Objects.isNull(cmd.getEnterpriseSkuId())) {
            throw new BusinessException(ParamErrorCode.APPOINT_ITEM_IS_NULL);
        }
        if (Objects.nonNull(cmd.getExtend()) && StringUtils.isBlank(cmd.getExtend().getPromiseAddress().getFullAddress())) {
            throw new BusinessException(ParamErrorCode.APPOINT_ADDRESS_IS_NULL);
        }
        if (StringUtils.isBlank(cmd.getAppointmentName())) {
            throw new BusinessException(ParamErrorCode.APPOINT_NAME_IS_NULL);
        }
        if (StringUtils.isBlank(cmd.getAppointmentPhone())) {
            throw new BusinessException(ParamErrorCode.APPOINT_MOBILE_NULL);
        }
        if (Objects.nonNull(cmd.getExtend())) {
            if (Objects.isNull(cmd.getExtend().getPromiseAppointmentTime()) || StringUtils.isBlank(cmd.getExtend().getPromiseAppointmentTime().getAppointmentEndTime())) {
                throw new BusinessException(ParamErrorCode.APPOINT_TIME_NULL);
            } else {
                try {
                    if (DateUtil.parseDate(cmd.getExtend().getPromiseAppointmentTime().getAppointmentEndTime(), "yyyy-MM-dd HH:mm").getTime() < System.currentTimeMillis()) {
                        throw new BusinessException(ParamErrorCode.APPOINT_TIME_INVALID);
                    }
                } catch (ParseException e) {
                    log.error("B2bEnterpriseVoucherApplicationImpl submitPromiseParamCheck ParseException", e);
                }
            }
            if (CollectionUtils.isEmpty(cmd.getExtend().getPromisePatientList())) {
                cmd.getExtend().getPromisePatientList().forEach(promisePatient -> {
                    if (StringUtils.isBlank(promisePatient.getName())) {
                        throw new BusinessException(ParamErrorCode.APPOINT_PATIENT_NAME_NULL);
                    }
                    if (Objects.isNull(promisePatient.getGender())) {
                        throw new BusinessException(ParamErrorCode.APPOINT_PATIENT_GENDER_NULL);
                    }
                    if (Objects.isNull(promisePatient.getAge())) {
                        throw new BusinessException(ParamErrorCode.APPOINT_PATIENT_AGE_NULL);
                    }
                });
            }
        }
    }

    /**
     * 同步履约状态
     *
     * @param cmd cmd
     * @return {@link Boolean }
     */
    @Override
    public Boolean savePromiseStatus(SyncPromiseStatusCmd cmd) {
        EnterpriseVoucherQuery enterpriseVoucherQuery = EnterpriseVoucherQuery.builder()
                .enterpriseVoucherId(Long.parseLong(cmd.getSourceVoucherId()))
                .build();

        EnterpriseVoucher enterpriseVoucher = enterpriseVoucherRepository.queryEnterpriseVoucher(enterpriseVoucherQuery);
        if (Objects.isNull(enterpriseVoucher)) {
            return Boolean.FALSE;
        }
        enterpriseVoucher.setPromiseStatus(cmd.getPromiseStatus());

        enterpriseVoucherRepository.save(enterpriseVoucher);
        return Boolean.TRUE;
    }

    @Override
    @LogAndAlarm
    public Boolean modifyEnterpriseVoucher(ModifyPromiseOrderPlatformRequest request) {
        assert Objects.nonNull(request);
        assert Objects.nonNull(request.getEnterpriseVoucherId());

        EnterpriseVoucherQuery enterpriseVoucherQuery = EnterpriseVoucherQuery.builder()
                .enterpriseVoucherId(request.getEnterpriseVoucherId())
                .build();

        EnterpriseVoucher enterpriseVoucher = enterpriseVoucherRepository.queryEnterpriseVoucher(enterpriseVoucherQuery);
        if (Objects.isNull(enterpriseVoucher)) {
            throw new BusinessException(BusinessErrorCode.ENTERPRISE_VOUCHER_NO_EXIST);
        }

        if (StringUtils.isNotBlank(request.getSourceOrderId())) {
            enterpriseVoucher.setSourceOrderId(request.getSourceOrderId());
        }
        if (StringUtils.isNotBlank(request.getSourceOrderPlatform())) {
            enterpriseVoucher.setSourceOrderPlatform(request.getSourceOrderPlatform());
        }
        enterpriseVoucherRepository.save(enterpriseVoucher);
        return Boolean.TRUE;
    }

    /**
     * 查询履约单详情
     *
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.application.enterprisevoucher.impl.B2bEnterpriseVoucherApplicationImpl.queryCompletePromiseDetails")
    public CompletePromiseDetailsDto queryCompletePromiseDetails(PromiseDetailRequest request) {
        // 查询履约单
        EnterpriseVoucherQuery enterpriseVoucherQuery = EnterpriseVoucherQuery.builder()
                .promiseId(request.getPromiseId())
                .build();
        EnterpriseVoucher enterpriseVoucher = enterpriseVoucherRepository.queryEnterpriseVoucher(enterpriseVoucherQuery);
        log.info("B2bEnterpriseVoucherApplicationImpl queryCompletePromiseDetails enterpriseVoucher={}", JSON.toJSONString(enterpriseVoucher));
        if (Objects.isNull(enterpriseVoucher)) {
            return null;
        }

        // 查询完整B2b promise信息
        ViaCompletePromiseReqBo bo = ViaCompletePromiseReqBo.builder().promiseId(enterpriseVoucher.getPromiseId()).build();
        ViaCompletePromiseResBo viaBo = manServicePromiseRpc.queryCompleteB2bPromise(bo);
        log.info("B2bEnterpriseVoucherApplicationImpl queryCompletePromiseDetails viaBo={}", JSON.toJSONString(viaBo));
        if (Objects.isNull(viaBo)) {
            return null;
        }
        CompletePromiseDetailsDto result = EnterpriseVoucherAppConvert.INS.toCompletePromiseDetailsDto(viaBo);
        result.setPromiseDetailsDto(EnterpriseVoucherAppConvert.INS.toPromiseDetailsDto(enterpriseVoucher));

        // 增强履约单信息
        this.enhancePromiseDetailsDto(result, viaBo, enterpriseVoucher);
        return result;
    }

    private void enhancePromiseDetailsDto(CompletePromiseDetailsDto result, ViaCompletePromiseResBo viaBo, EnterpriseVoucher enterpriseVoucher) {
        result.getPromiseDetailsDto().setCode(viaBo.getPromiseDto().getCode());

        // 企业信息
        JdhB2bEnterprise enterprise = jdhB2bEnterpriseRepository.find(JdhEnterpriseIdentifier.builder().enterpriseId(enterpriseVoucher.getEnterpriseId()).build());
        if (Objects.nonNull(enterprise)) {
            result.getPromiseDetailsDto().setEnterpriseName(enterprise.getName());
            result.getPromiseDetailsDto().setNeedIntendedNurse(enterprise.getNeedIntendedNurse());
        }

        // 商品信息
        JdhB2bEnterpriseSku enterpriseSku = jdhB2bEnterpriseSkuRepository.find(JdhEnterpriseSkuIdentifier.builder().enterpriseSkuId(enterpriseVoucher.getEnterpriseSkuId()).build());
        if (Objects.nonNull(enterpriseSku)) {
            Map<Long, JdhSkuResBo> skuMap = productJsfExportRpc.batchQueryJdhSkuInfo(Collections.singletonList(enterpriseSku.getSkuId()));
            if (MapUtils.isNotEmpty(skuMap) && Objects.nonNull(skuMap.get(enterpriseSku.getSkuId()))) {
                JdhSkuResBo skuResBo = skuMap.get(enterpriseSku.getSkuId());
                result.getPromiseDetailsDto().setSkuId(skuResBo.getSkuId());
                result.getPromiseDetailsDto().setSkuName(skuResBo.getSkuName());
                result.getPromiseDetailsDto().setShortTitle(skuResBo.getShortTitle());
            }
        }

        PromiseBo promiseDto = viaBo.getPromiseDto();
        AngelWorkDetailForManBo angelWorkDto = viaBo.getAngelWorkDto();

        // 预约时间
        if (Objects.nonNull(promiseDto) && promiseDto.getAppointmentTime() != null) {
            EnterpriseVoucherExtendDto extend = result.getPromiseDetailsDto().getExtend();
            if (Objects.isNull(extend)) {
                extend = new EnterpriseVoucherExtendDto();
            }
            PromiseAppointmentTimeDto appointmentTime = new PromiseAppointmentTimeDto();
            appointmentTime.setDateType(promiseDto.getAppointmentTime().getDateType());
            appointmentTime.setAppointmentStartTime(TimeUtils.getDateTimeStr((promiseDto.getAppointmentTime().getAppointmentStartTime())));
            appointmentTime.setAppointmentEndTime(TimeUtils.getDateTimeStr(promiseDto.getAppointmentTime().getAppointmentEndTime()));
            appointmentTime.setIsImmediately(promiseDto.getAppointmentTime().getIsImmediately());
            extend.setAppointmentTime(appointmentTime);
        }

        // 履约单详情按钮
        PromiseDetailButtonDto promiseDetailButtonDto = new PromiseDetailButtonDto();
        Map<String, Object> param = new HashMap<>();
        if (Objects.nonNull(promiseDto)) {
            param.put("promiseStatus", promiseDto.getPromiseStatus());
        }
        if (Objects.nonNull(angelWorkDto)) {
            param.put("workStatus", angelWorkDto.getWorkStatus());
        }
        JSONArray arr = JSON.parseArray(duccConfig.getPromiseDetailButtonConfig());
        for (int i = 0; i < arr.size(); i++) {
            JSONObject obj = arr.getJSONObject(i);
            if ("modifyAppointment".equals(obj.getString("code"))
                    && (Boolean) AviatorEvaluator.compile(obj.getString("statusExpression"), Boolean.TRUE).execute(param)) {
                promiseDetailButtonDto.setModifyAppointmentFlag(true);
            }
            if ("invalidVoucher".equals(obj.getString("code"))
                    && (Boolean) AviatorEvaluator.compile(obj.getString("statusExpression"), Boolean.TRUE).execute(param)) {
                promiseDetailButtonDto.setInvalidVoucherFlag(true);
            }
        }
        result.getPromiseDetailsDto().setPromiseDetailButtonDto(promiseDetailButtonDto);

        // 订单备注信息
        List<PromiseExtendBo> promiseExtends = viaBo.getPromiseDto().getPromiseExtends();
        if (CollectionUtils.isNotEmpty(promiseExtends)){
            for (PromiseExtendBo promiseExtend : promiseExtends) {
                if("orderRemark".equals(promiseExtend.getAttribute())){
                    result.getPromiseDetailsDto().setOrderRemark(promiseExtend.getOrderRemark());
                    break;
                }
            }
        }

    }

    /**
     * 查询可用时间
     *
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.application.enterprisevoucher.impl.B2bEnterpriseVoucherApplicationImpl.queryAvailableTime")
    public List<PromiseAvailableTimeDto> queryAvailableTime(AvailableTimeRequest request) {
        if (Objects.nonNull(request.getPromiseId())) {
            EnterpriseVoucherQuery enterpriseVoucherQuery = EnterpriseVoucherQuery.builder()
                    .promiseId(request.getPromiseId())
                    .build();
            EnterpriseVoucher enterpriseVoucher = enterpriseVoucherRepository.queryEnterpriseVoucher(enterpriseVoucherQuery);
            log.info("B2bEnterpriseVoucherApplicationImpl enterpriseVoucher enterpriseVoucher={}", JSON.toJSONString(enterpriseVoucher));
            if (Objects.isNull(enterpriseVoucher)) {
                throw new BusinessException(BusinessErrorCode.ENTERPRISE_VOUCHER_NO_EXIST);
            }
            JdhB2bEnterpriseSku enterpriseSku = jdhB2bEnterpriseSkuRepository.find(JdhEnterpriseSkuIdentifier.builder().enterpriseSkuId(enterpriseVoucher.getEnterpriseSkuId()).build());
            request.setSkuIds(Collections.singletonList(enterpriseSku.getSkuId()));
            request.setUserPin(enterpriseVoucher.getCustomerUserPin());
        }
        AgencyQueryDateBo bo = EnterpriseVoucherAppConvert.INS.convertAgencyQueryDateBo(request);
        List<AgencyAppointDateBo> appointDateBos = manServicePromiseRpc.queryAvailableTime(bo);
        log.info("B2bEnterpriseVoucherApplicationImpl enterpriseVoucher appointDateBos={}", JSON.toJSONString(appointDateBos));
        return EnterpriseVoucherAppConvert.INS.toAvailableTimeDto(appointDateBos);
    }

    /**
     * 修改预约时间
     *
     * @param cmd
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.application.enterprisevoucher.impl.B2bEnterpriseVoucherApplicationImpl.modifyAppointmentTime")
    public Boolean modifyAppointmentTime(ModifyPromiseTimeCmd cmd) {
        EnterpriseVoucherQuery enterpriseVoucherQuery = EnterpriseVoucherQuery.builder()
                .promiseId(cmd.getPromiseId())
                .build();
        EnterpriseVoucher enterpriseVoucher = enterpriseVoucherRepository.queryEnterpriseVoucher(enterpriseVoucherQuery);
        log.info("B2bEnterpriseVoucherApplicationImpl modifyAppointmentTime enterpriseVoucher={}", JSON.toJSONString(enterpriseVoucher));
        if (Objects.isNull(enterpriseVoucher)) {
            throw new BusinessException(BusinessErrorCode.ENTERPRISE_VOUCHER_NO_EXIST);
        }
        AgentModifyPromiseBo bo = AgentModifyPromiseBo.builder()
                .promiseId(cmd.getPromiseId())
                .appointmentStartTime(cmd.getAppointmentTime().getAppointmentStartTime())
                .appointmentEndTime(cmd.getAppointmentTime().getAppointmentEndTime())
                .dateType(cmd.getAppointmentTime().getDateType())
                .isImmediately(cmd.getAppointmentTime().getIsImmediately())
                .operatorRoleType(cmd.getOperatorRoleType())
                .operator(cmd.getOperator())
                .build();
        return manServicePromiseRpc.modifyAppointmentTime(bo);
    }

    /**
     * 作废服务单
     *
     * @param cmd
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.application.enterprisevoucher.impl.B2bEnterpriseVoucherApplicationImpl.invalidVoucher")
    public Boolean invalidVoucher(InvalidVoucherCmd cmd) {
        InvalidO2oVoucherBo bo = InvalidO2oVoucherBo.builder()
                .voucherId(cmd.getVoucherId())
                .invalidType(cmd.getInvalidType())
                .reason(cmd.getReason())
                .build();
        // 作废服务单
        Boolean invalidVoucherFlag = o2oServicePromiseRpc.invalidVoucher(bo);
        if (BooleanUtil.isTrue(invalidVoucherFlag)) {
            EnterpriseVoucherQuery enterpriseVoucherQuery = EnterpriseVoucherQuery.builder()
                    .voucherId(cmd.getVoucherId())
                    .build();
            EnterpriseVoucher enterpriseVoucher = enterpriseVoucherRepository.queryEnterpriseVoucher(enterpriseVoucherQuery);
            HashSet<Long> enterpriseVoucherIds = new HashSet<>();
            enterpriseVoucherIds.add(enterpriseVoucher.getEnterpriseVoucherId());

            SyncPromiseStatusRequest syncPromiseStatusRequest = SyncPromiseStatusRequest.builder()
                    .enterpriseVoucherIds(enterpriseVoucherIds)
                    .build();
            // 同步履约单状态
            this.syncPromiseStatus(syncPromiseStatusRequest);
        }
        return true;
    }

    /**
     * 查询录音
     *
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.application.enterprisevoucher.impl.B2bEnterpriseVoucherApplicationImpl.queryCallRecordList")
    public List<PromiseCallRecordDto> queryCallRecordList(RecordingRequest request) {
        QueryCallRecordBo bo = QueryCallRecordBo.builder()
                .promiseId(request.getPromiseId())
                .build();
        List<CallRecordBo> callRecordBos = manServicePromiseRpc.queryCallRecordList(bo);
        return EnterpriseVoucherAppConvert.INS.toRecordingDTO(callRecordBos);
    }

    /**
     * 查询录音链接
     *
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.application.enterprisevoucher.impl.B2bEnterpriseVoucherApplicationImpl.queryCallRecordUrl")
    public String queryCallRecordUrl(RecordingRequest request) {
        QueryCallRecordBo bo = QueryCallRecordBo.builder()
                .callId(request.getCallId())
                .build();
        return manServicePromiseRpc.queryCallRecordUrl(bo);
    }

    /**
     * 查询服务记录
     *
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.application.enterprisevoucher.impl.B2bEnterpriseVoucherApplicationImpl.queryServiceRecord")
    public PromiseServiceRecordDto queryServiceRecord(ServiceRecordRequest request) {
        ViaCompletePromiseReqBo bo = ViaCompletePromiseReqBo.builder()
                .promiseId(request.getPromiseId())
                .build();
        ViaCompletePromiseResBo viaBo = manServicePromiseRpc.queryCompleteB2bPromise(bo);
        log.info("B2bEnterpriseVoucherApplicationImpl queryServiceRecord viaBo={}", JSON.toJSONString(viaBo));
        if (Objects.isNull(viaBo)) {
            return null;
        }
        return EnterpriseVoucherAppConvert.INS.toPromiseServiceRecordDto(viaBo.getTagInfo());
    }

    /**
     * 重新派单
     *
     * @param cmd
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.application.enterprisevoucher.impl.B2bEnterpriseVoucherApplicationImpl.reDispatch")
    public Boolean reDispatch(AfreshDispatchCmd cmd) {
        ReDispatchBo bo = ReDispatchBo.builder()
                .promiseId(cmd.getPromiseId())
                .dispatchId(cmd.getDispatchId())
                .operator(cmd.getOperator())
                .roleType(cmd.getRoleType())
                .reason(cmd.getReason())
                .build();
        return manServicePromiseRpc.reDispatch(bo);
    }

    /**
     * 查询服务者详情
     *
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.application.enterprisevoucher.impl.B2bEnterpriseVoucherApplicationImpl.queryAngelDetail")
    public PromiseAngelDetailDto queryAngelDetail(AngelDetailRequest request) {
        AngelDetailBo bo = AngelDetailBo.builder()
                .angelId(request.getAngelId())
                .build();
        JdhAngelDetailBo angelDetailBo = manServicePromiseRpc.queryAngelDetail(bo);
        return EnterpriseVoucherAppConvert.INS.toAngelDetailDto(angelDetailBo);
    }

    /**
     * 定向派单
     *
     * @param cmd
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.application.enterprisevoucher.impl.B2bEnterpriseVoucherApplicationImpl.targetDispatch")
    public Boolean targetDispatch(AppointDispatchCmd cmd) {
        TargetDispatchBo bo = TargetDispatchBo.builder()
                .promiseId(cmd.getPromiseId())
                .dispatchId(cmd.getDispatchId())
                .operator(cmd.getOperator())
                .roleType(cmd.getRoleType())
                .reason(cmd.getReason())
                .targetAngelId(cmd.getTargetAngelId())
                .build();
        DispatchTargetStatusBo targetStatusBo = manServicePromiseRpc.targetDispatch(bo);
        return targetStatusBo.getTargetResult();
    }

    /**
     * 查询派单明细
     *
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.application.enterprisevoucher.impl.B2bEnterpriseVoucherApplicationImpl.queryDispatchDetailList")
    public PageDto<PromiseDispatchDetailDto> queryDispatchDetailList(DispatchDetailRequest request) {
        DispatchDetailForManBo bo = DispatchDetailForManBo.builder()
                .dispatchId(request.getDispatchId())
                .dispatchRound(request.getDispatchRound())
                .pageNum(request.getPageNum())
                .pageSize(request.getPageSize())
                .build();
        PageDto<JdhDispatchForManBo> page = manServicePromiseRpc.queryDispatchDetailList(bo);
        PageDto<PromiseDispatchDetailDto> result = new PageDto<>();
        result.setTotalPage(page.getTotalPage());
        result.setPageNum(page.getPageNum());
        result.setPageSize(page.getPageSize());
        result.setTotalCount(page.getTotalCount());
        result.setList(EnterpriseVoucherAppConvert.INS.toDispatchForManDtoList(page.getList()));
        return result;
    }

    @Override
    public TextParseAddressDto parseAddressText(TextParseAddressRequest request) {
        if (Objects.isNull(request) || StringUtils.isBlank(request.getText())) {
            throw new BusinessException(BusinessErrorCode.USER_ADDRESS_TEXT_PARSE_ERROR);
        }
        TextParseAddressBO addressBO = addressRpc.parseText(EnterpriseVoucherAppConvert.INS.request2RpcParam(request));
        return EnterpriseVoucherAppConvert.INS.textParseBo2Dto(addressBO);
    }

    /**
     * 查询意向护士
     *
     * @param request 请求参数
     * @return 意向护士信息
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.application.enterprisevoucher.impl.B2bEnterpriseVoucherApplicationImpl.queryIntendedAngel")
    public IntendedAngelDto queryIntendedAngel(QueryIntendedAngelRequest request) {
        // 参数校验
        if (Objects.isNull(request)) {
            throw new BusinessException(SystemErrorCode.PARAM_NULL_ERROR.formatDescription("request"));
        }
        if (CollectionUtils.isEmpty(request.getServiceIds())) {
            throw new BusinessException(new DynamicErrorCode("S0011", "服务ID不能为空"));
        }
        if (StringUtils.isBlank(request.getCpsInviteCode())) {
            throw new BusinessException(new DynamicErrorCode("S0011", "CPS邀请码不能为空"));
        }

        // 通过MapStruct将请求对象转换为上下文对象
        QueryIntendedAngelCtx ctx = EnterpriseVoucherAppConvert.INS.request2QueryIntendedAngelCtx(request);

        // 调用RPC查询意向护士
        IntendedAngelBo angelBo = o2oServiceTradeRpc.queryIntendedAngel(ctx);
        log.info("B2bEnterpriseVoucherApplicationImpl queryIntendedAngel angelBo={}", JSON.toJSONString(angelBo));

        // 转换为DTO并返回
        return EnterpriseVoucherAppConvert.INS.toIntendedAngelDto(angelBo);
    }

    @Override
    @LogAndAlarm
    public Boolean syncPromiseStatus(SyncPromiseStatusRequest request) {
        int pageNum = 1;
        Date startTime = Date.from(LocalDate.now().minusMonths(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
        do {
            //1、查询未同步的履约单
            EnterpriseVoucherPageQuery query = EnterpriseVoucherPageQuery.builder()
                    .pageNum(pageNum)
                    .pageSize(200)
                    .promiseStatusSet(PromiseStatusEnum.NON_FINISH_STATUS)
                    .createTimeStart(startTime)
                    .createTimeEnd(new Date())
                    .build();
            // 指定查询条件
            if (Objects.nonNull(request) && CollectionUtils.isNotEmpty(request.getEnterpriseVoucherIds())) {
                query.setEnterpriseVoucherIdList(request.getEnterpriseVoucherIds().stream().collect(Collectors.toList()));
            }

            Page<EnterpriseVoucher> page = enterpriseVoucherRepository.queryEnterpriseVoucherPage(query);
            List<EnterpriseVoucher> enterpriseVoucherList = page.getRecords();

            if (CollectionUtils.isEmpty(enterpriseVoucherList)) {
                break;
            }

            // 2、查询中台履约单状态、并执行同步
            actionSyncPromiseStatus(enterpriseVoucherList);

            //3、下一页
            pageNum++;
        } while (true);

        return Boolean.TRUE;
    }

    /**
     * 查询服务记录
     *
     * @param request
     * @return
     */
    @Override
    public List<AngelServiceRecordDTO> queryAngelServiceRecordList(AngelServiceRecordRequest request) {


        //查询企业配置
        B2bEnterpriseRequest b2bEnterpriseRequest = new B2bEnterpriseRequest();
        b2bEnterpriseRequest.setEnterpriseId(request.getEnterpriseId());
        B2bEnterpriseDto b2bEnterpriseDto = b2bEnterpriseApplication.queryEnterprise(b2bEnterpriseRequest);
        //是否需要服务记录
        Boolean needAngelServiceRecord =  Objects.equals(CommonConstant.ONE,b2bEnterpriseDto.getNeedAngelServiceRecord()) ? Boolean.TRUE : Boolean.FALSE;
        if (!needAngelServiceRecord){
            return Lists.newArrayList();
        }

        EnterpriseVoucher enterpriseVoucher = enterpriseVoucherRepository.queryEnterpriseVoucher(EnterpriseVoucherQuery.builder().enterpriseId(request.getEnterpriseId()).enterpriseVoucherId(request.getEnterpriseVoucherId()).build());
        if (Objects.isNull(enterpriseVoucher)){
            return null;
        }
        EnterpriseVoucherExtend extend = enterpriseVoucher.getExtend();


        List<AngelServiceRecordBBo> bos = o2oAngelServiceRecordRpc.queryAngelServiceBRecordFlow(
                O2oAngelServiceRecordBo.builder()
                        .promiseId(request.getPromiseId())
                        .promisePatientId(request.getPromisePatientId())
                        .build()
        );

        //过滤数据
        Set<String> bQuestionGroupType = duccConfig.getBQuestionGroupType();

        List<AngelServiceRecordDTO> queryAngelServiceRecordList = Lists.newArrayList();

        packDTO(bos, queryAngelServiceRecordList, bQuestionGroupType,extend);

        //组装数据
        return queryAngelServiceRecordList;
    }



    /**
     * 查询中台履约单状态、并执行同步
     *
     * @param enterpriseVoucherList
     */
    private void actionSyncPromiseStatus(List<EnterpriseVoucher> enterpriseVoucherList) {
        // 查询中台履约单状态
        CompletePromiseRequestBo completePromiseRequestBo = new CompletePromiseRequestBo();
        completePromiseRequestBo.setPromiseIds(enterpriseVoucherList.stream().map(EnterpriseVoucher::getPromiseId).collect(Collectors.toSet()));
        List<CompletePromiseBo> completePromiseBoList = o2oServicePromiseRpc.queryCompletePromiseList(completePromiseRequestBo);

        // 比较状态并更新履约状态，终态补充完成/取消时间
        getWaitFixedEnterpriseVoucherList(enterpriseVoucherList, completePromiseBoList).forEach(s -> {
            // 取消态释放余额冻结
            if (s.getPromiseStatus().equals(PromiseStatusEnum.CANCEL.getStatus())) {
                JdhB2bEnterpriseSku enterpriseSku = jdhB2bEnterpriseSkuRepository.find(JdhEnterpriseSkuIdentifier.builder().enterpriseSkuId(s.getEnterpriseSkuId()).build());
                BigDecimal freezeAmount = calcFreezeAmount(s, enterpriseSku);
                // 计算释放金额
                CreateB2bEnterpriseAccountDetailCmd accountCmd = CreateB2bEnterpriseAccountDetailCmd.builder().enterpriseId(s.getEnterpriseId()).freezeAmount(freezeAmount).freezeType(2).sourceReceiptType(1).sourceReceiptId(s.getEnterpriseVoucherId()).operator("o2o-status-sync").build();
                b2bEnterpriseAccountApplication.createEnterpriseAccountDetail(accountCmd);
            }
            // 保存
            enterpriseVoucherRepository.save(s);
        });
    }

    /**
     * 计算释放冻结余额
     *
     * @param enterpriseVoucher
     * @param enterpriseSku
     * @return
     */
    @LogAndAlarm
    private BigDecimal calcFreezeAmount(EnterpriseVoucher enterpriseVoucher, JdhB2bEnterpriseSku enterpriseSku) {
        if (Objects.isNull(enterpriseVoucher) || Objects.isNull(enterpriseVoucher.getExtend())) {
            return BigDecimal.ZERO;
        }
        if (Objects.isNull(enterpriseVoucher.getTotalAmount()) || enterpriseVoucher.getTotalAmount().compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal nurseOutCancelDeduction = enterpriseSku.getNurseOutCancelDeduction();
        if (Objects.isNull(nurseOutCancelDeduction) || nurseOutCancelDeduction.compareTo(BigDecimal.ZERO) <= 0) {
            nurseOutCancelDeduction = BigDecimal.ZERO;
        }
        BigDecimal servedCancelDeduction = enterpriseSku.getNurseServedCancelDeduction();
        if (Objects.isNull(servedCancelDeduction) || servedCancelDeduction.compareTo(BigDecimal.ZERO) <= 0) {
            servedCancelDeduction = BigDecimal.ZERO;
        }

        Integer cancelResponsibleType = enterpriseVoucher.getExtend().getCancelResponsibleType();
        BigDecimal freezeAmount = BigDecimal.ZERO;
        if (Objects.isNull(cancelResponsibleType) || cancelResponsibleType.intValue() == 0) {
            freezeAmount = enterpriseVoucher.getTotalAmount();
        } else if (cancelResponsibleType.intValue() == 1) {
            freezeAmount = enterpriseVoucher.getTotalAmount().multiply(new BigDecimal(1).subtract(nurseOutCancelDeduction)).setScale(2, RoundingMode.HALF_UP);
        } else if (cancelResponsibleType.intValue() == 2) {
            freezeAmount = enterpriseVoucher.getTotalAmount().multiply(new BigDecimal(1).subtract(servedCancelDeduction)).setScale(2, RoundingMode.HALF_UP);
        } else if (cancelResponsibleType.intValue() == 3) {
            freezeAmount = BigDecimal.ZERO;
        }
        return freezeAmount;
    }

    /**
     * 状态比较
     *
     * @param enterpriseVoucherList
     * @param completePromiseBoLit
     * @return
     */
    private List<EnterpriseVoucher> getWaitFixedEnterpriseVoucherList(List<EnterpriseVoucher> enterpriseVoucherList, List<CompletePromiseBo> completePromiseBoLit) {
        if (CollectionUtils.isEmpty(enterpriseVoucherList)) {
            return Collections.emptyList();
        }
        List<EnterpriseVoucher> waitFixedEnterpriseVoucherList = new ArrayList<>();
        Map<Long, EnterpriseVoucher> enterpriseVoucherMap = enterpriseVoucherList.stream().collect(Collectors.toMap(s -> s.getPromiseId(), s -> s, (s1, s2) -> s1));
        Map<Long, CompletePromiseBo> completePromiseBoMap = completePromiseBoLit.stream().collect(Collectors.toMap(s -> s.getPromiseBo().getPromiseId(), s -> s, (s1, s2) -> s1));
        enterpriseVoucherMap.forEach((k, v) -> {
            if (fixEnterpriseVoucherStatus(v, completePromiseBoMap.get(k))) {
                waitFixedEnterpriseVoucherList.add(v);
            }
        });
        log.info("B2bEnterpriseVoucherApplicationImpl getWaitFixedEnterpriseVoucherList enterpriseVoucherList={}", JSON.toJSONString(enterpriseVoucherList));
        log.info("B2bEnterpriseVoucherApplicationImpl getWaitFixedEnterpriseVoucherList waitFixedEnterpriseVoucherList={}", JSON.toJSONString(waitFixedEnterpriseVoucherList));
        return waitFixedEnterpriseVoucherList;
    }

    /**
     * 修正状态
     *
     * @param enterpriseVoucher
     * @param completePromiseBo
     * @return
     */
    private boolean fixEnterpriseVoucherStatus(EnterpriseVoucher enterpriseVoucher, CompletePromiseBo completePromiseBo) {
        if (Objects.isNull(enterpriseVoucher) || Objects.isNull(completePromiseBo) || Objects.isNull(completePromiseBo.getPromiseBo())) {
            return false;
        }
        log.info("B2bEnterpriseVoucherApplicationImpl fixEnterpriseVoucherStatus enterpriseVoucher={}", JSON.toJSONString(enterpriseVoucher));
        log.info("B2bEnterpriseVoucherApplicationImpl fixEnterpriseVoucherStatus completePromiseBo={}", JSON.toJSONString(completePromiseBo));
        int oldPromiseStatus = enterpriseVoucher.getPromiseStatus().intValue();
        int completePromiseStatus = completePromiseBo.getPromiseBo().getPromiseStatus().intValue();
        AngelWorkDetailBo angelWorkDetailBo = completePromiseBo.getAngelWorkDetailBo();
        int newPromiseStatus = PromiseStatusEnum.INIT.getStatus();

        // 工单存在
        if (Objects.nonNull(angelWorkDetailBo)) {
            if (angelWorkDetailBo.getStatus().intValue() == 9) {
                newPromiseStatus = PromiseStatusEnum.WAIT_RECEIVE.getStatus();
            } else {
                newPromiseStatus = angelWorkDetailBo.getStatus().intValue();
            }
        } else {
            if (completePromiseStatus > 1) {
                newPromiseStatus = PromiseStatusEnum.WAIT_RECEIVE.getStatus();
            }
        }

        // 作废
        if (completePromiseStatus == 13) {
            newPromiseStatus = PromiseStatusEnum.CANCEL.getStatus();
            // 已过期
        } else if (completePromiseStatus == 14) {
            newPromiseStatus = PromiseStatusEnum.EXPIRED.getStatus();
            // 待接单
        }
        log.info("B2bEnterpriseVoucherApplicationImpl fixEnterpriseVoucherStatus oldPromiseStatus={}, newPromiseStatus={}", oldPromiseStatus, newPromiseStatus);
        if (newPromiseStatus == oldPromiseStatus) {
            return false;
        }

        // 更新状态
        enterpriseVoucher.setPromiseStatus(newPromiseStatus);
        // 终态补充完成/取消时间
        if (PromiseStatusEnum.FINISH_STATUS.contains(newPromiseStatus)) {
            EnterpriseVoucherExtend extend = enterpriseVoucher.getExtend();
            extend.setFinishTime(completePromiseBo.getPromiseBo().getUpdateTime());
            enterpriseVoucher.setExtend(extend);
        }
        // 作废补充是否有责
        if (newPromiseStatus == PromiseStatusEnum.CANCEL.getStatus().intValue()) {
            int cancelResponsibleType = 0;
            // 已出门到作废
            if (oldPromiseStatus == PromiseStatusEnum.WAIT_SERVICE.getStatus().intValue()) {
                cancelResponsibleType = 1;
                // 服务中到作废
            } else if (oldPromiseStatus == PromiseStatusEnum.SERVICING.getStatus().intValue()) {
                cancelResponsibleType = 2;
                // 服务完成到作废
            } else if (oldPromiseStatus == PromiseStatusEnum.SERVICED.getStatus().intValue() || oldPromiseStatus == PromiseStatusEnum.COMPLETED.getStatus().intValue()) {
                cancelResponsibleType = 3;
            }
            EnterpriseVoucherExtend extend = enterpriseVoucher.getExtend();
            extend.setCancelResponsibleType(cancelResponsibleType);
            log.info("B2bEnterpriseVoucherApplicationImpl fixEnterpriseVoucherStatus cancelResponsibleType={}", cancelResponsibleType);
            enterpriseVoucher.setExtend(extend);
        }
        return true;
    }

    /**
     * 分页查询履约单
     *
     * @param pageRequest pageRequest
     * @return {@link PageDto }<{@link CompletePromiseDetailsDto }>
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.application.enterprisevoucher.impl.B2bEnterpriseVoucherApplicationImpl.queryPromiseDetailPage")
    public PageDto<PromiseDetailsDto> queryPromiseDetailPage(PromiseDetailPageRequest pageRequest) {
        EnterpriseVoucherPageQuery query = EnterpriseVoucherPageQuery.builder()
                .enterpriseIdList(this.convertQueryPageEnterpriseId(pageRequest))
                .promiseId(pageRequest.getPromiseId())
                .enterpriseSkuIdSet(pageRequest.getEnterpriseSkuIdSet())
                .promiseStatusSet(pageRequest.getPromiseStatusSet())
                .createTimeStart(pageRequest.getCreateTimeStart())
                .createTimeEnd(pageRequest.getCreateTimeEnd())
                .enterpriseVoucherId(pageRequest.getEnterpriseVoucherId())
                .pageNum(pageRequest.getPageNum())
                .pageSize(pageRequest.getPageSize())
                .build();
        Page<EnterpriseVoucher> page = enterpriseVoucherRepository.queryEnterpriseVoucherPage(query);
        log.info("B2bEnterpriseVoucherApplicationImpl queryPromiseDetailPage query={}, page={}", JSON.toJSONString(query), JSON.toJSONString(page));

        PageDto<PromiseDetailsDto> result = new PageDto<>();
        result.setPageNum(page.getCurrent());
        result.setPageSize(page.getSize());
        result.setTotalPage(page.getPages());
        result.setTotalCount(page.getTotal());
        result.setList(EnterpriseVoucherAppConvert.INS.toPromiseDetailsList(page.getRecords()));

        // 增强履约单信息
        this.enhancePromiseDetailPage(result);

        return result;
    }


    /**
     * 导出履约单列表
     *
     * @param pageRequest
     * @return
     */
    @Override
    public Boolean exportPromiseDetailPage(ExportPromiseDetailPageRequest pageRequest) {
        CompletableFuture.runAsync(() -> {
            // 最大导出1万条
            pageRequest.setPageSize(10000);
            PageDto<PromiseDetailsDto> pageDto = this.queryPromiseDetailPage(pageRequest);
            Map<String, Object> exportMap = new HashMap<>();
            if (Objects.isNull(pageDto) || CollectionUtils.isEmpty(pageDto.getList())) {
                throw new BusinessException(ParamErrorCode.EXPORT_VOUCHER_NON_DATA);
            }
            exportMap.put("data", pageDto.getList());
            exportMap.put("userPin", pageRequest.getUserPin());
            exportMap.put("scene", "enterpriseVoucherExport");
            exportMap.put("operationType", "enterpriseVoucherExport");
            Response<PutFileResultDto> exportResult = jdhExportToolsExportService.exportAndDownloadFile(exportMap);
            if (Objects.nonNull(exportResult) && Objects.nonNull(exportResult.getData())) {
                PutFileResultDto data = exportResult.getData();
                // 保存操作记录
                OperationLogCmd operationLogCmd = new OperationLogCmd();
                operationLogCmd.setEnterpriseId(pageRequest.getEnterpriseId());
                operationLogCmd.setOperator(pageRequest.getUserPin());
                operationLogCmd.setBizUnionId("exportEnterpriseVoucher");
                operationLogCmd.setBizSceneDesc("下载履约单");
                operationLogCmd.setBizSceneKey("com.jdh.o2oservice.b2b.application.enterprisevoucher.impl.B2bEnterpriseVoucherApplicationImpl.exportPromiseDetailPage");
                operationLogCmd.setOperateType(OpTypeEnum.DOWNLOAD_SERVICE_APPOINTMENT.getType());
                operationLogCmd.setResult(JSON.toJSONString(JdhB2bOperationLogResult.builder().resultAttachment(data.getFileUrl()).resultAttachmentName("履约单.xlsx").build()));
                operationLogCmd.setResultType(2);
                operationLogApplication.batchInsertAsyncToLocalDB(Collections.singletonList(operationLogCmd));
            }
        }, ExecutorPoolFactory.get(ThreadPoolConfigEnum.DEFAULT));
        return true;
    }

    private List<Long> convertQueryPageEnterpriseId(PromiseDetailPageRequest pageRequest) {
        List<Long> enterpriseIdList = new ArrayList<>();
        if (Objects.nonNull(pageRequest.getEnterpriseId())) {
            enterpriseIdList.add(pageRequest.getEnterpriseId());
        }

        // 运营端检索
        if (StringUtils.isNotBlank(pageRequest.getEnterpriseName())) {
            JdhB2bEnterpriseQuery enterpriseQuery = JdhB2bEnterpriseQuery.builder().name(pageRequest.getEnterpriseName()).build();
            List<JdhB2bEnterprise> enterpriseList = jdhB2bEnterpriseRepository.findList(enterpriseQuery);
            log.info("B2bEnterpriseVoucherApplicationImpl convertQueryPageEnterpriseId enterpriseList={}", JSON.toJSONString(enterpriseList));
            if (CollectionUtils.isEmpty(enterpriseList)) {
                enterpriseIdList.add(0L);
            } else {
                List<Long> dbEnterpriseIdList = enterpriseList.stream().map(JdhB2bEnterprise::getEnterpriseId).collect(Collectors.toList());
                enterpriseIdList.addAll(dbEnterpriseIdList);
            }
        }
        return enterpriseIdList;
    }

    /**
     * 增强履约单信息
     *
     * @param result
     */
    private void enhancePromiseDetailPage(PageDto<PromiseDetailsDto> result) {
        if (CollectionUtils.isNotEmpty(result.getList())) {
            // 查询企业信息
            List<Long> enterpriseIdList = result.getList().stream().map(PromiseDetailsDto::getEnterpriseId).collect(Collectors.toList());
            JdhB2bEnterpriseQuery enterpriseQuery = new JdhB2bEnterpriseQuery();
            enterpriseQuery.setEnterpriseIdList(enterpriseIdList);
            List<JdhB2bEnterprise> enterpriseList = jdhB2bEnterpriseRepository.findList(enterpriseQuery);
            log.info("B2bEnterpriseVoucherApplicationImpl enhancePromiseDetailPage enterpriseList={}", JSON.toJSONString(enterpriseList));
            Map<Long, JdhB2bEnterprise> enterpriseMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(enterpriseList)) {
                enterpriseMap = enterpriseList.stream().collect(Collectors.toMap(JdhB2bEnterprise::getEnterpriseId, Function.identity(), (key1, key2) -> key2));
            }

            // 查询履约单
            List<Long> voucherIdList = result.getList().stream().map(PromiseDetailsDto::getVoucherId).collect(Collectors.toList());
            O2oPromiseListRequestBo o2oPromiseListRequestBo = new O2oPromiseListRequestBo();
            o2oPromiseListRequestBo.setVoucherIds(voucherIdList);
            List<O2oPromiseBo> promiseList = o2oServicePromiseRpc.findByPromiseList(o2oPromiseListRequestBo);
            log.info("B2bEnterpriseVoucherApplicationImpl enhancePromiseDetailPage promiseList={}", JSON.toJSONString(promiseList));
            Map<Long, O2oPromiseBo> promiseMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(promiseList)) {
                promiseMap = promiseList.stream().collect(Collectors.toMap(O2oPromiseBo::getPromiseId, Function.identity(), (key1, key2) -> key2));
            }

            // 查询企业服务项目
            List<Long> enterpriseSkuIdList = result.getList().stream().map(PromiseDetailsDto::getEnterpriseSkuId).collect(Collectors.toList());
            JdhB2bEnterpriseSkuQuery enterpriseSkuQuery = new JdhB2bEnterpriseSkuQuery();
            enterpriseSkuQuery.setEnterpriseSkuIdList(enterpriseSkuIdList);
            List<JdhB2bEnterpriseSku> enterpriseSkuList = jdhB2bEnterpriseSkuRepository.findList(enterpriseSkuQuery);
            log.info("B2bEnterpriseVoucherApplicationImpl enhancePromiseDetailPage enterpriseSkuList={}", JSON.toJSONString(promiseList));
            Map<Long, JdhB2bEnterpriseSku> enterpriseSkuMap = enterpriseSkuList.stream().collect(Collectors.toMap(JdhB2bEnterpriseSku::getEnterpriseSkuId,
                    Function.identity(), (key1, key2) -> key2));

            List<Long> skuIds = enterpriseSkuList.stream().map(JdhB2bEnterpriseSku::getSkuId).collect(Collectors.toList());
            Map<Long, JdhSkuResBo> skuMap = productJsfExportRpc.batchQueryJdhSkuInfo(skuIds);
            log.info("B2bEnterpriseVoucherApplicationImpl enhancePromiseDetailPage skuMap={}", JSON.toJSONString(skuMap));

            // 数据增强
            for (PromiseDetailsDto p : result.getList()) {
                JdhB2bEnterprise enterprise = enterpriseMap.get(p.getEnterpriseId());
                if (Objects.nonNull(enterprise)) {
                    p.setEnterpriseName(enterprise.getName());
                    p.setNeedIntendedNurse(enterprise.getNeedIntendedNurse());
                }

                O2oPromiseBo promiseBo = promiseMap.get(p.getPromiseId());
                if (Objects.nonNull(promiseBo) && promiseBo.getAppointmentTime() != null) {
                    EnterpriseVoucherExtendDto extend = p.getExtend();
                    if (Objects.isNull(extend)) {
                        extend = new EnterpriseVoucherExtendDto();
                    }
                    PromiseAppointmentTimeDto appointmentTime = new PromiseAppointmentTimeDto();
                    appointmentTime.setDateType(promiseBo.getAppointmentTime().getDateType());
                    appointmentTime.setAppointmentStartTime(TimeUtils.getDateTimeStr((promiseBo.getAppointmentTime().getAppointmentStartTime())));
                    appointmentTime.setAppointmentEndTime(TimeUtils.getDateTimeStr(promiseBo.getAppointmentTime().getAppointmentEndTime()));
                    appointmentTime.setIsImmediately(promiseBo.getAppointmentTime().getIsImmediately());
                    extend.setAppointmentTime(appointmentTime);
                }

                JdhB2bEnterpriseSku enterpriseSku = enterpriseSkuMap.get(p.getEnterpriseSkuId());
                if (Objects.nonNull(enterpriseSku)) {
                    JdhSkuResBo skuResBo = skuMap.get(enterpriseSku.getSkuId());
                    if (Objects.nonNull(skuResBo)) {
                        p.setSkuId(skuResBo.getSkuId());
                        p.setSkuName(StringUtils.isNotBlank(skuResBo.getShortTitle()) ? skuResBo.getShortTitle() : skuResBo.getSkuName());
                        p.setShortTitle(skuResBo.getShortTitle());
                    }
                }

            }
        }
    }


    /**
     * 通过文件上传，批量预约
     *
     * @param cmd cmd
     * @return {@link Boolean }
     */
    @Override
    @LogAndAlarm
    public Boolean submitPromiseByUpload(SubmitPromiseByUploadCmd cmd) {
        List<ImportVoucherExcelRowRecord> excelRowRecordList = new ArrayList<>();
        AtomicInteger succeedCount = new AtomicInteger(0);
        FileInputStreamRequest fileInputStreamRequest = new FileInputStreamRequest();
        fileInputStreamRequest.setFileId(cmd.getFileId());
        InputStream fileInputStream;
        try {
            // 获取文件数据（可能是byte[]）
            Object fileData = fileManageJsfExport.getFileInputStream(fileInputStreamRequest).getData();

            if (fileData instanceof byte[]) {
                // 如果是byte[]，转换为InputStream
                log.info("B2bEnterpriseVoucherApplicationImpl.submitPromiseByUpload fileData={}", JSON.toJSONString(fileData));
                fileInputStream = new ByteArrayInputStream((byte[]) fileData);
            } else if (fileData instanceof InputStream) {
                // 如果已经是InputStream，直接使用
                fileInputStream = (InputStream) fileData;
            } else {
                throw new IllegalArgumentException("不支持的返回类型: " + fileData.getClass());
            }
        } catch (Exception e) {
            // 处理异常
            throw new RuntimeException("处理文件流时出错", e);
        }

        EasyExcel.read(fileInputStream, ImportVoucherExcelRowData.class, new AnalysisEventListener<ImportVoucherExcelRowData>() {
            @Override
            public void invoke(ImportVoucherExcelRowData importVoucherExcelRowData, AnalysisContext context) {
                log.info("[导入voucher数据] excel行数据{}", JSON.toJSONString(importVoucherExcelRowData));
                addVoucherRowData(importVoucherExcelRowData, excelRowRecordList, context, succeedCount);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
            }
        }).doReadAll();
        log.info("B2bEnterpriseVoucherApplicationImpl.submitPromiseByUpload succeedCount={}, excelRowRecordList={}", succeedCount.get(), JSON.toJSONString(excelRowRecordList));
        Map<String, List<ImportVoucherExcelRowRecord>> importVoucherExcelRowRecordMap = excelRowRecordList.stream().collect(Collectors.groupingBy(ImportVoucherExcelRowRecord::getSheetName));

        OperationLogCmd operationLogCmd = new OperationLogCmd();
        operationLogCmd.setEnterpriseId(cmd.getEnterpriseId());
        operationLogCmd.setOperator(cmd.getUserPin());
        operationLogCmd.setBizUnionId("exportEnterpriseVoucher");
        operationLogCmd.setBizSceneDesc("导入履约单");
        operationLogCmd.setBizSceneKey("com.jdh.o2oservice.b2b.application.enterprisevoucher.B2bEnterpriseVoucherApplication.submitPromiseByUpload");
        operationLogCmd.setOperateType(OpTypeEnum.BATCH_APPOINTMENT.getType());
        operationLogCmd.setResultType(2);

        if (succeedCount.intValue() > 0) {
            // 异步开启解析和发起预约逻辑 todo
            CompletableFuture.supplyAsync(() -> {
                submitPromiseByUpload(importVoucherExcelRowRecordMap, operationLogCmd);
                return null;
            }, executorPoolFactory.get(ThreadPoolConfigEnum.DEFAULT));
        } else {
            //处
            byte[] bytes = generateFailRecord(importVoucherExcelRowRecordMap);
            String fileName = cmd.getFileId() + ".xlsx";
            // 上传文件
            PutFileRequest putFileRequest = new PutFileRequest();
            putFileRequest.setKey(fileName);
            putFileRequest.setDataBtes(bytes);
            putFileRequest.setIsPublic(Boolean.TRUE);
            putFileRequest.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            Response<PutFileResultDto> putFileResultResponse = fileManageJsfExport.putFile(putFileRequest);
            PutFileResultDto putFileResult = putFileResultResponse.getData();
            operationLogCmd.setResult(JSON.toJSONString(JdhB2bOperationLogResult.builder().resultAttachment(putFileResult.getFileUrl()).resultAttachmentName(putFileResult.getFilePath().split("/")[1]).build()));
            operationLogCmd.setResultType(3);
            batchInsertAsyncToLocalDB(Collections.singletonList(operationLogCmd));
        }
        return Boolean.TRUE;
    }

    private void submitPromiseByUpload(Map<String, List<ImportVoucherExcelRowRecord>> importVoucherExcelRowRecordMap, OperationLogCmd operationLogCmd) {
        int succeedCount = 0;
        int importCount = 0;
        Long enterpriseId = operationLogCmd.getEnterpriseId();
        String userPin = operationLogCmd.getOperator();
        for (Map.Entry<String, List<ImportVoucherExcelRowRecord>> entry : importVoucherExcelRowRecordMap.entrySet()) {
            List<ImportVoucherExcelRowRecord> importVoucherExcelRowRecordList = entry.getValue();
            for (ImportVoucherExcelRowRecord importVoucherExcelRowRecord : importVoucherExcelRowRecordList) {
                importCount++;
                if (StringUtils.isNotBlank(importVoucherExcelRowRecord.getErrMsg())) {
                    continue;
                }
                try {
                    submitPromise(getSubmitPromiseCmd(importVoucherExcelRowRecord, enterpriseId, userPin));
                    succeedCount++;
                } catch (Exception e) {
                    String errorMessage = e.getMessage();
                    if (errorMessage != null && errorMessage.contains("desc=")) {
                        // 提取desc=后面的内容，不包括括号等其他信息
                        int descIndex = errorMessage.indexOf("desc=");
                        if (descIndex != -1) {
                            String descPart = errorMessage.substring(descIndex + 5); // 跳过"desc="
                            int endIndex = descPart.indexOf(")");
                            if (endIndex != -1) {
                                errorMessage = descPart.substring(0, endIndex);
                            } else {
                                errorMessage = descPart;
                            }
                        }
                    }
                    importVoucherExcelRowRecord.setErrMsg(errorMessage);
                    log.error("B2bEnterpriseVoucherApplicationImpl.submitPromiseByUpload error", e);
                }
            }
        }
        log.info("B2bEnterpriseVoucherApplicationImpl.submitPromiseByUpload succeedCount={}, importVoucherExcelRowRecord={}", succeedCount, JSON.toJSONString(importVoucherExcelRowRecordMap));
        byte[] bytes = generateFailRecord(importVoucherExcelRowRecordMap);
        String fileName = System.currentTimeMillis() + ".xlsx";
        // 上传文件
        PutFileRequest putFileRequest = new PutFileRequest();
        putFileRequest.setKey(fileName);
        putFileRequest.setDataBtes(bytes);
        putFileRequest.setIsPublic(Boolean.TRUE);
        putFileRequest.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        log.info("B2bEnterpriseVoucherApplicationImpl.submitPromiseByUpload putFileRequest={}", JSON.toJSONString(putFileRequest));
        Response<PutFileResultDto> putFileResultResponse = fileManageJsfExport.putFile(putFileRequest);
        log.info("B2bEnterpriseVoucherApplicationImpl.submitPromiseByUpload putFileResult={}", JSON.toJSONString(putFileResultResponse.getData()));
        PutFileResultDto putFileResult = putFileResultResponse.getData();
        operationLogCmd.setResult(JSON.toJSONString(JdhB2bOperationLogResult.builder().resultAttachment(putFileResult.getFileUrl()).resultAttachmentName(putFileResult.getFilePath().split("/")[1]).build()));
        operationLogCmd.setResultType(succeedCount == 0 ? 3 : (succeedCount == importCount ? 2 : 4));
        batchInsertAsyncToLocalDB(Collections.singletonList(operationLogCmd));
    }

    private SubmitPromiseCmd getSubmitPromiseCmd(ImportVoucherExcelRowRecord importVoucherExcelRowRecord, Long enterpriseId, String userPin) throws ParseException {
        SubmitPromiseCmd cmd = new SubmitPromiseCmd();
        cmd.setEnterpriseId(enterpriseId);
        cmd.setErpPin(userPin);
        cmd.setUserPin(userPin);
        cmd.setSourceOrderId(importVoucherExcelRowRecord.getSourceOrderId());
        cmd.setSourceOrderPlatform(importVoucherExcelRowRecord.getSourceOrderPlatform());
        cmd.setAppointmentName(importVoucherExcelRowRecord.getAppointmentName());
        cmd.setAppointmentPhone(importVoucherExcelRowRecord.getAppointmentPhone());
        cmd.setEnterpriseSkuId(Long.valueOf(importVoucherExcelRowRecord.getEnterpriseSkuId()));
        SubmitPromiseExtend extend = new SubmitPromiseExtend();
        PromiseAddressInfo addressInfo = new PromiseAddressInfo();
        addressInfo.setFullAddress(importVoucherExcelRowRecord.getFullAddress());
        extend.setPromiseAddress(addressInfo);
        PromiseAppointmentTimeInfo appointmentTimeInfo = new PromiseAppointmentTimeInfo();
        appointmentTimeInfo.setDateType(2);
        Date appointmentStartTimeDate = DateUtil.parseDate(importVoucherExcelRowRecord.getAppointmentStartDateTime(), "yyyy-MM-dd HH:mm");
        //appointmentStartTimeDate = TimeUtils.add(appointmentStartTimeDate, Calendar.SECOND, 343);
        Date appointmentEndTimeDate = TimeUtils.add(appointmentStartTimeDate, Calendar.HOUR_OF_DAY, 1);
        appointmentTimeInfo.setAppointmentStartTime(DateUtil.formatDate(appointmentStartTimeDate, "yyyy-MM-dd HH:mm"));
        appointmentTimeInfo.setAppointmentEndTime(DateUtil.formatDate(appointmentEndTimeDate, "yyyy-MM-dd HH:mm"));
        extend.setPromiseAppointmentTime(appointmentTimeInfo);
        extend.setRemark(importVoucherExcelRowRecord.getRemark());

        List<PromisePatientInfo> promisePatientList = new ArrayList<>();
        if (StringUtils.isNotBlank(importVoucherExcelRowRecord.getPatientName1())) {
            promisePatientList.add(PromisePatientInfo.builder().name(importVoucherExcelRowRecord.getPatientName1()).phone(importVoucherExcelRowRecord.getPatientPhone1()).age(Integer.valueOf(importVoucherExcelRowRecord.getPatientAge1())).gender("男".equals(importVoucherExcelRowRecord.getPatientGender1()) ? 1 : 2).build());
        }
        if (StringUtils.isNotBlank(importVoucherExcelRowRecord.getPatientName2())) {
            promisePatientList.add(PromisePatientInfo.builder().name(importVoucherExcelRowRecord.getPatientName2()).phone(importVoucherExcelRowRecord.getPatientPhone2()).age(Integer.valueOf(importVoucherExcelRowRecord.getPatientAge2())).gender("男".equals(importVoucherExcelRowRecord.getPatientGender2()) ? 1 : 2).build());
        }
        if (StringUtils.isNotBlank(importVoucherExcelRowRecord.getPatientName3())) {
            promisePatientList.add(PromisePatientInfo.builder().name(importVoucherExcelRowRecord.getPatientName3()).phone(importVoucherExcelRowRecord.getPatientPhone3()).age(Integer.valueOf(importVoucherExcelRowRecord.getPatientAge3())).gender("男".equals(importVoucherExcelRowRecord.getPatientGender3()) ? 1 : 2).build());
        }
        if (StringUtils.isNotBlank(importVoucherExcelRowRecord.getPatientName4())) {
            promisePatientList.add(PromisePatientInfo.builder().name(importVoucherExcelRowRecord.getPatientName4()).phone(importVoucherExcelRowRecord.getPatientPhone4()).age(Integer.valueOf(importVoucherExcelRowRecord.getPatientAge4())).gender("男".equals(importVoucherExcelRowRecord.getPatientGender4()) ? 1 : 2).build());
        }
        if (StringUtils.isNotBlank(importVoucherExcelRowRecord.getPatientName5())) {
            promisePatientList.add(PromisePatientInfo.builder().name(importVoucherExcelRowRecord.getPatientName5()).phone(importVoucherExcelRowRecord.getPatientPhone5()).age(Integer.valueOf(importVoucherExcelRowRecord.getPatientAge5())).gender("男".equals(importVoucherExcelRowRecord.getPatientGender5()) ? 1 : 2).build());
        }
        extend.setPromisePatientList(promisePatientList);
        cmd.setExtend(extend);
        log.info("B2bEnterpriseVoucherApplicationImpl.getSubmitPromiseCmd cmd={}", JSON.toJSONString(cmd));
        return cmd;
    }

    private void batchInsertAsyncToLocalDB(List<OperationLogCmd> operationLogCmds) {
        operationLogApplication.batchInsertAsyncToLocalDB(operationLogCmds);
    }

    private byte[] generateFailRecord(Map<String, List<ImportVoucherExcelRowRecord>> excelRowRecordMap) {
        log.info("B2bEnterpriseVoucherApplicationImpl.generateFailRecord2Return.excelRowRecordMap={}", JSON.toJSONString(excelRowRecordMap));
        //存储输出流
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        // 写入 Excel 数据到输出流
        Integer index = 0;
        ExcelWriter excelWriter = EasyExcel.write(byteArrayOutputStream).build();

        for (Map.Entry<String, List<ImportVoucherExcelRowRecord>> entry : excelRowRecordMap.entrySet()) {
            log.info("B2bEnterpriseVoucherApplicationImpl.generateFailRecord2Return.index={},key={},value={}", index, JSON.toJSONString(entry.getKey()), JSON.toJSONString(entry.getValue()));
            List<ImportVoucherExcelRowRecord> rowErrRecordList = bulidFeeConfigExcelErrTile();
            List<ImportVoucherExcelRowRecord> rowErrRecords = JSON.parseArray(JSON.toJSONString(entry.getValue()), ImportVoucherExcelRowRecord.class);
            rowErrRecordList.addAll(rowErrRecords);
            WriteSheet writeSheet = EasyExcel.writerSheet().sheetNo(index).sheetName(entry.getKey()).build();
            excelWriter.write(rowErrRecordList, writeSheet);
            index++;
        }
        excelWriter.finish();
        try {
            byteArrayOutputStream.close();
        } catch (Exception e) {
            log.error("ImportVoucherExcelRowRecord.generateFailRecord2Return has error", e);
        }
        return byteArrayOutputStream.toByteArray();
    }

    private List<ImportVoucherExcelRowRecord> bulidFeeConfigExcelErrTile() {
        List<ImportVoucherExcelRowRecord> excelRowErrRecords = new ArrayList<>();
        ImportVoucherExcelRowRecord excelRowErrRecord = new ImportVoucherExcelRowRecord();
        excelRowErrRecord.setEnterpriseSkuId("预约项目");
        excelRowErrRecord.setFullAddress("上门地址");
        excelRowErrRecord.setAppointmentName("联系人");
        excelRowErrRecord.setAppointmentPhone("手机号");
        excelRowErrRecord.setAppointmentStartDateTime("上门时间");
        excelRowErrRecord.setRemark("备注");
        excelRowErrRecord.setSourceOrderId("源订单号");
        excelRowErrRecord.setSourceOrderPlatform("源订单平台");
        excelRowErrRecord.setPatientName1("被服务人1");
        excelRowErrRecord.setPatientGender1("性别1");
        excelRowErrRecord.setPatientAge1("年龄1");
        excelRowErrRecord.setPatientPhone1("手机号1");
        excelRowErrRecord.setPatientName2("被服务人2");
        excelRowErrRecord.setPatientGender2("性别2");
        excelRowErrRecord.setPatientAge2("年龄2");
        excelRowErrRecord.setPatientPhone2("手机号2");
        excelRowErrRecord.setPatientName3("被服务人3");
        excelRowErrRecord.setPatientGender3("性别3");
        excelRowErrRecord.setPatientAge3("年龄3");
        excelRowErrRecord.setPatientPhone3("手机号3");
        excelRowErrRecord.setPatientName4("被服务人4");
        excelRowErrRecord.setPatientGender4("性别4");
        excelRowErrRecord.setPatientAge4("年龄4");
        excelRowErrRecord.setPatientPhone4("手机号4");
        excelRowErrRecord.setPatientName5("被服务人5");
        excelRowErrRecord.setPatientGender5("性别5");
        excelRowErrRecord.setPatientAge5("年龄5");
        excelRowErrRecord.setPatientPhone5("手机号5");
        excelRowErrRecord.setErrMsg("错误信息");
        excelRowErrRecords.add(excelRowErrRecord);
        return excelRowErrRecords;
    }

    /**
     * 向保存的实体中添加数据
     *
     * @param
     */
    private void addVoucherRowData(ImportVoucherExcelRowData importVoucherExcelRowData, List<ImportVoucherExcelRowRecord> excelRowRecordList, AnalysisContext context, AtomicInteger succeedCount) {
        AtomicBoolean flag = new AtomicBoolean(true);
        List<String> errors = AnnotationValidator.validate(importVoucherExcelRowData);
        log.info("B2bEnterpriseVoucherApplicationImpl.addVoucherRowData.errors={}", JSON.toJSONString(errors));

        if (CollectionUtils.isNotEmpty(errors)) {
            flag.set(false);
        } else {
            succeedCount.incrementAndGet();
        }
        ImportVoucherExcelRowRecord importVoucherExcelRowRecord = JSON.parseObject(JSON.toJSONString(importVoucherExcelRowData), ImportVoucherExcelRowRecord.class);
        importVoucherExcelRowRecord.setSheetName(context.readSheetHolder().getSheetName());
        if (!flag.get()) {
            importVoucherExcelRowRecord.setErrMsg(errors.stream().collect(Collectors.joining(", ")));
        }

        excelRowRecordList.add(importVoucherExcelRowRecord);
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @EqualsAndHashCode(callSuper = false)
    static class UploadSubmitPromiseOriginData {

        private String skuId;

        private String fullAddress;

        private String name;

        private String phone;

        private String date;

        private String timeRange;

        private String remark;

        private String sourceOrderId;

        private String platform;

        private String patientName;

        private String patientGender;

        private String patientAge;

        private String patientPhone;

        public String groupByCondition() {
            return skuId + fullAddress + name + phone + date + timeRange + remark + sourceOrderId + platform;
        }
    }

    @Data
    @Builder
    static class UploadSubmitPromise {

        private String skuId;

        private String fullAddress;

        private String name;

        private String phone;

        private String date;

        private String timeRange;

        private String remark;

        private String sourceOrderId;

        private String platform;

        private List<UploadSubmitPromisePatient> patientList;

    }

    @Data
    @Builder
    static class UploadSubmitPromisePatient {

        private String patientName;

        private String patientGender;

        private String patientAge;

        private String patientPhone;
    }

    /**
     * 将AngelServiceRecordBBo列表转换为AngelServiceRecordDTO列表，并根据指定的题目组类型过滤题目。
     * @param bos AngelServiceRecordBBo列表
     * @param queryAngelServiceRecordList AngelServiceRecordDTO列表，用于存储转换后的结果
     * @param bQuestionGroupType 需要过滤的题目组类型集合
     */
    private void packDTO(List<AngelServiceRecordBBo> bos, List<AngelServiceRecordDTO> queryAngelServiceRecordList, Set<String> bQuestionGroupType,EnterpriseVoucherExtend extend) {
        if (CollectionUtils.isNotEmpty(bos)){
            //查询题目

            Map<Long, O2oQuestionBo> quesIdToObj = Maps.newHashMap();

            List<Long> questionIds = Lists.newArrayList();
            for (AngelServiceRecordBBo bo : bos){
                if (CollectionUtils.isNotEmpty(bo.getQuestionGroupDtoList())){
                    for (AngelServiceRecordQuestionGroupBo questionGroupBo : bo.getQuestionGroupDtoList()){
                        if (CollectionUtils.isNotEmpty(questionGroupBo.getQuestionDTOS())){
                            List<Long> quesIds = questionGroupBo.getQuestionDTOS().stream().map(AngelServiceRecordQuestionBo::getQuesId).collect(Collectors.toList());
                            questionIds.addAll(quesIds);
                        }
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(questionIds)){
                List<O2oQuestionBo> o2oQuestionBos = o2oQuestionRpc.queryQuestionList(questionIds);
                if (CollectionUtils.isNotEmpty(o2oQuestionBos)){
                    quesIdToObj = o2oQuestionBos.stream().collect(Collectors.toMap(O2oQuestionBo::getQuesId, p -> p));
                }
            }


            Map<String, List<Long>> filterQuesId = duccConfig.getFilterQuesId();

            //组装name to outpatientId， 返回outpatientId
            Map<String,String> nameToOutPatientId = Maps.newHashMap();
            if (Objects.nonNull(extend)){
                if (CollectionUtils.isNotEmpty(extend.getPatientList())){
                    extend.getPatientList().removeIf(p->StringUtils.isBlank(p.getOutPatientId()));
                    if (CollectionUtils.isNotEmpty(extend.getPatientList())){
                        nameToOutPatientId = extend.getPatientList().stream().collect(Collectors.toMap(PromisePatient::getName, PromisePatient::getOutPatientId));
                    }
                }
            }

            for (AngelServiceRecordBBo bo : bos) {
                //如果没有题目,或者无需过滤题目,直接保存
                AngelServiceRecordDTO dto = EnterpriseVoucherAppConvert.INS.convert(bo);
                queryAngelServiceRecordList.add(dto);
                if (nameToOutPatientId.containsKey(bo.getPatientName())){
                    dto.setOutPatientId(nameToOutPatientId.get(bo.getPatientName()));
                }

                if (CollectionUtils.isEmpty(bo.getQuestionGroupDtoList()) || CollectionUtils.isEmpty(bQuestionGroupType)){
                    continue;
                }
                //过滤题目
                List<AngelServiceRecordQuestionGroupDTO> questionGroupDtoList = dto.getQuestionGroupDtoList();
                for (AngelServiceRecordQuestionGroupDTO questionGroupDto : questionGroupDtoList) {
                    if (!bQuestionGroupType.contains(questionGroupDto.getCode())){
                        continue;
                    }
                    //过滤题目
                    //评估师评估，过滤评估重点、请关联患者的医嘱图片；过滤个性化不展示内容
                    if (StringUtils.equals(QuestionGroupTypeEnum.PREDANGERASSESSMENT.getCode(),questionGroupDto.getCode())){
                        if (CollectionUtils.isNotEmpty(questionGroupDto.getQuestionDTOS())){
                            //过滤评估重点、请关联患者的医嘱图片；过滤个性化不展示内容,如果没有题目，默认不展示
                            Map<Long, O2oQuestionBo> finalQuesIdToObj = quesIdToObj;
                            questionGroupDto.getQuestionDTOS().removeIf(p ->
                                    p.getQuesCode().contains("evaluatePoint")
                                            || Objects.isNull(finalQuesIdToObj.get(p.getQuesId()))
                                            || !Objects.equals(CommonConstant.ONE,finalQuesIdToObj.get(p.getQuesId()).getShowToB())
                            );
                        }
                    }else if (StringUtils.equals(QuestionGroupTypeEnum.PRERECEIVEASSESSMENT.getCode(),questionGroupDto.getCode())){
                        //接单评估，过滤评估重点；过滤个性化不展示内容
                        if (CollectionUtils.isNotEmpty(questionGroupDto.getQuestionDTOS())){
                            Map<Long, O2oQuestionBo> finalQuesIdToObj = quesIdToObj;
                            questionGroupDto.getQuestionDTOS().removeIf(p ->
                                    p.getQuesCode().contains("evaluatePoint")
                                            || Objects.isNull(finalQuesIdToObj.get(p.getQuesId()))
                                            || !Objects.equals(CommonConstant.ONE,finalQuesIdToObj.get(p.getQuesId()).getShowToB())
                            );
                        }
                    }else if (StringUtils.equals(QuestionGroupTypeEnum.PRESERVICEASSESSMENT.getCode(),questionGroupDto.getCode())){
                        //服务评估，过滤评估重点、历史病历、生命体征；过滤个性化不展示内容

                        if (CollectionUtils.isNotEmpty(questionGroupDto.getQuestionDTOS())){
                            List<Long> filterQuesIds = filterQuesId.get(QuestionGroupTypeEnum.PRESERVICEASSESSMENT.getCode());

                            Map<Long, O2oQuestionBo> finalQuesIdToObj = quesIdToObj;
                            questionGroupDto.getQuestionDTOS().removeIf(p ->
                                    p.getQuesCode().contains("evaluatePoint")
                                            || Objects.isNull(finalQuesIdToObj.get(p.getQuesId()))
                                            || !Objects.equals(CommonConstant.ONE,finalQuesIdToObj.get(p.getQuesId()).getShowToB())
                                            ||filterQuesIds.contains(p.getQuesId())
                            );
                        }
                    }else if (StringUtils.equals(QuestionGroupTypeEnum.SERVICERECORD.getCode(),questionGroupDto.getCode())){
                        //服务过程，过滤个性化不展示内容

                        if (CollectionUtils.isNotEmpty(questionGroupDto.getQuestionDTOS())){

                            Map<Long, O2oQuestionBo> finalQuesIdToObj = quesIdToObj;
                            questionGroupDto.getQuestionDTOS().removeIf(p ->
                                    Objects.isNull(finalQuesIdToObj.get(p.getQuesId()))
                                            || !Objects.equals(CommonConstant.ONE,finalQuesIdToObj.get(p.getQuesId()).getShowToB())

                            );
                        }
                    }

                }

            }


        }
    }
}
