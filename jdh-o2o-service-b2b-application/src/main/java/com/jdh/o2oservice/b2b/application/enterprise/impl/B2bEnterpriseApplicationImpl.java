package com.jdh.o2oservice.b2b.application.enterprise.impl;
import cn.hutool.core.util.BooleanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jd.common.web.LoginContext;
import com.jdh.o2oservice.b2b.application.enterprise.B2bEnterpriseApplication;
import com.jdh.o2oservice.b2b.application.enterprise.convert.B2bEnterpriseApplicationConverter;
import com.jdh.o2oservice.b2b.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.b2b.base.constant.NumConstant;
import com.jdh.o2oservice.b2b.base.ducc.DuccConfig;
import com.jdh.o2oservice.b2b.base.exception.BusinessErrorCode;
import com.jdh.o2oservice.b2b.base.exception.BusinessException;
import com.jdh.o2oservice.b2b.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.b2b.base.util.ResponseUtil;
import com.jdh.o2oservice.b2b.common.response.PageDto;
import com.jdh.o2oservice.b2b.domain.enterprise.model.JdhB2bEnterprise;
import com.jdh.o2oservice.b2b.domain.enterprise.model.JdhEnterpriseIdentifier;
import com.jdh.o2oservice.b2b.domain.enterprise.repository.JdhB2bEnterpriseRepository;
import com.jdh.o2oservice.b2b.domain.enterprise.repository.query.JdhB2bEnterpriseQuery;
import com.jdh.o2oservice.b2b.domain.enterpriseaccount.repository.JdhB2bEnterpriseAccountRepository;
import com.jdh.o2oservice.b2b.domain.enterpriseaccount.repository.model.JdhB2bEnterpriseAccount;
import com.jdh.o2oservice.b2b.domain.enterpriseaccount.repository.query.JdhB2bEnterpriseAccountQuery;
import com.jdh.o2oservice.b2b.domain.enterprisesku.model.JdhB2bEnterpriseSku;
import com.jdh.o2oservice.b2b.domain.enterprisesku.repository.JdhB2bEnterpriseSkuRepository;
import com.jdh.o2oservice.b2b.domain.enterprisesku.repository.query.JdhB2bEnterpriseSkuQuery;
import com.jdh.o2oservice.b2b.domain.enterprisesku.rpc.O2oProductJsfExportRpc;
import com.jdh.o2oservice.b2b.domain.enterprisesku.rpc.bo.JdhSkuResBo;
import com.jdh.o2oservice.b2b.domain.enterpriseuser.model.JdhB2bEnterpriseUser;
import com.jdh.o2oservice.b2b.domain.enterpriseuser.repository.JdhB2bEnterpriseUserRepository;
import com.jdh.o2oservice.b2b.domain.enterpriseuser.repository.query.JdhB2bEnterpriseUserQuery;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.model.EnterpriseVoucher;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.query.EnterpriseVoucherQuery;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.repository.EnterpriseVoucherRepository;
import com.jdh.o2oservice.b2b.export.enterprise.cmd.CreateB2bEnterpriseCmd;
import com.jdh.o2oservice.b2b.export.enterprise.cmd.UpdateB2bEnterpriseCmd;
import com.jdh.o2oservice.b2b.export.enterprise.cmd.UpdateB2bEnterpriseStatusCmd;
import com.jdh.o2oservice.b2b.export.enterprise.dto.B2bEnterpriseDto;
import com.jdh.o2oservice.b2b.export.enterprise.dto.B2bEnterpriseEscrowDto;
import com.jdh.o2oservice.b2b.export.enterprise.dto.B2bEnterpriseRelInfoDto;
import com.jdh.o2oservice.b2b.export.enterprise.dto.B2bEnterpriseRelSkuDto;
import com.jdh.o2oservice.b2b.export.enterprise.query.B2bEnterpriseEscrowRequest;
import com.jdh.o2oservice.b2b.export.enterprise.query.B2bEnterprisePageRequest;
import com.jdh.o2oservice.b2b.export.enterprise.query.B2bEnterpriseRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.text.StringSubstitutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Description 企业信息服务
 * @Date 2025/2/24 下午3:07
 * <AUTHOR>
 **/
@Service
@Slf4j
public class B2bEnterpriseApplicationImpl implements B2bEnterpriseApplication {

    /**
     * JdhB2bEnterpriseRepository
     */
    @Resource
    private JdhB2bEnterpriseRepository jdhB2bEnterpriseRepository;
    /**
     * EnterpriseVoucherRepository
     */
    @Resource
    private EnterpriseVoucherRepository enterpriseVoucherRepository;
    /**
     * JdhB2bEnterpriseSkuRepository
     */
    @Resource
    private JdhB2bEnterpriseSkuRepository jdhB2bEnterpriseSkuRepository;

    /**
     * GenerateIdFactory
     */
    @Resource
    private GenerateIdFactory generateIdFactory;

    /**
     * JdhB2bEnterpriseUserRepository
     */
    @Resource
    private JdhB2bEnterpriseUserRepository jdhB2bEnterpriseUserRepository;

    /**
     * JdhB2bEnterpriseAccountRepository
     */
    @Resource
    private JdhB2bEnterpriseAccountRepository jdhB2bEnterpriseAccountRepository;

    /**
     * O2oProductJsfExportRpc
     */
    @Resource
    private O2oProductJsfExportRpc productJsfExportRpc;

    /**
     * DuccConfig
     */
    @Resource
    private DuccConfig duccConfig;

    /**
     * 创建企业
     * @param cmd
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.application.enterprise.impl.B2bEnterpriseApplicationImpl.createEnterprise")
    @Transactional
    public Boolean createEnterprise(CreateB2bEnterpriseCmd cmd) {
        // 校验与已有企业名称是否重复
        this.checkEnterpriseNameRepeat(cmd.getName(), null);

        // 创建企业
        JdhB2bEnterprise enterprise = B2bEnterpriseApplicationConverter.INS.cmd2Entity(cmd);
        Long enterpriseId = generateIdFactory.getId();
        enterprise.setEnterpriseId(enterpriseId);
        log.info("B2bEnterpriseApplicationImpl createEnterprise enterprise={}", JSON.toJSONString(enterprise));
        int saveEnterpriseCount = jdhB2bEnterpriseRepository.save(enterprise);
        if (saveEnterpriseCount < 0){
            throw new BusinessException(BusinessErrorCode.ENTERPRISE_SAVE_FAIL);
        }

        // 创建企业账户额度
        JdhB2bEnterpriseAccount enterpriseAccount = JdhB2bEnterpriseAccount.builder()
                .enterpriseId(enterpriseId)
                .accountId(generateIdFactory.getId())
                .creditAmount(cmd.getCreditAmount())
                .freezeAmount(new BigDecimal(NumConstant.NUM_0))
                .createUser(cmd.getOperator())
                .updateUser(cmd.getOperator())
                .build();
        log.info("B2bEnterpriseApplicationImpl createEnterprise enterpriseAccount={}", JSON.toJSONString(enterpriseAccount));
        int saveEnterpriseAccountCount = jdhB2bEnterpriseAccountRepository.save(enterpriseAccount);
        if (saveEnterpriseAccountCount < 0){
            throw new BusinessException(BusinessErrorCode.ENTERPRISE_ACCOUNT_SAVE_FAIL);
        }
        return Boolean.TRUE;
    }

    /**
     * 校验与已有企业名称是否重复
     * @param name
     * @param enterpriseId
     */
    private void checkEnterpriseNameRepeat(String name, Long enterpriseId) {
        List<JdhB2bEnterprise> dbEnterpriseList = jdhB2bEnterpriseRepository.findList(JdhB2bEnterpriseQuery.builder()
                .name(name).build());
        if (CollectionUtils.isNotEmpty(dbEnterpriseList)){
            dbEnterpriseList.forEach(enterprise->{
                if (Objects.isNull(enterpriseId)){
                    if (name.equals(enterprise.getName())){
                        throw new BusinessException(BusinessErrorCode.ENTERPRISE_NAME_EXIST);
                    }
                }else {
                    if (name.equals(enterprise.getName()) && !enterprise.getEnterpriseId().equals(enterpriseId)){
                        throw new BusinessException(BusinessErrorCode.ENTERPRISE_NAME_EXIST);
                    }
                }
            });
        }
    }

    /**
     * 修改企业
     * @param cmd
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.application.enterprise.impl.B2bEnterpriseApplicationImpl.updateEnterprise")
    @Transactional
    public Boolean updateEnterprise(UpdateB2bEnterpriseCmd cmd) {
        JdhB2bEnterprise dbEnterprise = jdhB2bEnterpriseRepository.find(JdhEnterpriseIdentifier.builder().enterpriseId(cmd.getEnterpriseId()).build());
        if (Objects.isNull(dbEnterprise)){
            throw new BusinessException(BusinessErrorCode.ENTERPRISE_NO_EXIST);
        }

        // 校验与已有企业名称是否重复
        this.checkEnterpriseNameRepeat(cmd.getName(), cmd.getEnterpriseId());

        // 更新企业
        JdhB2bEnterprise updateEnterprise = B2bEnterpriseApplicationConverter.INS.cmd2Entity(cmd);
        log.info("B2bEnterpriseApplicationImpl updateEnterprise updateEnterprise={}", JSON.toJSONString(updateEnterprise));
        int updateEnterpriseCount = jdhB2bEnterpriseRepository.update(updateEnterprise);
        if (updateEnterpriseCount < 0){
            throw new BusinessException(BusinessErrorCode.ENTERPRISE_UPDATE_FAIL);
        }

        // 更新企业信用额度
        if (cmd.getCreditAmount() != null){
            JdhB2bEnterpriseAccount dbEnterpriseAccount = this.getEnterpriseAccount(cmd.getEnterpriseId());
            // 更新企业账户
            JdhB2bEnterpriseAccount updateEnterpriseAccount = JdhB2bEnterpriseAccount.builder()
                    .accountId(dbEnterpriseAccount.getAccountId())
                    .creditAmount(cmd.getCreditAmount())
                    .version(dbEnterpriseAccount.getVersion())
                    .build();
            log.info("B2bEnterpriseApplicationImpl updateEnterprise updateEnterpriseAccount={}", JSON.toJSONString(updateEnterpriseAccount));
            jdhB2bEnterpriseAccountRepository.update(updateEnterpriseAccount);
        }
        return Boolean.TRUE;
    }

    private JdhB2bEnterpriseAccount getEnterpriseAccount(Long enterpriseId) {
        List<JdhB2bEnterpriseAccount> enterpriseAccountList = jdhB2bEnterpriseAccountRepository.findList(JdhB2bEnterpriseAccountQuery.builder().enterpriseId(enterpriseId).build());
        if (CollectionUtils.isEmpty(enterpriseAccountList)){
            throw new BusinessException(BusinessErrorCode.ENTERPRISE_ACCOUNT_NO_EXIST);
        }
        return enterpriseAccountList.get(0);
    }

    /**
     * 修改企业状态
     * @param cmd
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.application.enterprise.impl.B2bEnterpriseApplicationImpl.updateEnterpriseStatus")
    public Boolean updateEnterpriseStatus(UpdateB2bEnterpriseStatusCmd cmd) {
        JdhB2bEnterprise dbEnterprise = jdhB2bEnterpriseRepository.find(JdhEnterpriseIdentifier.builder().enterpriseId(cmd.getEnterpriseId()).build());
        if (Objects.isNull(dbEnterprise)){
            throw new BusinessException(BusinessErrorCode.ENTERPRISE_NO_EXIST);
        }
        JdhB2bEnterprise updateEnterprise = B2bEnterpriseApplicationConverter.INS.cmd2Entity(cmd);
        log.info("B2bEnterpriseApplicationImpl updateEnterpriseStatus updateEnterprise={}", JSON.toJSONString(updateEnterprise));
        int count = jdhB2bEnterpriseRepository.updateEnterpriseStatus(updateEnterprise);
        if (count < 0){
            throw new BusinessException(BusinessErrorCode.ENTERPRISE_UPDATE_FAIL);
        }
        return Boolean.TRUE;
    }

    /**
     * 查询企业
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.application.enterprise.impl.B2bEnterpriseApplicationImpl.queryEnterprise")
    public B2bEnterpriseDto queryEnterprise(B2bEnterpriseRequest request) {
        JdhB2bEnterprise dbEnterprise = jdhB2bEnterpriseRepository.find(JdhEnterpriseIdentifier.builder().enterpriseId(request.getEnterpriseId()).build());
        if (Objects.isNull(dbEnterprise)){
            throw new BusinessException(BusinessErrorCode.ENTERPRISE_NO_EXIST);
        }
        B2bEnterpriseDto result = B2bEnterpriseApplicationConverter.INS.entity2EnterpriseDto(dbEnterprise);
        JdhB2bEnterpriseAccount dbEnterpriseAccount = this.getEnterpriseAccount(request.getEnterpriseId());
        // 信用额度
        BigDecimal creditAmount = dbEnterpriseAccount.getCreditAmount();
        // 冻结金额
        BigDecimal freezeAmount = dbEnterpriseAccount.getFreezeAmount();
        // 剩余额度
        BigDecimal remainingAmount = creditAmount.subtract(freezeAmount);
        result.setCreditAmount(creditAmount);
        result.setFreezeAmount(freezeAmount);
        result.setRemainingAmount(remainingAmount);
        return result;
    }

    /**
     * @param request
     * @return
     */
    @Override
    public B2bEnterpriseDto queryEnterpriseByPromiseId(B2bEnterpriseRequest request) {
        EnterpriseVoucherQuery enterpriseVoucherQuery = EnterpriseVoucherQuery.builder()
                .promiseId(request.getPromiseId()).voucherId(request.getVoucherId())
                .build();
        EnterpriseVoucher enterpriseVoucher = enterpriseVoucherRepository.queryEnterpriseVoucher(enterpriseVoucherQuery);
        if(Objects.nonNull(enterpriseVoucher)){
            JdhB2bEnterprise dbEnterprise = jdhB2bEnterpriseRepository.find(JdhEnterpriseIdentifier.builder().enterpriseId(enterpriseVoucher.getEnterpriseId()).build());
            B2bEnterpriseDto result = B2bEnterpriseApplicationConverter.INS.entity2EnterpriseDto(dbEnterprise);
            return result;
        }

        return null;
    }

    /**
     * 分页查询企业
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.application.enterprise.impl.B2bEnterpriseApplicationImpl.queryPageEnterprise")
    public PageDto<B2bEnterpriseDto> queryPageEnterprise(B2bEnterprisePageRequest request) {
        // 返回结果
        PageDto<B2bEnterpriseDto> result = new PageDto<>();
        result.setPageNum(request.getPageNum());
        result.setPageSize(request.getPageSize());

        String searchEnterpriseId = request.getSearchEnterpriseId();
        if (StringUtils.isNotBlank(searchEnterpriseId)){
            if (StringUtils.isNumeric(searchEnterpriseId)){
                request.setEnterpriseId(Long.valueOf(searchEnterpriseId));
            }else {
                return result;
            }
        }
        JdhB2bEnterpriseQuery enterpriseQuery = JdhB2bEnterpriseQuery.builder()
                .enterpriseId(request.getEnterpriseId())
                .name(request.getName())
                .shortName(request.getShortName())
                .verticalCode(request.getVerticalCode())
                .available(request.getAvailable())
                .pageNum(request.getPageNum())
                .pageSize(request.getPageSize())
                .build();
        Page<JdhB2bEnterprise> page = jdhB2bEnterpriseRepository.queryPageEnterprise(enterpriseQuery);
        log.info("B2bEnterpriseApplicationImpl queryPageEnterprise enterpriseQuery={}, page={}", JSON.toJSONString(enterpriseQuery), JSON.toJSONString(page));

        result.setTotalPage(page.getPages());
        result.setTotalCount(page.getTotal());
        result.setList(B2bEnterpriseApplicationConverter.INS.entity2EnterpriseDtoList(page.getRecords()));

        if (CollectionUtils.isNotEmpty(result.getList())){
            // 查询企业sku
            JdhB2bEnterpriseSkuQuery enterpriseSkuQuery = JdhB2bEnterpriseSkuQuery.builder()
                    .enterpriseIdList(result.getList().stream().map(B2bEnterpriseDto::getEnterpriseId).collect(Collectors.toList()))
                    .build();
            List<JdhB2bEnterpriseSku> enterpriseSkuList = jdhB2bEnterpriseSkuRepository.findList(enterpriseSkuQuery);
            log.info("B2bEnterpriseApplicationImpl queryPageEnterprise enterpriseSkuList={}", JSON.toJSONString(enterpriseSkuList));
            if (CollectionUtils.isNotEmpty(enterpriseSkuList)){
                Map<Long, List<JdhB2bEnterpriseSku>> enterpriseSkuMap = enterpriseSkuList.stream().collect(Collectors.groupingBy(JdhB2bEnterpriseSku::getEnterpriseId));
                result.getList().forEach(r->{
                    List<JdhB2bEnterpriseSku> enterpriseSkus = enterpriseSkuMap.get(r.getEnterpriseId());
                    if (CollectionUtils.isNotEmpty(enterpriseSkus)){
                        r.setRelServiceNum(enterpriseSkus.size());
                    }
                });
            }
        }
        return result;
    }

    /**
     * queryEnterpriseRelInfo
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.application.enterprise.impl.B2bEnterpriseApplicationImpl.queryEnterpriseRelInfo")
    public B2bEnterpriseRelInfoDto queryEnterpriseRelInfo(B2bEnterpriseRequest request) {
        // 返回结果
        B2bEnterpriseRelInfoDto result = new B2bEnterpriseRelInfoDto();

        // 企业信息
        JdhB2bEnterprise dbEnterprise = jdhB2bEnterpriseRepository.find(JdhEnterpriseIdentifier.builder().enterpriseId(request.getEnterpriseId()).build());
        log.info("B2bEnterpriseApplicationImpl queryEnterpriseRelInfo dbEnterprise={}", JSON.toJSONString(dbEnterprise));
        if (Objects.isNull(dbEnterprise)){
            return null;
        }
        B2bEnterpriseDto enterpriseDto = B2bEnterpriseApplicationConverter.INS.entity2EnterpriseDto(dbEnterprise);

        // 查询企业账户
        List<JdhB2bEnterpriseAccount> enterpriseAccountList = jdhB2bEnterpriseAccountRepository.findList(JdhB2bEnterpriseAccountQuery.builder()
                .enterpriseId(request.getEnterpriseId()).build());
        log.info("B2bEnterpriseApplicationImpl queryEnterpriseRelInfo enterpriseAccountList={}", JSON.toJSONString(enterpriseAccountList));
        JdhB2bEnterpriseAccount enterpriseAccount = enterpriseAccountList.get(0);
        // 信用额度
        BigDecimal creditAmount = enterpriseAccount.getCreditAmount();
        // 冻结金额
        BigDecimal freezeAmount = enterpriseAccount.getFreezeAmount();
        // 剩余额度
        BigDecimal remainingAmount = creditAmount.subtract(freezeAmount);
        enterpriseDto.setCreditAmount(creditAmount);
        enterpriseDto.setFreezeAmount(freezeAmount);
        enterpriseDto.setRemainingAmount(remainingAmount);
        result.setB2bEnterprise(enterpriseDto);

        // 查询企业sku
        JdhB2bEnterpriseSkuQuery enterpriseSkuQuery = JdhB2bEnterpriseSkuQuery.builder()
                .enterpriseId(request.getEnterpriseId())
                .build();
        List<JdhB2bEnterpriseSku> enterpriseSkuList = jdhB2bEnterpriseSkuRepository.findList(enterpriseSkuQuery);
        log.info("B2bEnterpriseApplicationImpl queryEnterpriseRelInfo enterpriseSkuList={}", JSON.toJSONString(enterpriseSkuList));
        result.setEnterpriseRelSkuList(B2bEnterpriseApplicationConverter.INS.entity2EnterpriseRelSkuDto(enterpriseSkuList));

        if (CollectionUtils.isNotEmpty(result.getEnterpriseRelSkuList())){
            List<Long> skuIds = result.getEnterpriseRelSkuList().stream().map(B2bEnterpriseRelSkuDto::getSkuId).collect(Collectors.toList());
            Map<Long, JdhSkuResBo> skuMap = productJsfExportRpc.batchQueryJdhSkuInfo(skuIds);
            result.getEnterpriseRelSkuList().forEach(relSku->{
                JdhSkuResBo skuResBo = skuMap.get(relSku.getSkuId());
                if (Objects.nonNull(skuResBo)){
                    relSku.setSkuName(StringUtils.isNotBlank(skuResBo.getShortTitle()) ? skuResBo.getShortTitle() : skuResBo.getSkuName());
                }
            });
        }

        // 查询企业用户
        JdhB2bEnterpriseUserQuery enterpriseUserQuery = JdhB2bEnterpriseUserQuery.builder().enterpriseId(request.getEnterpriseId()).build();
        List<JdhB2bEnterpriseUser> enterpriseUserList = jdhB2bEnterpriseUserRepository.findList(enterpriseUserQuery);
        log.info("B2bEnterpriseApplicationImpl queryEnterpriseRelInfo enterpriseUserList={}", JSON.toJSONString(enterpriseUserList));
        result.setEnterpriseRelUserList(B2bEnterpriseApplicationConverter.INS.entity2EnterpriseRelUserDto(enterpriseUserList));
        return result;
    }

    /**
     * 企业代管
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.application.enterprise.impl.B2bEnterpriseApplicationImpl.enterpriseEscrow")
    public B2bEnterpriseEscrowDto enterpriseEscrow(B2bEnterpriseEscrowRequest request) {
        B2bEnterpriseEscrowDto result = new B2bEnterpriseEscrowDto();
        result.setEscrowMode(false);
        if (BooleanUtil.isTrue(request.getEscrowMode())){
            result.setEscrowMode(request.getEscrowMode());
            result.setEnterpriseId(request.getEnterpriseId());
            JdhB2bEnterprise enterprise = jdhB2bEnterpriseRepository.find(JdhEnterpriseIdentifier.builder().enterpriseId(request.getEnterpriseId()).build());
            log.info("B2bEnterpriseApplicationImpl enterpriseEscrow enterprise={}", JSON.toJSONString(enterprise));
            String escrowCopyWriting = duccConfig.getEscrowCopyWriting();
            Map<String, String> dataMap = new HashMap<>(1);
            dataMap.put("enterpriseName", enterprise.getName());
            StringSubstitutor sub = new StringSubstitutor(dataMap);
            escrowCopyWriting = sub.replace(escrowCopyWriting);
            result.setEscrowCopyWriting(escrowCopyWriting);
        }
        return result;
    }

    /**
     * 企业代管添加cookie
     * @param req
     * @param res
     */
    @Override
    public void enterpriseEscrowAddCookie(HttpServletRequest req, HttpServletResponse res) throws IOException {
        log.info("B2bEnterpriseApplicationImpl enterpriseEscrowAddCookie start");
        String erp = LoginContext.getLoginContext().getPin();
        String enterpriseId = req.getParameter("enterpriseId");
        log.info("B2bEnterpriseApplicationImpl enterpriseEscrowAddCookie erp={}, enterpriseId={}", erp, enterpriseId);

        Cookie userErpCookie = new Cookie("escrowNo", erp);
        userErpCookie.setDomain("jd.com");
        userErpCookie.setPath("/");
        userErpCookie.setMaxAge(duccConfig.getEscrowCookieEffective());

        Cookie enterpriseIdCookie = new Cookie("enterpriseNo", org.apache.commons.lang3.StringUtils.isBlank(enterpriseId) ? "0" : enterpriseId);
        enterpriseIdCookie.setDomain("jd.com");
        enterpriseIdCookie.setPath("/");
        enterpriseIdCookie.setMaxAge(duccConfig.getEscrowCookieEffective());

        Cookie cthrCookie = new Cookie("cthr", "1");
        cthrCookie.setDomain("jd.com");
        cthrCookie.setPath("/");
        cthrCookie.setMaxAge(duccConfig.getEscrowCookieEffective());

        res.addCookie(userErpCookie);
        res.addCookie(enterpriseIdCookie);
        res.addCookie(cthrCookie);

        PrintWriter writer = res.getWriter();
        writer.write(JSON.toJSONString(ResponseUtil.buildSuccResponse(true)));
        writer.close();
        log.info("B2bEnterpriseApplicationImpl enterpriseEscrowAddCookie io close");
    }

    /**
     * 企业代管移除cookie
     * @param req
     * @param res
     */
    @Override
    public void enterpriseEscrowRemoveCookie(HttpServletRequest req, HttpServletResponse res) throws IOException {
        log.info("B2bEnterpriseApplicationImpl enterpriseEscrowRemoveCookie start");
        String erp = LoginContext.getLoginContext().getPin();
        String enterpriseId = req.getParameter("enterpriseId");
        log.info("B2bEnterpriseApplicationImpl enterpriseEscrowRemoveCookie erp={}, enterpriseId={}", erp, enterpriseId);

        Cookie userErpCookie = new Cookie("escrowNo", null);
        userErpCookie.setDomain("jd.com");
        userErpCookie.setMaxAge(0);
        userErpCookie.setPath("/");

        Cookie enterpriseIdCookie = new Cookie("enterpriseNo", null);
        enterpriseIdCookie.setDomain("jd.com");
        enterpriseIdCookie.setMaxAge(0);
        enterpriseIdCookie.setPath("/");

        Cookie cthrCookie = new Cookie("cthr", null);
        cthrCookie.setDomain("jd.com");
        cthrCookie.setMaxAge(0);
        cthrCookie.setPath("/");

        res.addCookie(userErpCookie);
        res.addCookie(enterpriseIdCookie);
        res.addCookie(cthrCookie);

        PrintWriter writer = res.getWriter();
        writer.write(JSON.toJSONString(ResponseUtil.buildSuccResponse(true)));
        writer.close();
        log.info("B2bEnterpriseApplicationImpl enterpriseEscrowRemoveCookie io close");
    }
}
