package com.jdh.o2oservice.b2b.application.enterpriseuser;
import com.jdh.o2oservice.b2b.common.response.PageDto;
import com.jdh.o2oservice.b2b.export.enterpriseuser.cmd.CreateB2bEnterpriseUserCmd;
import com.jdh.o2oservice.b2b.export.enterpriseuser.cmd.DeleteB2bEnterpriseUserCmd;
import com.jdh.o2oservice.b2b.export.enterpriseuser.cmd.UpdateB2bEnterpriseUserCmd;
import com.jdh.o2oservice.b2b.export.enterpriseuser.cmd.UpdateB2bEnterpriseUserStatusCmd;
import com.jdh.o2oservice.b2b.export.enterpriseuser.dto.B2bEnterpriseUserDto;
import com.jdh.o2oservice.b2b.export.enterpriseuser.query.B2bEnterpriseUserPageRequest;
import com.jdh.o2oservice.b2b.export.enterpriseuser.query.B2bEnterpriseUserRequest;

/**
 * 企业用户
 */
public interface B2bEnterpriseUserApplication {

    /**
     * 创建企业用户
     * @param cmd
     * @return
     */
    Boolean createEnterpriseUser(CreateB2bEnterpriseUserCmd cmd);

    /**
     * 修改企业用户
     * @param cmd
     * @return
     */
    Boolean updateEnterpriseUser(UpdateB2bEnterpriseUserCmd cmd);

    /**
     * 通过userPin 查询
     * @param userPin
     * @return
     */
    B2bEnterpriseUserDto findEnterpriseUserByPin(String userPin);

    /**
     * 修改企业用户状态
     * @param cmd
     * @return
     */
    Boolean updateEnterpriseUserStatus(UpdateB2bEnterpriseUserStatusCmd cmd);

    /**
     * 删除企业用户
     * @param cmd
     * @return
     */
    Boolean deleteEnterpriseUser(DeleteB2bEnterpriseUserCmd cmd);

    /**
     * 查询企业用户
     * @param request
     * @return
     */
    B2bEnterpriseUserDto queryEnterpriseUser(B2bEnterpriseUserRequest request);

    /**
     * 分页查询企业用户
     * @param request
     * @return
     */
    PageDto<B2bEnterpriseUserDto> queryPageEnterpriseUser(B2bEnterpriseUserPageRequest request);

}
