package com.jdh.o2oservice.b2b.application.enterprisebill.service;

import com.jdh.o2oservice.b2b.common.response.PageDto;
import com.jdh.o2oservice.b2b.domain.enterprisebill.context.B2bEnterpriseBillQueryContext;
import com.jdh.o2oservice.b2b.export.enterprisebill.cmd.B2bEnterpriseBillCmd;
import com.jdh.o2oservice.b2b.export.enterprisebill.dto.B2bEnterpriseBillDetailDto;
import com.jdh.o2oservice.b2b.export.enterprisebill.dto.B2bEnterpriseBillDto;
import com.jdh.o2oservice.b2b.export.enterprisebill.query.B2bEnterpriseBillRequest;
import com.jdh.o2oservice.b2b.export.enterprisevoucher.query.ExportPromiseDetailPageRequest;

import java.time.LocalDate;

/**
 * B2bEnterpriseBillApplication
 * 企业账单Application
 * <AUTHOR>
 * @version 2025/2/25 15:15
 **/
public interface B2bEnterpriseBillApplication {

    /**
     * 查询企业账单列表
     * @param queryContext
     * @return
     */
    PageDto<B2bEnterpriseBillDto> queryEnterpriseBillPage(B2bEnterpriseBillQueryContext queryContext);
    /**
     * 查询企业某月账单
     * @param queryContext
     * @return
     */
    B2bEnterpriseBillDto queryEnterpriseBillByDate(B2bEnterpriseBillQueryContext queryContext);

    /**
     * 查询企业某月账单明细
     * @param queryContext
     * @return
     */
    PageDto<B2bEnterpriseBillDetailDto> queryEnterpriseBillDetailByDate(B2bEnterpriseBillQueryContext queryContext);

    /**
     * 导出企业月账单明细
     * @param request
     * @return
     */
    Boolean exportEnterpriseBillDetail(B2bEnterpriseBillRequest request);
    /**
     * 企业账单调账
     * @param b2bEnterpriseBillCmd
     * @return
     */
    Boolean adjustEnterpriseBill(B2bEnterpriseBillCmd b2bEnterpriseBillCmd);
    /**
     * 企业账单--确认到账
     * @param b2bEnterpriseBillCmd
     * @return
     */
    Boolean configEnterpriseBill(B2bEnterpriseBillCmd b2bEnterpriseBillCmd);

    /**
     * 企业账单--确认账单
     * @param b2bEnterpriseBillCmd
     * @return
     */
    Boolean confirmEnterpriseBill(B2bEnterpriseBillCmd b2bEnterpriseBillCmd);
    /**
     * 新增企业账单
     * @param b2bEnterpriseBillCmd
     * @return
     */
    Boolean saveEnterpriseBill(B2bEnterpriseBillCmd b2bEnterpriseBillCmd);
    /**
     * 生成企业账单
     * @param
     * @return
     */
    void createEnterpriseBill(String lastMonthFirstDay,String lastMonthLastDay);
}
