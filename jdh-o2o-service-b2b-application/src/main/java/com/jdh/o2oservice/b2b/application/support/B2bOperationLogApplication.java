package com.jdh.o2oservice.b2b.application.support;
import com.jdh.o2oservice.b2b.common.response.PageDto;
import com.jdh.o2oservice.b2b.export.operationLog.cmd.OperationLogCmd;
import com.jdh.o2oservice.b2b.export.operationLog.dto.B2bOperationLogDto;
import com.jdh.o2oservice.b2b.export.operationLog.query.B2bOperationLogRequest;

import java.util.List;

/**
 * 操作日志
 */
public interface B2bOperationLogApplication {

    /**
     * 分页查询操作日志
     * @param request
     * @return
     */
    PageDto<B2bOperationLogDto> queryPageOperationLog(B2bOperationLogRequest request);

    /**
     * 异步保存操作日志
     * @param operationLogCmd
     * @return
     */
    Boolean insertAsyncToLocalDB(OperationLogCmd operationLogCmd);

    /**
     * 异步保存操作日志
     * @param operationLogCmdList
     * @return
     */
    Boolean batchInsertAsyncToLocalDB(List<OperationLogCmd> operationLogCmdList);
}
