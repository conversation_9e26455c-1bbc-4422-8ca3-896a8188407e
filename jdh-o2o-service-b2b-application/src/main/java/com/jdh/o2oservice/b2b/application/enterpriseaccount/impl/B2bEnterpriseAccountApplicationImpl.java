package com.jdh.o2oservice.b2b.application.enterpriseaccount.impl;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.b2b.application.enterprise.convert.B2bEnterpriseApplicationConverter;
import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.b2b.application.enterpriseaccount.B2bEnterpriseAccountApplication;
import com.jdh.o2oservice.b2b.application.enterpriseaccount.convert.B2bEnterpriseAccountConvert;
import com.jdh.o2oservice.b2b.application.enterpriseaccount.enums.ContractStatusEnum;
import com.jdh.o2oservice.b2b.application.enterprisesku.B2bEnterpriseSkuApplication;
import com.jdh.o2oservice.b2b.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.b2b.base.constant.CommonConstant;
import com.jdh.o2oservice.b2b.base.ducc.DuccConfig;
import com.jdh.o2oservice.b2b.base.enums.AccountSourceReceiptTypeEnum;
import com.jdh.o2oservice.b2b.base.enums.AccountfreezeTypeEnum;
import com.jdh.o2oservice.b2b.base.enums.YnStatusEnum;
import com.jdh.o2oservice.b2b.base.exception.BusinessErrorCode;
import com.jdh.o2oservice.b2b.base.exception.BusinessException;
import com.jdh.o2oservice.b2b.base.exception.SystemErrorCode;
import com.jdh.o2oservice.b2b.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.b2b.base.util.AssertUtils;
import com.jdh.o2oservice.b2b.base.util.DateUtil;
import com.jdh.o2oservice.b2b.common.response.PageDto;
import com.jdh.o2oservice.b2b.domain.enterprise.model.JdhB2bEnterprise;
import com.jdh.o2oservice.b2b.domain.enterprise.model.JdhB2bEnterpriseContract;
import com.jdh.o2oservice.b2b.domain.enterprise.model.JdhEnterpriseIdentifier;
import com.jdh.o2oservice.b2b.domain.enterprise.repository.JdhB2bEnterpriseContractRepository;
import com.jdh.o2oservice.b2b.domain.enterprise.repository.JdhB2bEnterpriseRepository;
import com.jdh.o2oservice.b2b.domain.enterprise.repository.query.JdhB2bEnterpriseContractQuery;
import com.jdh.o2oservice.b2b.domain.enterpriseaccount.model.JdhB2bEnterpriseAccountDetail;
import com.jdh.o2oservice.b2b.domain.enterpriseaccount.repository.JdhB2bEnterpriseAccountDetailRepository;
import com.jdh.o2oservice.b2b.domain.enterpriseaccount.repository.JdhB2bEnterpriseAccountRepository;
import com.jdh.o2oservice.b2b.domain.enterpriseaccount.repository.model.JdhB2bEnterpriseAccount;
import com.jdh.o2oservice.b2b.domain.enterpriseaccount.repository.query.JdhB2bEnterpriseAccountDetailQuery;
import com.jdh.o2oservice.b2b.domain.enterpriseaccount.repository.query.JdhB2bEnterpriseAccountQuery;
import com.jdh.o2oservice.b2b.domain.enterprisecontract.bo.EnterpriseContractBo;
import com.jdh.o2oservice.b2b.domain.enterprisecontract.rpc.EnterpriseContractRpc;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.model.EnterpriseVoucher;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.model.EnterpriseVoucherExtend;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.query.EnterpriseVoucherQuery;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.repository.EnterpriseVoucherRepository;
import com.jdh.o2oservice.b2b.export.enterprise.cmd.B2bEnterpriseContractCmd;
import com.jdh.o2oservice.b2b.export.enterprise.query.B2bEnterprisePageRequest;
import com.jdh.o2oservice.b2b.export.enterpriseaccount.cmd.CreateB2bEnterpriseAccountDetailCmd;
import com.jdh.o2oservice.b2b.export.enterpriseaccount.dto.*;
import com.jdh.o2oservice.b2b.export.enterprise.query.B2bEnterpriseRequest;
import com.jdh.o2oservice.b2b.export.enterpriseaccount.query.B2bEnterpriseAccountRequest;
import com.jdh.o2oservice.b2b.export.enterprisesku.dto.B2bEnterpriseSkuDto;
import com.jdh.o2oservice.b2b.export.enterprisesku.query.B2bEnterpriseSkuRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.text.StringSubstitutor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * 企业账户Application
 * <AUTHOR>
 * @version 2025/2/25 15:15
 **/
@Slf4j
@Service
public class B2bEnterpriseAccountApplicationImpl implements B2bEnterpriseAccountApplication{

    /**
     * JdhB2bEnterpriseAccountDetailRepository
     */
    @Autowired
    private JdhB2bEnterpriseAccountDetailRepository jdhB2bEnterpriseAccountDetailRepository;

    /**
     * JdhB2bEnterpriseRepository
     */
    @Autowired
    private JdhB2bEnterpriseRepository jdhB2bEnterpriseRepository;

    /**
     * JdhB2bEnterpriseContractRepository
     */
    @Autowired
    private JdhB2bEnterpriseContractRepository jdhB2bEnterpriseContractRepository;

    /**
     * EnterpriseVoucherRepository
     */
    @Resource
    private EnterpriseVoucherRepository enterpriseVoucherRepository;

    /**
     * EnterpriseContractRpc
     */
    @Resource
    private EnterpriseContractRpc enterpriseContractRpc;

    /**
     * JdhB2bEnterpriseAccountRepository
     */
    @Resource
    private JdhB2bEnterpriseAccountRepository jdhB2bEnterpriseAccountRepository;

    /**
     * GenerateIdFactory
     */
    @Resource
    private GenerateIdFactory generateIdFactory;

    /**
     * DuccConfig
     */
    @Resource
    private DuccConfig duccConfig;

    /**
     * B2bEnterpriseSkuApplication
     */
    @Resource
    private B2bEnterpriseSkuApplication b2bEnterpriseSkuApplication;



    /**
     * 查询企业服务单明细
     *
     * @param request
     * @return
     */
    @Override
    public B2bEnterpriseAcountContractDto queryEnterpriseAccount(B2bEnterpriseRequest request) {
        EnterpriseVoucherQuery query = new EnterpriseVoucherQuery();
        query.setPromiseId(request.getPromiseId());
        EnterpriseVoucher enterpriseVoucher = enterpriseVoucherRepository.queryEnterpriseVoucher(query);
        if(Objects.isNull(enterpriseVoucher)){
            return null;
        }
        JdhB2bEnterpriseAccountDetailQuery queryContext = B2bEnterpriseAccountConvert.ins.queryToContext(request);
        queryContext.setSourceReceiptId(enterpriseVoucher.getEnterpriseVoucherId());
        List<JdhB2bEnterpriseAccountDetail> detailList = jdhB2bEnterpriseAccountDetailRepository.queryEnterpriseAccountDetailList(queryContext);
        B2bEnterpriseAcountContractDto contractDto = new B2bEnterpriseAcountContractDto();
        if(CollectionUtils.isNotEmpty(detailList)){
            BigDecimal serviceAmount = BigDecimal.ZERO;
            for(JdhB2bEnterpriseAccountDetail detail : detailList){
                Integer freezeType = detail.getFreezeType();
                if(freezeType == 1){
                    serviceAmount = serviceAmount.add(detail.getFreezeAmount());
                }else if(freezeType == 2){
                    serviceAmount = serviceAmount.subtract(detail.getFreezeAmount());
                }
                log.info("createEnterpriseAccountDetail serviceAmount,={},queryEnterpriseAccount={}",serviceAmount,JSON.toJSONString(detail));
            }
            JdhB2bEnterpriseAccountDetail detail = detailList.get(0);
            JdhB2bEnterprise dbEnterprise = jdhB2bEnterpriseRepository.find(JdhEnterpriseIdentifier.builder().enterpriseId(detail.getEnterpriseId()).build());
            contractDto.setEnterpriseId(detail.getEnterpriseId());
            contractDto.setEnterName(dbEnterprise.getShortName());
            contractDto.setServiceAmount(serviceAmount.compareTo(BigDecimal.ZERO)>0 ? serviceAmount : BigDecimal.ZERO);
            JdhB2bEnterpriseContract jdhB2bEnterpriseContract = getJdhB2bEnterpriseContract(detail.getEnterpriseId(),queryContext.getPromiseId());
            if(Objects.nonNull(jdhB2bEnterpriseContract)){
                contractDto.setContractNumber(jdhB2bEnterpriseContract.getContractNumber());
//                Integer settlementPeriod = jdhB2bEnterpriseContract.getSettlementPeriod();
//                Integer settlementDays = jdhB2bEnterpriseContract.getSettlementDays();
//                Long daysDifference = getSettlementDays(settlementPeriod, settlementDays);
//                contractDto.setSettlementDays(daysDifference.intValue());
            }
        }
        return contractDto;
    }

    /**
     * 查询合同信息
     *
     * @param request
     * @return
     */
    @Override
    public B2bEnterpriseContractDetailDto queryAccountInfoByContractNum(B2bEnterpriseRequest request) {
        AssertUtils.nonNull(request, SystemErrorCode.PARAM_NULL_ERROR);
        AssertUtils.hasText(request.getContractNumber(), SystemErrorCode.CONTRACT_NUM_NULL);
        EnterpriseContractBo enterpriseContractBo = enterpriseContractRpc.queryContractInfo(request.getContractNumber());
        if(Objects.nonNull(enterpriseContractBo)){
            return B2bEnterpriseAccountConvert.ins.convertToContractDetailDto(enterpriseContractBo);
        }
        return null;
    }

    /**
     * @param cmd
     * @return
     */
    @Override
    public boolean saveEnterpriseContract(B2bEnterpriseContractCmd cmd) {
        AssertUtils.nonNull(cmd, SystemErrorCode.PARAM_NULL_ERROR);
        AssertUtils.nonNull(cmd.getEnterpriseId(), SystemErrorCode.CONTRACT_NUM_NULL);
        JdhB2bEnterpriseContractQuery contractQuery = new JdhB2bEnterpriseContractQuery();
        contractQuery.setContractNumber(cmd.getContractNumber());
        contractQuery.setEnterpriseId(cmd.getEnterpriseId());
        JdhB2bEnterpriseContract enterpriseContract = jdhB2bEnterpriseContractRepository.findJdhB2bEnterpriseContract(contractQuery);
        AssertUtils.isTrue(Objects.nonNull(enterpriseContract), SystemErrorCode.CONTRACT_IS_EXIST);
        JdhB2bEnterpriseContract jdhB2bEnterpriseContract = B2bEnterpriseAccountConvert.ins.convertToJdhB2bEnterpriseContract(cmd);
        jdhB2bEnterpriseContractRepository.save(jdhB2bEnterpriseContract);
        return true;
    }

    /**
     * @param cmd
     * @return
     */
    @Override
    public boolean deleteEnterpriseContract(B2bEnterpriseContractCmd cmd) {
        AssertUtils.nonNull(cmd, SystemErrorCode.PARAM_NULL_ERROR);
        AssertUtils.nonNull(cmd.getContractId(), SystemErrorCode.PARAM_NULL_ERROR);
        JdhB2bEnterpriseContractQuery enterpriseContractQuery = new JdhB2bEnterpriseContractQuery();
        enterpriseContractQuery.setContractId(cmd.getContractId());
        JdhB2bEnterpriseContract jdhB2bEnterpriseContract = jdhB2bEnterpriseContractRepository.findJdhB2bEnterpriseContract(enterpriseContractQuery);
        AssertUtils.nonNull(jdhB2bEnterpriseContract, SystemErrorCode.CONTRACT_NULL);

        B2bEnterpriseSkuRequest request = new B2bEnterpriseSkuRequest();
        request.setContractNumber(jdhB2bEnterpriseContract.getContractNumber());
        List<B2bEnterpriseSkuDto> result = b2bEnterpriseSkuApplication.queryEnterpriseSkusByCondition(request);
        AssertUtils.isEmpty(result, SystemErrorCode.CONTRACT_SKU_IS_EXIST);

        jdhB2bEnterpriseContractRepository.delete(jdhB2bEnterpriseContract);
        return true;
    }

    /**
     * 创建企业账户明细
     * @param cmd
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.application.enterpriseaccount.impl.B2bEnterpriseAccountApplicationImpl.createEnterpriseAccountDetail")
    @Transactional
    public Boolean createEnterpriseAccountDetail(CreateB2bEnterpriseAccountDetailCmd cmd) {
        log.info("B2bEnterpriseAccountApplicationImpl createEnterpriseAccountDetail cmd={}", JSON.toJSONString(cmd));
        List<JdhB2bEnterpriseAccount> enterpriseAccountList = jdhB2bEnterpriseAccountRepository.findList(JdhB2bEnterpriseAccountQuery.builder()
                .enterpriseId(cmd.getEnterpriseId()).build());
        log.info("createEnterpriseAccountDetail enterpriseAccountList={}", JSON.toJSONString(enterpriseAccountList));
        if (CollectionUtils.isEmpty(enterpriseAccountList)){
            throw new BusinessException(BusinessErrorCode.ENTERPRISE_ACCOUNT_NO_EXIST);
        }
        JdhB2bEnterpriseAccount enterpriseAccount = enterpriseAccountList.get(0);
        AccountSourceReceiptTypeEnum typeEnum = AccountSourceReceiptTypeEnum.getEnumByType(cmd.getSourceReceiptType());
        switch (typeEnum){
            case VOUCHER:
                return createAccountDetailVoucher(cmd, enterpriseAccount);
            case BILL_PAYMENT:
                return createAccountDetailBillPayment(cmd, enterpriseAccount);
            default:
                return false;
        }
    }

    /**
     * voucher
     * @param cmd
     * @param enterpriseAccount
     * @return
     */
    private Boolean createAccountDetailVoucher(CreateB2bEnterpriseAccountDetailCmd cmd, JdhB2bEnterpriseAccount enterpriseAccount){
        log.info("B2bEnterpriseAccountApplicationImpl createAccountDetailVoucher cmd={}, enterpriseAccount={}", JSON.toJSONString(cmd), JSON.toJSONString(enterpriseAccount));
        JdhB2bEnterpriseAccountDetail accountDetail = B2bEnterpriseAccountConvert.ins.convertToEnterpriseAccountDetail(cmd);
        accountDetail.setAccountId(enterpriseAccount.getAccountId());
        accountDetail.setAccountDetailId(generateIdFactory.getId());
        log.info("createAccountDetailVoucher accountDetail={}", JSON.toJSONString(accountDetail));
        jdhB2bEnterpriseAccountDetailRepository.save(accountDetail);
        if (AccountfreezeTypeEnum.FREEZE.getType().equals(cmd.getFreezeType())){
            enterpriseAccount.setFreezeAmount(enterpriseAccount.getFreezeAmount().add(cmd.getFreezeAmount()));
        }else if (AccountfreezeTypeEnum.RELEASE.getType().equals(cmd.getFreezeType())){
            enterpriseAccount.setFreezeAmount(enterpriseAccount.getFreezeAmount().subtract(cmd.getFreezeAmount()));
        }
        log.info("createAccountDetailVoucher enterpriseAccount={}", JSON.toJSONString(enterpriseAccount));
        jdhB2bEnterpriseAccountRepository.update(enterpriseAccount);
        return true;
    }

    /**
     * 账单打款
     * @param cmd
     * @param enterpriseAccount
     * @return
     */
    private Boolean createAccountDetailBillPayment(CreateB2bEnterpriseAccountDetailCmd cmd, JdhB2bEnterpriseAccount enterpriseAccount){
        log.info("B2bEnterpriseAccountApplicationImpl createAccountDetailBillPayment cmd={}, enterpriseAccount={}", JSON.toJSONString(cmd), JSON.toJSONString(enterpriseAccount));
        JdhB2bEnterpriseAccountDetail accountDetail = B2bEnterpriseAccountConvert.ins.convertToEnterpriseAccountDetail(cmd);
        accountDetail.setAccountId(enterpriseAccount.getAccountId());
        accountDetail.setAccountDetailId(generateIdFactory.getId());
        accountDetail.setSourceReceiptId(0L);
        accountDetail.setSourceReceiptType(cmd.getSourceReceiptType());
        accountDetail.setFreezeType(cmd.getFreezeType());
        log.info("createAccountDetailBillPayment accountDetail={}", JSON.toJSONString(accountDetail));
        jdhB2bEnterpriseAccountDetailRepository.save(accountDetail);

        enterpriseAccount.setFreezeAmount(enterpriseAccount.getFreezeAmount().subtract(cmd.getFreezeAmount()));
        log.info("createAccountDetailBillPayment enterpriseAccount={}", JSON.toJSONString(enterpriseAccount));
        jdhB2bEnterpriseAccountRepository.update(enterpriseAccount);
        return true;
    }

    /**
     * 分页查询企业合同
     *
     * @param pageRequest
     * @return
     */
    @Override
    public PageDto<B2bEnterpriseAcountContractDto> queryPageEnterpriseContract(B2bEnterprisePageRequest pageRequest) {
        JdhB2bEnterpriseContractQuery enterpriseContractQuery = new JdhB2bEnterpriseContractQuery();
        enterpriseContractQuery.setEnterpriseId(pageRequest.getEnterpriseId());
        enterpriseContractQuery.setPageNum(pageRequest.getPageNum());
        enterpriseContractQuery.setPageSize(pageRequest.getPageSize());
        Page<JdhB2bEnterpriseContract> page = jdhB2bEnterpriseContractRepository.queryPageEnterpriseContract(enterpriseContractQuery);
        PageDto<B2bEnterpriseAcountContractDto> result = new PageDto<>();
        result.setTotalPage(page.getPages());
        result.setPageNum(page.getCurrent());
        result.setPageSize(page.getSize());
        result.setTotalCount(page.getTotal());
        result.setList(entity2EnterpriseContractDtoList(page.getRecords()));
        return result;
    }

    /**
     * 校验企业合同有效性
     * 4-已签署 + 有效期之内
     *
     * @param contractNumber
     * @return
     */
    @Override
    public Boolean checkEnterpriseContract(Long enterpriseId,String contractNumber) {
        AssertUtils.hasText(contractNumber, SystemErrorCode.CONTRACT_NUM_NULL);
        EnterpriseContractBo enterpriseContractBo = enterpriseContractRpc.queryContractInfo(contractNumber);
        AssertUtils.nonNull(enterpriseContractBo, SystemErrorCode.CONTRACT_NULL);
        if(ContractStatusEnum.SIGNED.getType().equals(enterpriseContractBo.getStatus())
                && enterpriseContractBo.getValid() == CommonConstant.ONE){
            Date startTime = enterpriseContractBo.getStartTime();
            Date endTime = enterpriseContractBo.getEndTime();
            Boolean isExpired = new Date().after(startTime) && new Date().before(endTime);
            AssertUtils.isFalse(isExpired,SystemErrorCode.CONTRACT_EXPIRE);
            JdhB2bEnterpriseContract jdhB2bEnterprise = new JdhB2bEnterpriseContract();
            jdhB2bEnterprise.setEnterpriseId(enterpriseId);
            jdhB2bEnterprise.setContractNumber(contractNumber);
            jdhB2bEnterprise.setStatus(enterpriseContractBo.getStatus());
            jdhB2bEnterpriseContractRepository.updateContract(jdhB2bEnterprise);
            return true;
        }else{
            AssertUtils.isFalse(true,SystemErrorCode.CONTRACT_NOT_SIGN);
        }
        return false;
    }

    /**
     * 查询信用额度提醒
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.application.enterpriseaccount.impl.B2bEnterpriseAccountApplicationImpl.queryAccountCreditAmountTip")
    @Transactional
    public B2bEnterpriseAccountCreditAmountTipDto queryAccountCreditAmountTip(B2bEnterpriseAccountRequest request) {
        // 返回结果
        B2bEnterpriseAccountCreditAmountTipDto result = new B2bEnterpriseAccountCreditAmountTipDto();

        // 查询企业账户
        List<JdhB2bEnterpriseAccount> enterpriseAccountList = jdhB2bEnterpriseAccountRepository.findList(JdhB2bEnterpriseAccountQuery.builder()
                .enterpriseId(request.getEnterpriseId()).build());
        log.info("B2bEnterpriseAccountApplicationImpl queryAccountCreditAmountTip enterpriseAccountList={}", JSON.toJSONString(enterpriseAccountList));
        if (CollectionUtils.isEmpty(enterpriseAccountList)){
            return null;
        }
        JdhB2bEnterpriseAccount enterpriseAccount = enterpriseAccountList.get(0);
        // 信用额度
        BigDecimal creditAmount = enterpriseAccount.getCreditAmount();
        // 冻结金额
        BigDecimal freezeAmount = enterpriseAccount.getFreezeAmount();
        // 剩余额度
        BigDecimal remainingAmount = creditAmount.subtract(freezeAmount);

        JSONObject configObj = JSON.parseObject(duccConfig.getCreditAmountTipContent());
        if (remainingAmount.compareTo(BigDecimal.ZERO)>0){// 剩余信用额度 > 0
            result.setTipType(1);
            String tipContent = "";
            Map<String, String> dataMap = new HashMap<>(2);
            dataMap.put("remainingAmount", remainingAmount.toString());

            JdhB2bEnterpriseAccountDetailQuery accountDetailQuery = JdhB2bEnterpriseAccountDetailQuery.builder()
                    .enterpriseId(request.getEnterpriseId())
                    .sourceReceiptType(AccountSourceReceiptTypeEnum.VOUCHER.getType())
                    .freezeType(AccountfreezeTypeEnum.FREEZE.getType())
                    .build();
            List<JdhB2bEnterpriseAccountDetail> accountDetails = jdhB2bEnterpriseAccountDetailRepository.queryEnterpriseAccountDetailList(accountDetailQuery);
            log.info("queryAccountCreditAmountTip accountDetailQuery={}, accountDetails={}", JSON.toJSONString(accountDetailQuery), JSON.toJSONString(accountDetails));
            if (CollectionUtils.isNotEmpty(accountDetails) && accountDetails.size() >= 10){
                BigDecimal sumFreezeAmount = accountDetails.subList(0, 10).stream().map(JdhB2bEnterpriseAccountDetail::getFreezeAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal basicSettlementAvgAmount = sumFreezeAmount.divide(new BigDecimal(10), 0, RoundingMode.HALF_UP);
                // 可预约服务次数预估算法：取最近10个履约单的基础结算金额算平均值，然后剩余信用额度除以这个平均值，四舍五入
                BigDecimal appointmentCount = remainingAmount.divide(basicSettlementAvgAmount, 0, RoundingMode.HALF_UP);
                dataMap.put("appointmentCount", appointmentCount.toString());
                tipContent = configObj.getString("availableAmountAppointmentTip");
            }else {
                tipContent = configObj.getString("availableAmountTip");
            }
            StringSubstitutor sub = new StringSubstitutor(dataMap);
            tipContent = sub.replace(tipContent);
            result.setTipContent(tipContent);
        }else {
            result.setTipType(2);
            result.setTipContent(configObj.getString("noAvailableAmountTip"));
        }

        BigDecimal occupyScale = freezeAmount.divide(creditAmount, 10, RoundingMode.HALF_UP);
        // 当企业的已使用额度达到信用额度的 80%及以上时，全局展示红色提醒
        BigDecimal threshold = new BigDecimal("0.8");
        if (occupyScale.compareTo(threshold) > 0) {
            result.setTipRed(1);
        } else {
            result.setTipRed(2);
        }
        return result;
    }

    /**
     * 查询企业账户信息
     * @param request
     * @return
     */
    @Override
    public B2bEnterpriseAccountInfoDto queryEnterpriseAccountInfo(B2bEnterpriseAccountRequest request) {
        List<JdhB2bEnterpriseAccount> enterpriseAccountList = jdhB2bEnterpriseAccountRepository.findList(JdhB2bEnterpriseAccountQuery.builder()
                .enterpriseId(request.getEnterpriseId()).build());
        if (CollectionUtils.isNotEmpty(enterpriseAccountList)){
            return  B2bEnterpriseAccountConvert.ins.convertToEnterpriseAccountDto(enterpriseAccountList.get(0));
        }
        return null;
    }

    /**
     *
     * @param enterpriseList
     * @return
     */
    private List<B2bEnterpriseAcountContractDto> entity2EnterpriseContractDtoList(List<JdhB2bEnterpriseContract> enterpriseList){
        List<B2bEnterpriseAcountContractDto> dtoList = new ArrayList<>();
        for (JdhB2bEnterpriseContract enterpriseContract : enterpriseList){
            B2bEnterpriseAcountContractDto enterpriseContractDto = B2bEnterpriseApplicationConverter.INS.entity2EnterpriseContractDto(enterpriseContract);
            enterpriseContractDto.setContractName(enterpriseContract.getName());
            enterpriseContractDto.setContractDuration(DateUtil.formatDate(enterpriseContract.getStartTime(), CommonConstant.YMD) + "-" +
            DateUtil.formatDate(enterpriseContract.getEndTime(), CommonConstant.YMD));
            enterpriseContractDto.setStatusDesc(ContractStatusEnum.getEnumDescByType(enterpriseContract.getStatus()));
            enterpriseContractDto.setValidDesc(YnStatusEnum.getDescByCode(enterpriseContract.getValid()));
            dtoList.add(enterpriseContractDto);
        }
        return dtoList;
    }

    /**
     * 获取执行中的合同
     * @param enterpriseId
     * @return
     */
    private JdhB2bEnterpriseContract getJdhB2bEnterpriseContract(Long enterpriseId,Long promiseId){
        EnterpriseVoucherQuery query = EnterpriseVoucherQuery.builder().enterpriseVoucherId(enterpriseId).promiseId(promiseId).build();
        EnterpriseVoucher enterpriseVoucher = enterpriseVoucherRepository.queryEnterpriseVoucher(query);
        JdhB2bEnterpriseContractQuery enterpriseContractQuery = new JdhB2bEnterpriseContractQuery();
        enterpriseContractQuery.setEnterpriseId(enterpriseId);
        if(Objects.nonNull(enterpriseVoucher)){
            EnterpriseVoucherExtend extend = enterpriseVoucher.getExtend();
            String contractNumber = extend.getContractNumber();
            enterpriseContractQuery.setContractNumber(contractNumber);
        }else {
            enterpriseContractQuery.setValid(1);
//            enterpriseContractQuery.setStatus();
        }
        JdhB2bEnterpriseContract jdhB2bEnterpriseContract = jdhB2bEnterpriseContractRepository.findJdhB2bEnterpriseContract(enterpriseContractQuery);
        return jdhB2bEnterpriseContract;
    }

    /**
     *
     * @param settlementPeriod
     * @param settlementDays
     * @return
     */
    private Long getSettlementDays(Integer settlementPeriod,Integer settlementDays){
        LocalDate today = LocalDate.now();
        LocalDate threeMonthsLater = null;
        if(Objects.nonNull(settlementDays) && settlementDays > 0){
            //        1-日 2-月 3-季度 4-年 5-周 6-半月 7-其他时间
            if(settlementPeriod == 1){
                threeMonthsLater = today.plusDays(settlementDays);
            }else if(settlementPeriod == 2){
                threeMonthsLater = today.plusMonths(settlementDays);
            }else if(settlementPeriod == 3){
                threeMonthsLater = today.plusMonths(settlementDays * 3);
            }else if(settlementPeriod == 4){
                threeMonthsLater = today.plusYears(settlementDays);
            }else if(settlementPeriod == 5){
                threeMonthsLater = today.plusWeeks(settlementDays);
            }else if(settlementPeriod == 6){
                threeMonthsLater = today.plusMonths(settlementDays);
                long daysDifference = ChronoUnit.DAYS.between(today, threeMonthsLater);
                return daysDifference / 2;
            }else{
                threeMonthsLater = today.plusMonths(1);
            }
        }else{
            threeMonthsLater = today.plusMonths(1);
        }

        long daysDifference = ChronoUnit.DAYS.between(today, threeMonthsLater);
        return daysDifference;
    }
}
