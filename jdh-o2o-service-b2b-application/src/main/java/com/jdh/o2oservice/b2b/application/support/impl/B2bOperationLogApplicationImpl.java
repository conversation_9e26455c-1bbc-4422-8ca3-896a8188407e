package com.jdh.o2oservice.b2b.application.support.impl;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.b2b.application.enterpriseuser.B2bEnterpriseUserApplication;
import com.jdh.o2oservice.b2b.application.support.B2bOperationLogApplication;
import com.jdh.o2oservice.b2b.application.support.converter.B2bOperationLogApplicationConverter;
import com.jdh.o2oservice.b2b.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.b2b.base.enums.OpResultTypeEnum;
import com.jdh.o2oservice.b2b.base.enums.OpTypeEnum;
import com.jdh.o2oservice.b2b.base.enums.ThreadPoolConfigEnum;
import com.jdh.o2oservice.b2b.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.b2b.common.response.PageDto;
import com.jdh.o2oservice.b2b.domain.support.operationlog.mode.JdhB2bOperationLog;
import com.jdh.o2oservice.b2b.domain.support.operationlog.repository.JdhB2bOperationLogRepository;
import com.jdh.o2oservice.b2b.domain.support.operationlog.repository.query.JdhB2bOperationLogQuery;
import com.jdh.o2oservice.b2b.export.operationLog.cmd.OperationLogCmd;
import com.jdh.o2oservice.b2b.export.operationLog.dto.B2bOperationLogDto;
import com.jdh.o2oservice.b2b.export.operationLog.query.B2bOperationLogRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 操作日志
 */
@Slf4j
@Service
public class B2bOperationLogApplicationImpl implements B2bOperationLogApplication {

    @Resource
    private JdhB2bOperationLogRepository jdhB2bOperationLogRepository;

    /**
     * 企业用户应用
     */
    @Resource
    private B2bEnterpriseUserApplication b2bEnterpriseUserApplication;

    /**
     * 分页查询操作日志
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.application.support.impl.B2bOperationLogApplicationImpl.queryPageOperationLog")
    public PageDto<B2bOperationLogDto> queryPageOperationLog(B2bOperationLogRequest request) {
        JdhB2bOperationLogQuery operationLogQuery = JdhB2bOperationLogQuery.builder()
                .id(request.getId())
                .enterpriseId(request.getEnterpriseId())
                .operateType(request.getOperateType())
                .resultType(request.getResultType())
                .operator(request.getOperator())
                .pageNum(request.getPageNum())
                .pageSize(request.getPageSize())
                .build();
        Page<JdhB2bOperationLog> page = jdhB2bOperationLogRepository.queryPageOperationLog(operationLogQuery);
        log.info("B2bOperationLogApplicationImpl queryPageOperationLog page={}", JSON.toJSONString(page));
        PageDto<B2bOperationLogDto> result = new PageDto<>();
        result.setTotalPage(page.getPages());
        result.setPageNum(page.getCurrent());
        result.setPageSize(page.getSize());
        result.setTotalCount(page.getTotal());
        result.setList(B2bOperationLogApplicationConverter.INS.entity2Dto(page.getRecords()));
        if (CollectionUtils.isNotEmpty(result.getList())){
            result.getList().forEach(r->{
                r.setOperateTypeDesc(OpTypeEnum.getDescOfType(r.getOperateType()));
                r.setResultTypeDesc(OpResultTypeEnum.getDescOfType(r.getResultType()));
            });
        }
        return result;
    }

    /**
     * 异步保存操作日志
     * @param operationLogCmd
     * @return
     */
    @Override
    public Boolean insertAsyncToLocalDB(OperationLogCmd operationLogCmd) {
        CompletableFuture.runAsync(() -> {
            jdhB2bOperationLogRepository.save(B2bOperationLogApplicationConverter.INS.cmd2Entity(operationLogCmd));
        }, ExecutorPoolFactory.get(ThreadPoolConfigEnum.OPLOG_SAVE_THREAD_POOL));
        return true;
    }

    /**
     * 异步保存操作日志
     * @param operationLogCmdList
     * @return
     */
    @Override
    public Boolean batchInsertAsyncToLocalDB(List<OperationLogCmd> operationLogCmdList) {
        log.info("B2bOperationLogApplicationImpl batchInsertAsyncToLocalDB operationLogCmdList={}", JSON.toJSONString(operationLogCmdList));
        operationLogCmdList.forEach(this::insertAsyncToLocalDB);
        return true;
    }
}
