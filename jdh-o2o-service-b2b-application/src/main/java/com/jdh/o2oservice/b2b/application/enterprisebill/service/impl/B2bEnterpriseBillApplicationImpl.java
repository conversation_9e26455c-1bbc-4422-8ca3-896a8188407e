package com.jdh.o2oservice.b2b.application.enterprisebill.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.jdh.o2oservice.b2b.application.enterpriseaccount.B2bEnterpriseAccountApplication;
import com.jdh.o2oservice.b2b.application.enterprisebill.convert.B2bEnterpriseBillConvert;
import com.jdh.o2oservice.b2b.application.enterprisebill.service.B2bEnterpriseBillApplication;
import com.jdh.o2oservice.b2b.application.enterprisevoucher.converter.EnterpriseVoucherAppConvert;
import com.jdh.o2oservice.b2b.application.support.B2bOperationLogApplication;
import com.jdh.o2oservice.b2b.application.support.converter.B2bOperationLogApplicationConverter;
import com.jdh.o2oservice.b2b.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.b2b.base.constant.CommonConstant;
import com.jdh.o2oservice.b2b.base.enums.AccountSourceReceiptTypeEnum;
import com.jdh.o2oservice.b2b.base.enums.AccountfreezeTypeEnum;
import com.jdh.o2oservice.b2b.base.enums.OpTypeEnum;
import com.jdh.o2oservice.b2b.base.enums.ThreadPoolConfigEnum;
import com.jdh.o2oservice.b2b.base.exception.SystemErrorCode;
import com.jdh.o2oservice.b2b.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.b2b.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.b2b.base.util.DateUtil;
import com.jdh.o2oservice.b2b.base.util.AssertUtils;
import com.jdh.o2oservice.b2b.common.response.PageDto;
import com.jdh.o2oservice.b2b.domain.enterpriseaccount.model.JdhB2bEnterpriseAccountDetail;
import com.jdh.o2oservice.b2b.domain.enterpriseaccount.repository.JdhB2bEnterpriseAccountDetailRepository;
import com.jdh.o2oservice.b2b.domain.enterpriseaccount.repository.query.JdhB2bEnterpriseAccountDetailQuery;
import com.jdh.o2oservice.b2b.domain.enterprisebill.context.B2bEnterpriseBillQueryContext;
import com.jdh.o2oservice.b2b.domain.enterprisebill.enums.BillStatusEnum;
import com.jdh.o2oservice.b2b.domain.enterprisebill.enums.EnterpriseBillErrorCode;
import com.jdh.o2oservice.b2b.domain.enterprisebill.model.JdhB2bEnterpriseBill;
import com.jdh.o2oservice.b2b.domain.enterprisebill.model.JdhB2bEnterpriseBillDetail;
import com.jdh.o2oservice.b2b.domain.enterprisebill.repository.JdhB2bEnterpriseBillRepository;
import com.jdh.o2oservice.b2b.domain.enterprisebill.repository.JdhB2bEnterpriseBillDetailRepository;
import com.jdh.o2oservice.b2b.domain.enterprisesku.model.JdhB2bEnterpriseSku;
import com.jdh.o2oservice.b2b.domain.enterprisesku.rpc.O2oProductJsfExportRpc;
import com.jdh.o2oservice.b2b.domain.enterprisesku.rpc.bo.JdhSkuResBo;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.cmd.EnterpriseVoucherCmd;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.enums.PromiseStatusEnum;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.model.*;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.query.EnterpriseVoucherPageQuery;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.repository.EnterpriseVoucherRepository;
import com.jdh.o2oservice.b2b.domain.support.operationlog.mode.JdhB2bOperationLogResult;
import com.jdh.o2oservice.b2b.export.enterpriseaccount.cmd.CreateB2bEnterpriseAccountDetailCmd;
import com.jdh.o2oservice.b2b.export.enterprisebill.cmd.B2bEnterpriseBillCmd;
import com.jdh.o2oservice.b2b.export.enterprisebill.dto.B2bEnterpriseBillDetailDto;
import com.jdh.o2oservice.b2b.export.enterprisebill.dto.B2bEnterpriseBillDto;
import com.jdh.o2oservice.b2b.export.enterprisebill.query.B2bEnterpriseBillRequest;
import com.jdh.o2oservice.b2b.export.enterprisevoucher.dto.CompletePromiseDetailsDto;
import com.jdh.o2oservice.b2b.export.enterprisevoucher.dto.PromiseDetailsDto;
import com.jdh.o2oservice.b2b.export.enterprisevoucher.query.PromiseDetailPageRequest;
import com.jdh.o2oservice.b2b.export.operationLog.cmd.OperationLogCmd;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.support.JdhExportToolsExportService;
import com.jdh.o2oservice.export.support.dto.PutFileResultDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 企业账单Application
 * <AUTHOR>
 * @version 2025/2/25 15:15
 **/
@Slf4j
@Service
public class B2bEnterpriseBillApplicationImpl implements B2bEnterpriseBillApplication {

    /**
     * jdhB2bEnterpriseBillRepository
     */
    @Autowired
    private JdhB2bEnterpriseBillRepository jdhB2bEnterpriseBillRepository;
    /**
     * jdhB2bEnterpriseBillDetailRepository
     */
    @Autowired
    private JdhB2bEnterpriseBillDetailRepository jdhB2bEnterpriseBillDetailRepository;
    /**
     * enterpriseVoucherRepository
     */
    @Autowired
    private EnterpriseVoucherRepository enterpriseVoucherRepository;
    /**
     * jdhB2bEnterpriseAccountDetailRepository
     */
    @Autowired
    private JdhB2bEnterpriseAccountDetailRepository jdhB2bEnterpriseAccountDetailRepository;
    /**
     *
     */
    @Resource
    private GenerateIdFactory generateIdFactory;
    /** */
    private static final List<Integer> FINISH_STATUS = new ArrayList<>(PromiseStatusEnum.FINISH_STATUS);

    /**
     * b2bEnterpriseAccountApplication
     */
    @Autowired
    private B2bEnterpriseAccountApplication b2bEnterpriseAccountApplication;

    /**
     * O2oProductJsfExportRpc
     */
    @Resource
    private O2oProductJsfExportRpc productJsfExportRpc;
    /**
     * jdhExportToolsExportService
     */
    @Resource
    private JdhExportToolsExportService jdhExportToolsExportService;
    /**
     * operationLogApplication
     */
    @Resource
    private B2bOperationLogApplication operationLogApplication;

    /**
     * 查询企业账单列表
     *
     * @param queryContext
     * @return
     */
    @Override
    public PageDto<B2bEnterpriseBillDto> queryEnterpriseBillPage(B2bEnterpriseBillQueryContext queryContext) {
        Page<JdhB2bEnterpriseBill> pageResult = jdhB2bEnterpriseBillRepository.queryEnterpriseBillPage(queryContext);
        return B2bEnterpriseBillConvert.ins.entity2B2bEnterpriseBillDtoPage(pageResult);
    }

    /**
     * 查询企业某月账单
     *
     * @param queryContext
     * @return
     */
    @Override
    public B2bEnterpriseBillDto queryEnterpriseBillByDate(B2bEnterpriseBillQueryContext queryContext) {
        JdhB2bEnterpriseBill jdhB2bEnterpriseBill = jdhB2bEnterpriseBillRepository.queryEnterpriseBillByDate(queryContext);
        return B2bEnterpriseBillConvert.ins.entity2B2bEnterpriseBillDto(jdhB2bEnterpriseBill);
    }

    /**
     * 查询企业某月账单明细
     *
     * @param queryContext
     * @return
     */
    @Override
    public PageDto<B2bEnterpriseBillDetailDto> queryEnterpriseBillDetailByDate(B2bEnterpriseBillQueryContext queryContext) {
        JdhB2bEnterpriseBill jdhB2bEnterpriseBill = jdhB2bEnterpriseBillRepository.queryEnterpriseBillByDate(queryContext);
        log.info("B2bEnterpriseBillApplicationImpl queryEnterpriseBillDetailByDate queryContext={}, jdhB2bEnterpriseBill={}", JSON.toJSONString(queryContext)
                , JSON.toJSONString(jdhB2bEnterpriseBill));
        if (Objects.isNull(jdhB2bEnterpriseBill)){
            return new PageDto<>();
        }
        queryContext.setBillId(jdhB2bEnterpriseBill.getBillId());
        Page<JdhB2bEnterpriseBillDetail> page = jdhB2bEnterpriseBillDetailRepository.queryEnterpriseBillDetailPage(queryContext);
        log.info("queryEnterpriseBillDetailByDate page={}", JSON.toJSONString(page));
        List<JdhB2bEnterpriseBillDetail> detailList = page.getRecords();
        PageDto<B2bEnterpriseBillDetailDto> pageDto = new PageDto<>();
        if(CollUtil.isNotEmpty(detailList)){
            List<Long> enterVoucherIdList = detailList.stream().map(JdhB2bEnterpriseBillDetail::getEnterpriseVoucherId).collect(Collectors.toList());
            EnterpriseVoucherPageQuery query = EnterpriseVoucherPageQuery.builder().enterpriseVoucherIdList(enterVoucherIdList).build();
            List<EnterpriseVoucher> enterpriseVoucherList = enterpriseVoucherRepository.queryEnterpriseVoucherList(query);
            log.info("queryEnterpriseBillDetailByDate queryEnterpriseVoucherList query={}, enterpriseVoucherList={}", JSON.toJSONString(query), JSON.toJSONString(enterpriseVoucherList));
            if(CollUtil.isNotEmpty(enterpriseVoucherList)){
                List<Long> skuIdList = detailList.stream()
                        .map(JdhB2bEnterpriseBillDetail::getSkuShortName)
                        .filter(StringUtils::isNotBlank)
                        .filter(skuShortName -> !Objects.equals("null", skuShortName))
                        .map(Long::parseLong)
                        .collect(Collectors.toList());
                Map<Long, JdhSkuResBo> skuMap = getJdhSkuResMap(skuIdList);
                List<B2bEnterpriseBillDetailDto> detailDtoList = buildDetailDtoList(enterpriseVoucherList, skuMap,detailList);
                pageDto.setList(detailDtoList);
                pageDto.setPageSize(page.getSize());
                pageDto.setTotalPage(page.getPages());
                pageDto.setPageNum(page.getCurrent());
                pageDto.setTotalCount(page.getTotal());
                return pageDto;
            }
        }
        return pageDto;
    }

    /**
     * 导出企业月账单明细
     *
     * @param request
     * @return
     */
    @Override
    public Boolean exportEnterpriseBillDetail(B2bEnterpriseBillRequest request) {
        CompletableFuture.runAsync(() -> {
            // 最大导出1万条
            request.setPageSize(10000);
            PageDto<B2bEnterpriseBillDetailDto> pageDto = this.queryEnterpriseBillDetailPage(request);
            Map<String, Object> exportMap = new HashMap<>();
            if (Objects.nonNull(pageDto) && CollectionUtils.isNotEmpty(pageDto.getList())){
                exportMap.put("data", pageDto.getList());
            }
            exportMap.put("userPin", request.getUserPin());
            exportMap.put("scene", "enterpriseBillDetailExport");
            exportMap.put("operationType", "enterpriseBillDetailExport");
            Response<PutFileResultDto> exportResult = jdhExportToolsExportService.exportAndDownloadFile(exportMap);
            if(Objects.nonNull(exportResult) && Objects.nonNull(exportResult.getData())){
                PutFileResultDto data = exportResult.getData();
                // 保存操作记录
                OperationLogCmd operationLogCmd = new OperationLogCmd();
                operationLogCmd.setEnterpriseId(request.getEnterpriseId());
                operationLogCmd.setOperator(request.getUserPin());
                operationLogCmd.setBizUnionId("enterpriseBillDetailExport");
                operationLogCmd.setBizSceneDesc("下载账单");
                operationLogCmd.setBizSceneKey("com.jdh.o2oservice.b2b.application.enterprisebill.service.impl.B2bEnterpriseBillApplicationImpl.exportEnterpriseBillDetail");
                operationLogCmd.setOperateType(OpTypeEnum.DOWNLOAD_BILL.getType());
                operationLogCmd.setResult(JSON.toJSONString(JdhB2bOperationLogResult.builder().resultAttachment(data.getFileUrl()).resultAttachmentName("账单.xlsx").build()));
                operationLogCmd.setResultType(2);
                operationLogApplication.batchInsertAsyncToLocalDB(Collections.singletonList(operationLogCmd));
            }
        }, ExecutorPoolFactory.get(ThreadPoolConfigEnum.DEFAULT));
        return true;
    }

    /**
     *
     * @param pageRequest
     * @return
     */
    private PageDto<B2bEnterpriseBillDetailDto> queryEnterpriseBillDetailPage(B2bEnterpriseBillRequest pageRequest) {
        B2bEnterpriseBillQueryContext queryContext = new B2bEnterpriseBillQueryContext();
        queryContext.setEnterpriseId(pageRequest.getEnterpriseId());
        queryContext.setBillDate(pageRequest.getBillDate());
        queryContext.setEnterpriseSkuIdSet(pageRequest.getEnterpriseSkuIdSet());
        queryContext.setPromiseStatusSet(pageRequest.getPromiseStatusSet());
        queryContext.setPromiseId(pageRequest.getPromiseId());
        queryContext.setStatus(pageRequest.getStatus());
        queryContext.setOrderTimeStart(pageRequest.getOrderTimeStart());
        queryContext.setOrderTimeEnd(pageRequest.getOrderTimeEnd());
        PageDto<B2bEnterpriseBillDetailDto> result = this.queryEnterpriseBillDetailByDate(queryContext);
        return result;
    }

    /**
     *
     * @param skuIdList
     * @return
     */
    private Map<Long, JdhSkuResBo> getJdhSkuResMap(List<Long> skuIdList){
        Map<Long, JdhSkuResBo> skuMap = productJsfExportRpc.batchQueryJdhSkuInfo(skuIdList);
        return skuMap;
    }

    /**
     *
     * @param enterpriseVoucherList
     * @param detailList
     * @return
     */
    private List<B2bEnterpriseBillDetailDto> buildDetailDtoList(List<EnterpriseVoucher> enterpriseVoucherList,Map<Long, JdhSkuResBo> skuMap,
                                                                List<JdhB2bEnterpriseBillDetail> detailList) {
        List<B2bEnterpriseBillDetailDto> detailDtoList = new ArrayList<>();
        Map<Long,JdhB2bEnterpriseBillDetail> billDetailMap = detailList.stream().collect(
                Collectors.toMap(JdhB2bEnterpriseBillDetail::getEnterpriseVoucherId,buildDetail -> buildDetail , (t1,t2) -> t2));
        enterpriseVoucherList.forEach(enterpriseVoucher ->{
            B2bEnterpriseBillDetailDto detailDto = B2bEnterpriseBillConvert.ins.queryToB2bEnterpriseBillDetailDto(enterpriseVoucher);
            log.info("buildDetailDtoList detailDto={}",JSON.toJSONString(detailDto));
            EnterpriseVoucherExtend extend = enterpriseVoucher.getExtend();
            log.info("buildDetailDtoList extend={}",JSON.toJSONString(extend));
            if(Objects.nonNull(extend)){
                PromiseAddress address = extend.getAddress();
                detailDto.setFullAddress(Objects.nonNull(address) && StringUtils.isNotEmpty(address.getFullAddress())?address.getFullAddress():"");
                List<PromisePatient> patientList = extend.getPatientList();
                if(CollUtil.isNotEmpty(patientList)){
                    StringBuilder nameList = new StringBuilder();
                    patientList.forEach(promisePatient -> {
                        nameList.append(promisePatient.getName());
                    });
                    detailDto.setNameList(nameList.toString());
                }
                PromiseAppointmentTime appointmentTime = extend.getAppointmentTime();
                detailDto.setAppointmentStartTime(Objects.nonNull(appointmentTime) && StringUtils.isNotEmpty(appointmentTime.getAppointmentStartTime())?appointmentTime.getAppointmentStartTime():"");
                detailDto.setFinishTime(DateUtil.formatDate(enterpriseVoucher.getUpdateTime(), CommonConstant.YMDHMS));
                detailDto.setOrderTime(DateUtil.formatDate(enterpriseVoucher.getCreateTime(),CommonConstant.YMDHMS));
                JdhB2bEnterpriseBillDetail jdhB2bEnterpriseBillDetail = billDetailMap.get(enterpriseVoucher.getEnterpriseVoucherId());
                if(Objects.isNull(jdhB2bEnterpriseBillDetail)){
                    return; // 如果找不到对应的账单明细，跳过当前记录
                }
                String skuIdTemp = jdhB2bEnterpriseBillDetail.getSkuShortName();
                Long skuId = 0L;
                if(StringUtils.isBlank(skuIdTemp) || Objects.equals("null", skuIdTemp)){
                    PromiseSku service = extend.getService();
                    log.info("buildDetailDtoList service={}",JSON.toJSONString(service));
                    skuId = Objects.nonNull(service) && Objects.nonNull(service.getSkuId()) ? service.getSkuId() : 0L;
                }else{
                    skuId = Long.parseLong(skuIdTemp);
                }

                JdhSkuResBo skuResBo = skuMap.get(skuId);
                if (Objects.nonNull(skuResBo)) {
                    detailDto.setSkuId(skuResBo.getSkuId());
                    detailDto.setSkuName(StringUtils.isNotBlank(skuResBo.getShortTitle()) ? skuResBo.getShortTitle() : skuResBo.getSkuName());
                    detailDto.setSkuShortName(skuResBo.getShortTitle());
                }
                detailDto.setBillAmount(jdhB2bEnterpriseBillDetail.getSettlementAmount());
                detailDto.setPromiseStatusDesc(B2bEnterpriseBillConvert.ins.promiseBillStatusConvert(jdhB2bEnterpriseBillDetail.getStatus()));
                detailDtoList.add(detailDto);
            }
        });
        return detailDtoList;
    }

    /**
     * 企业账单调账
     *
     * @param b2bEnterpriseBillCmd
     * @return
     */
    @Override
    public Boolean adjustEnterpriseBill(B2bEnterpriseBillCmd b2bEnterpriseBillCmd) {
        AssertUtils.nonNull(b2bEnterpriseBillCmd, SystemErrorCode.PARAM_NULL_ERROR);
        AssertUtils.nonNull(b2bEnterpriseBillCmd.getBillId(), EnterpriseBillErrorCode.BILL_ID_NULL);
        AssertUtils.hasText(b2bEnterpriseBillCmd.getAdjustAmount(), EnterpriseBillErrorCode.BILL_ADJUST_NULL);
        String adjustDescribe = b2bEnterpriseBillCmd.getAdjustDescribe();
        AssertUtils.isTrue(StringUtils.isNotEmpty(adjustDescribe) && adjustDescribe.length() > 2000, EnterpriseBillErrorCode.CONFIRM_BILL_STATUS_FAIL);

        String adjustAmountStr = b2bEnterpriseBillCmd.getAdjustAmount();
        AssertUtils.isFalse(NumberUtil.isNumber(adjustAmountStr), EnterpriseBillErrorCode.ADJUST_AMOUNT_FAIL);
        String pattern = "^-?(([1-9]{1}\\d*)|(0{1}))(\\.\\d{1,2})?$";
        AssertUtils.isFalse(adjustAmountStr.matches(pattern), EnterpriseBillErrorCode.ADJUST_AMOUNT_OUT);

        B2bEnterpriseBillQueryContext queryContext = new B2bEnterpriseBillQueryContext();
        queryContext.setBillId(b2bEnterpriseBillCmd.getBillId());
        JdhB2bEnterpriseBill jdhB2bEnterpriseBill = jdhB2bEnterpriseBillRepository.queryEnterpriseBill(queryContext);
        AssertUtils.nonNull(jdhB2bEnterpriseBill, EnterpriseBillErrorCode.BILL_NOT_EXIST);

        AssertUtils.isFalse((BillStatusEnum.CONFIRMING.getStatus().equals(jdhB2bEnterpriseBill.getBillStatus()) ||
                BillStatusEnum.CONFIRMED.getStatus().equals(jdhB2bEnterpriseBill.getBillStatus())), EnterpriseBillErrorCode.CONFIRM_BILL_STATUS_FAIL);

        BigDecimal adjustAmount = new BigDecimal(b2bEnterpriseBillCmd.getAdjustAmount());
        BigDecimal finalAmount = jdhB2bEnterpriseBill.getBillAmount().add(adjustAmount);
        AssertUtils.isTrue(finalAmount.compareTo(BigDecimal.ZERO) < 0, EnterpriseBillErrorCode.ADJUST_AMOUNT_OUT_BILL);

        JdhB2bEnterpriseBill cmdToJdhB2bEnterpriseBill = B2bEnterpriseBillConvert.ins.cmdToJdhB2bEnterpriseBill(b2bEnterpriseBillCmd);
        cmdToJdhB2bEnterpriseBill.setAdjustAmount(adjustAmount);
        cmdToJdhB2bEnterpriseBill.setFinalAmount(finalAmount);
        cmdToJdhB2bEnterpriseBill.setUpdateUser(b2bEnterpriseBillCmd.getAdjustUser());
        cmdToJdhB2bEnterpriseBill.setBillStatus(1);
        jdhB2bEnterpriseBillRepository.updateEnterpriseBill(cmdToJdhB2bEnterpriseBill);
        return Boolean.TRUE;
    }

    /**
     * 企业账单--确认到账
     *
     * @param b2bEnterpriseBillCmd
     * @return
     */
    @Override
    public Boolean configEnterpriseBill(B2bEnterpriseBillCmd b2bEnterpriseBillCmd) {
        AssertUtils.nonNull(b2bEnterpriseBillCmd, SystemErrorCode.PARAM_NULL_ERROR);
        AssertUtils.nonNull(b2bEnterpriseBillCmd.getBillId(), EnterpriseBillErrorCode.BILL_ID_NULL);
        B2bEnterpriseBillQueryContext queryContext = new B2bEnterpriseBillQueryContext();
        queryContext.setBillId(b2bEnterpriseBillCmd.getBillId());
        JdhB2bEnterpriseBill jdhB2bEnterpriseBill = jdhB2bEnterpriseBillRepository.queryEnterpriseBill(queryContext);
        AssertUtils.nonNull(jdhB2bEnterpriseBill, EnterpriseBillErrorCode.BILL_NOT_EXIST);
        AssertUtils.isFalse(BillStatusEnum.CONFIRMED.getStatus().equals(jdhB2bEnterpriseBill.getBillStatus()), EnterpriseBillErrorCode.CONFIRM_BILL_STATUS_FAIL);

        if(updateEnterpriseAccount(jdhB2bEnterpriseBill)){
            JdhB2bEnterpriseBill cmdToJdhB2bEnterpriseBill = B2bEnterpriseBillConvert.ins.cmdToJdhB2bEnterpriseBill(b2bEnterpriseBillCmd);
            cmdToJdhB2bEnterpriseBill.setBillStatus(BillStatusEnum.ACCOUNT.getStatus());
            return jdhB2bEnterpriseBillRepository.updateEnterpriseBill(cmdToJdhB2bEnterpriseBill) > 0;
        }
        return Boolean.FALSE;
    }

    /**
     * 企业账单--确认账单
     *
     * @param b2bEnterpriseBillCmd
     * @return
     */
    @Override
    public Boolean confirmEnterpriseBill(B2bEnterpriseBillCmd b2bEnterpriseBillCmd) {
        AssertUtils.nonNull(b2bEnterpriseBillCmd, SystemErrorCode.PARAM_NULL_ERROR);
        AssertUtils.nonNull(b2bEnterpriseBillCmd.getBillId(), EnterpriseBillErrorCode.BILL_ID_NULL);
        B2bEnterpriseBillQueryContext queryContext = new B2bEnterpriseBillQueryContext();
        queryContext.setBillId(b2bEnterpriseBillCmd.getBillId());
        JdhB2bEnterpriseBill jdhB2bEnterpriseBill = jdhB2bEnterpriseBillRepository.queryEnterpriseBill(queryContext);
        AssertUtils.nonNull(jdhB2bEnterpriseBill, EnterpriseBillErrorCode.BILL_NOT_EXIST);
        AssertUtils.isFalse(BillStatusEnum.CONFIRMING.getStatus().equals(jdhB2bEnterpriseBill.getBillStatus()), EnterpriseBillErrorCode.CONFIRM_BILL_STATUS_FAIL);

        JdhB2bEnterpriseBill cmdToJdhB2bEnterpriseBill = B2bEnterpriseBillConvert.ins.cmdToJdhB2bEnterpriseBill(b2bEnterpriseBillCmd);
        cmdToJdhB2bEnterpriseBill.setBillStatus(BillStatusEnum.CONFIRMED.getStatus());
        return jdhB2bEnterpriseBillRepository.updateEnterpriseBill(cmdToJdhB2bEnterpriseBill) > 0;
    }

    /**
     * 确认到账更新企业可用余额
     * @param jdhB2bEnterpriseBill
     * @return
     */
    private Boolean updateEnterpriseAccount(JdhB2bEnterpriseBill jdhB2bEnterpriseBill){
        CreateB2bEnterpriseAccountDetailCmd cmd = new CreateB2bEnterpriseAccountDetailCmd();
        cmd.setEnterpriseId(jdhB2bEnterpriseBill.getEnterpriseId());
        cmd.setSourceReceiptType(AccountSourceReceiptTypeEnum.BILL_PAYMENT.getType());
        cmd.setFreezeType(AccountfreezeTypeEnum.RELEASE.getType());
        cmd.setFreezeAmount(jdhB2bEnterpriseBill.getFinalAmount());
        return b2bEnterpriseAccountApplication.createEnterpriseAccountDetail(cmd);
    }

    /**
     * 新增企业账单
     *
     * @param b2bEnterpriseBillCmd
     * @return
     */
    @Override
    public Boolean saveEnterpriseBill(B2bEnterpriseBillCmd b2bEnterpriseBillCmd) {
        JdhB2bEnterpriseBill cmdToJdhB2bEnterpriseBill = B2bEnterpriseBillConvert.ins.cmdToJdhB2bEnterpriseBill(b2bEnterpriseBillCmd);
        cmdToJdhB2bEnterpriseBill.setBillId(generateIdFactory.getId());
        jdhB2bEnterpriseBillRepository.saveEnterpriseBill(cmdToJdhB2bEnterpriseBill);
        return Boolean.TRUE;
    }

    /**
     * 生成企业账单
     *
     * @param lastMonthFirstDay
     * @param lastMonthLastDay
     * @return
     */
    @Override
    public void createEnterpriseBill(String lastMonthFirstDay, String lastMonthLastDay) {
        log.info("[B2bEnterpriseBillApplicationImpl.createEnterpriseBill] lastMonthFirstDay={} lastMonthLastDay={}", lastMonthFirstDay, lastMonthLastDay);
        EnterpriseVoucherPageQuery query = EnterpriseVoucherPageQuery.builder().build();
        query.setPromiseStatusList(FINISH_STATUS);
        query.setStatistics(0);
        query.setLastMonthFirstDay(lastMonthFirstDay);
        query.setLastMonthLastDay(lastMonthLastDay);
        List<EnterpriseVoucher> enterpriseVoucherList = enterpriseVoucherRepository.queryEnterpriseVoucherList(query);
        if (CollUtil.isNotEmpty(enterpriseVoucherList)) {
            Map<Long, List<EnterpriseVoucher>> enterpriseVoucherMap = enterpriseVoucherList.stream().
                    collect(Collectors.groupingBy(EnterpriseVoucher::getEnterpriseId));
            enterpriseVoucherMap.forEach((enterpriseId, list) -> {
                Map<Long,EnterpriseVoucher> enterpriseVoucherIdMap = list.stream().collect(Collectors.
                        toMap(EnterpriseVoucher::getEnterpriseVoucherId,voucher -> voucher,(t1,t2) -> t2));
                enterpriseBill(enterpriseId,enterpriseVoucherIdMap,lastMonthFirstDay);
            });
            List<Long> enterpriseVoucherIdList = enterpriseVoucherList.stream().map(EnterpriseVoucher::getEnterpriseVoucherId).collect(Collectors.toList());
            EnterpriseVoucherCmd enterpriseVoucherCmd = EnterpriseVoucherCmd.builder().enterpriseVoucherIdList(enterpriseVoucherIdList).build();
            enterpriseVoucherRepository.updateVoucherStatistics(enterpriseVoucherCmd);
        }
        log.info("[B2bEnterpriseBillApplicationImpl.createEnterpriseBill] end");
    }

    /**
     *
     * @param enterpriseId
     * @param enterpriseVoucherIdMap
     */
    private void enterpriseBill(Long enterpriseId,Map<Long,EnterpriseVoucher> enterpriseVoucherIdMap,String lastMonthFirstDay){
        Set<Long> enterpriseVoucherIdSet = enterpriseVoucherIdMap.keySet();
        log.info("[B2bEnterpriseBillApplicationImpl.enterpriseBill] enterpriseVoucherIdSet={}", JSON.toJSON(enterpriseVoucherIdSet));

        // 查jdh_b2b_enterprise_account_detail企业账户明细信息表，关联上对应的冻结金额
        List<Long> sourceReceiptIdList = new ArrayList<>(enterpriseVoucherIdSet);
        JdhB2bEnterpriseAccountDetailQuery queryContext = new JdhB2bEnterpriseAccountDetailQuery();
        queryContext.setEnterpriseId(enterpriseId);
        queryContext.setSourceReceiptIdList(sourceReceiptIdList);
        List<JdhB2bEnterpriseAccountDetail> enterpriseAccountDetailList = jdhB2bEnterpriseAccountDetailRepository.queryEnterpriseAccountDetailList(queryContext);
        Map<Long,List<JdhB2bEnterpriseAccountDetail>> accountDetailMap = enterpriseAccountDetailList.stream().
                collect(Collectors.groupingBy(JdhB2bEnterpriseAccountDetail::getSourceReceiptId));
        // 生成账单 + 账单明细
        JdhB2bEnterpriseBill b2bEnterpriseBill = new JdhB2bEnterpriseBill();
        b2bEnterpriseBill.setEnterpriseId(enterpriseId);
        b2bEnterpriseBill.setBillId(generateIdFactory.getId());
        b2bEnterpriseBill.setBillStatus(1);
        b2bEnterpriseBill.setBillDate(getBillDate(lastMonthFirstDay));

        List<JdhB2bEnterpriseBillDetail> enterpriseBillDetailList = new ArrayList<>();
        AtomicReference<BigDecimal> billSettlementAmount = new AtomicReference<>(BigDecimal.ZERO);

        enterpriseVoucherIdMap.forEach((enterpriseVoucherId, enterpriseVoucher) -> {
            List<JdhB2bEnterpriseAccountDetail> accountDetailList = accountDetailMap.get(enterpriseVoucherId);
            if (CollUtil.isNotEmpty(accountDetailList)) {
                AtomicReference<BigDecimal> settlementAmount = new AtomicReference<>(BigDecimal.ZERO);
                accountDetailList.forEach(accountDetail ->{
                    if(accountDetail.getFreezeType() ==  1){
                        settlementAmount.set(settlementAmount.get().add(accountDetail.getFreezeAmount()));
                    }else {
                        settlementAmount.set(settlementAmount.get().subtract(accountDetail.getFreezeAmount()));
                    }
                });

                billSettlementAmount.set(billSettlementAmount.get().add(settlementAmount.get()));
                JdhB2bEnterpriseBillDetail enterpriseBillDetail = new JdhB2bEnterpriseBillDetail();
                enterpriseBillDetail.setBillId(b2bEnterpriseBill.getBillId());
                enterpriseBillDetail.setEnterpriseId(enterpriseId);
                enterpriseBillDetail.setBillDetailId(generateIdFactory.getId());
                enterpriseBillDetail.setEnterpriseVoucherId(enterpriseVoucherId);
                enterpriseBillDetail.setSettlementAmount(settlementAmount.get());
                EnterpriseVoucherExtend extend = enterpriseVoucher.getExtend();
                if(PromiseStatusEnum.CANCEL.getStatus().equals(enterpriseVoucher.getPromiseStatus())){
                    Integer cancelResponsibleType = extend.getCancelResponsibleType();
                    enterpriseBillDetail.setStatus(cancelResponsibleType == 0 ? 2 : 3);
                }else {
                    enterpriseBillDetail.setStatus(1);
                }
                enterpriseBillDetail.setPromiseId(enterpriseVoucher.getPromiseId());
                PromiseSku service = extend.getService();
                if(Objects.nonNull(service)){
                    enterpriseBillDetail.setSkuName(String.valueOf(service.getEnterpriseSkuId()));
                    enterpriseBillDetail.setSkuShortName(String.valueOf(service.getSkuId()));
                }
                enterpriseBillDetail.setOrderTime(enterpriseVoucher.getCreateTime());
                enterpriseBillDetailList.add(enterpriseBillDetail);
            }
        });
        b2bEnterpriseBill.setBillAmount(billSettlementAmount.get());

        jdhB2bEnterpriseBillRepository.saveEnterpriseBill(b2bEnterpriseBill);
        if(CollUtil.isNotEmpty(enterpriseBillDetailList)){
            jdhB2bEnterpriseBillDetailRepository.batchSaveEnterpriseBillDetail(enterpriseBillDetailList);
        }
    }

    private String getBillDate(String lastMonthFirstDay){
        return lastMonthFirstDay.substring(0,7);
    }
}
