package com.jdh.o2oservice.b2b.application.support.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.b2b.application.enterpriseuser.B2bEnterpriseUserApplication;
import com.jdh.o2oservice.b2b.application.support.OrderPlatformApplication;
import com.jdh.o2oservice.b2b.application.support.converter.OrderPlatformConverter;
import com.jdh.o2oservice.b2b.base.constant.NumConstant;
import com.jdh.o2oservice.b2b.base.exception.BusinessException;
import com.jdh.o2oservice.b2b.base.exception.DynamicErrorCode;
import com.jdh.o2oservice.b2b.common.response.PageDto;
import com.jdh.o2oservice.b2b.domain.support.orderplatform.context.CreateOrderPlatformCtx;
import com.jdh.o2oservice.b2b.domain.support.orderplatform.factory.OrderPlatformFactory;
import com.jdh.o2oservice.b2b.domain.support.orderplatform.model.OrderPlatform;
import com.jdh.o2oservice.b2b.domain.support.orderplatform.repository.OrderPlatformRepository;
import com.jdh.o2oservice.b2b.domain.support.orderplatform.repository.query.OrderPlatformPageQuery;
import com.jdh.o2oservice.b2b.export.enterpriseuser.dto.B2bEnterpriseUserDto;
import com.jdh.o2oservice.b2b.export.orderplatform.cmd.CreateOrderPlatformCmd;
import com.jdh.o2oservice.b2b.export.orderplatform.dto.OrderPlatformDto;
import com.jdh.o2oservice.b2b.export.orderplatform.query.OrderPlatformPageRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * OrderPlatformApplicationImpl
 *
 * <AUTHOR>
 * @date 2025/02/25
 */
@Slf4j
@Service
public class OrderPlatformApplicationImpl implements OrderPlatformApplication {

    /**
     * orderPlatformRepository
     */
    @Autowired
    private OrderPlatformRepository orderPlatformRepository;

    /**
     * 企业用户应用
     */
    @Resource
    private B2bEnterpriseUserApplication b2bEnterpriseUserApplication;

    /**
     * 创建订单平台
     *
     * @param cmd cmd
     * @return {@link Boolean }
     */
    @Override
    public Boolean createOrderPlatform(CreateOrderPlatformCmd cmd) {
        //1、工厂创建
        CreateOrderPlatformCtx createOrderPlatformCtx = OrderPlatformConverter.INS.cmd2Ctx(cmd);

        OrderPlatform orderPlatform = OrderPlatformFactory.createOrderPlatform(createOrderPlatformCtx);

        if(Objects.isNull(orderPlatform)){
            return false;
        }

        //2、save
        int saved = orderPlatformRepository.save(orderPlatform);
        return NumConstant.NUM_1.equals(saved);
    }

    /**
     * 查询订单平台页面
     *
     * @param pageRequest 页面请求
     * @return {@link PageDto }<{@link OrderPlatformDto }>
     */
    @Override
    public PageDto<OrderPlatformDto> queryOrderPlatformPage(OrderPlatformPageRequest pageRequest) {
        Page<OrderPlatform> page = orderPlatformRepository.queryPage(OrderPlatformPageQuery.builder()
                .enterpriseId(pageRequest.getEnterpriseId())
                .pageSize(pageRequest.getPageSize())
                .pageNum(pageRequest.getPageNum())
                .sourceOrderPlatform(pageRequest.getSourceOrderPlatform())
                .build());

        PageDto<OrderPlatformDto> pageDto = new PageDto<>();
        pageDto.setList(OrderPlatformConverter.INS.entity2DtoList(page.getRecords()));
        pageDto.setTotalPage(page.getPages());
        pageDto.setTotalCount(page.getTotal());
        return pageDto;
    }
}
