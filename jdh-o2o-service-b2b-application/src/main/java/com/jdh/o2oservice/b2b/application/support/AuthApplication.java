package com.jdh.o2oservice.b2b.application.support;

import com.jdh.o2oservice.b2b.domain.support.user.uim.bo.Uim2DimResourceBO;
import com.jdh.o2oservice.b2b.domain.support.user.uim.bo.Uim2MenuBO;
import com.jdh.o2oservice.b2b.domain.support.user.uim.bo.Uim2RoleBO;
import com.jdh.o2oservice.b2b.domain.support.user.uim.bo.UserBO;
import com.jdh.o2oservice.b2b.domain.support.user.uim.param.Uim2UserContext;

import java.util.List;

/**
 * @Author: duanqiaona1
 * @Date: 2024/4/25 2:24 下午
 * @Description:
 */
public interface AuthApplication {


    /**
     * 查询菜单列表
     *
     * @param userContext
     * @return
     */
    List<Uim2MenuBO> getMenuTree(Uim2UserContext userContext);


    /**
     *
     * @param erpCode
     * @return
     */
    UserBO getUserInfo(String erpCode);

    /**
     * 查询用户拥有的全部角色
     *
     * @param erp erp
     * @return list
     */
    List<Uim2RoleBO> getRole(String erp);

    /**
     * getDimResource
     *
     * @param erp ERP
     * @return {@link List}<{@link Uim2DimResourceBO}>
     */
    List<Uim2DimResourceBO> getDimResource(String erp);
}
