package com.jdh.o2oservice.b2b.application.support.converter;
import com.jdh.o2oservice.b2b.domain.support.operationlog.mode.JdhB2bOperationLog;
import com.jdh.o2oservice.b2b.export.operationLog.cmd.OperationLogCmd;
import com.jdh.o2oservice.b2b.export.operationLog.dto.B2bOperationLogDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;
@Mapper
public interface B2bOperationLogApplicationConverter {

    B2bOperationLogApplicationConverter INS = Mappers.getMapper(B2bOperationLogApplicationConverter.class);

    List<B2bOperationLogDto> entity2Dto(List<JdhB2bOperationLog> operationLogList);

    List<JdhB2bOperationLog> cmd2Entity(List<OperationLogCmd> operationLogCmdList);

    JdhB2bOperationLog cmd2Entity(OperationLogCmd operationLogCmd);
}
