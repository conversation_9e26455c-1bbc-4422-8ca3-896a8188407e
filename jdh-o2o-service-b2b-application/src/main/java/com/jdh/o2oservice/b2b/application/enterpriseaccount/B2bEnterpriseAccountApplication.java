package com.jdh.o2oservice.b2b.application.enterpriseaccount;

import com.jdh.o2oservice.b2b.common.response.PageDto;
import com.jdh.o2oservice.b2b.export.enterprise.cmd.B2bEnterpriseContractCmd;
import com.jdh.o2oservice.b2b.export.enterprise.query.B2bEnterpriseRequest;
import com.jdh.o2oservice.b2b.export.enterpriseaccount.cmd.CreateB2bEnterpriseAccountDetailCmd;
import com.jdh.o2oservice.b2b.export.enterprise.query.B2bEnterprisePageRequest;
import com.jdh.o2oservice.b2b.export.enterpriseaccount.dto.*;
import com.jdh.o2oservice.b2b.export.enterpriseaccount.query.B2bEnterpriseAccountRequest;

/**
 * @Description 企业账户服务
 * @Date 2025/2/24 下午3:07
 * <AUTHOR>
public interface B2bEnterpriseAccountApplication {

    /**
     * 查询企业服务单明细
     * @param request
     * @return
     */
    B2bEnterpriseAcountContractDto queryEnterpriseAccount(B2bEnterpriseRequest request);

    /**
     * 查询合同信息
     * @param request
     * @return
     */
    B2bEnterpriseContractDetailDto queryAccountInfoByContractNum(B2bEnterpriseRequest request);

    /**
     * 保存合同
     * @param cmd
     * @return
     */
    boolean saveEnterpriseContract(B2bEnterpriseContractCmd cmd);

    /**
     * 删除合同
     * @param cmd
     * @return
     */
    boolean deleteEnterpriseContract(B2bEnterpriseContractCmd cmd);

    /**
     * 创建企业账户明细
     * @param cmd
     * @return
     */
    Boolean createEnterpriseAccountDetail(CreateB2bEnterpriseAccountDetailCmd cmd);

    /**
     * 分页查询企业合同
     * @param pageRequest
     * @return
     */
    PageDto<B2bEnterpriseAcountContractDto> queryPageEnterpriseContract(B2bEnterprisePageRequest pageRequest);

    /**
     * 校验企业合同有效性
     * 4-已签署 + 有效期之内
     * @param contractNumber
     * @return
     */
    Boolean checkEnterpriseContract(Long enterpriseId,String contractNumber);

    /**
     * 查询信用额度提醒
     * @param request
     * @return
     */
    B2bEnterpriseAccountCreditAmountTipDto queryAccountCreditAmountTip(B2bEnterpriseAccountRequest request);

    /**
     * 查询企业账户信息
     * @param request
     * @return
     */
    B2bEnterpriseAccountInfoDto queryEnterpriseAccountInfo(B2bEnterpriseAccountRequest request);
}
