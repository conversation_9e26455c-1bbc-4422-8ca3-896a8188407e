package com.jdh.o2oservice.b2b.application.enterpriseremark.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.b2b.application.enterpriseremark.B2bEnterpriseRemarkApplication;
import com.jdh.o2oservice.b2b.application.enterpriseremark.convert.EnterpriseRemarkAppConvert;
import com.jdh.o2oservice.b2b.application.enterpriseuser.B2bEnterpriseUserApplication;
import com.jdh.o2oservice.b2b.base.exception.BusinessException;
import com.jdh.o2oservice.b2b.base.exception.DynamicErrorCode;
import com.jdh.o2oservice.b2b.base.exception.SystemErrorCode;
import com.jdh.o2oservice.b2b.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.b2b.common.response.PageDto;
import com.jdh.o2oservice.b2b.domain.enterpriseremark.cmd.EnterpriseRemarkCmd;
import com.jdh.o2oservice.b2b.domain.enterpriseremark.factory.EnterpriseRemarkFactory;
import com.jdh.o2oservice.b2b.domain.enterpriseremark.model.EnterpriseRemark;
import com.jdh.o2oservice.b2b.domain.enterpriseremark.query.EnterpriseRemarkPageQuery;
import com.jdh.o2oservice.b2b.domain.enterpriseremark.query.EnterpriseRemarkQuery;
import com.jdh.o2oservice.b2b.domain.enterpriseremark.repository.EnterpriseRemarkRepository;
import com.jdh.o2oservice.b2b.export.enterpriseremark.cmd.AddEnterpriseRemarkCmd;
import com.jdh.o2oservice.b2b.export.enterpriseremark.cmd.DeleteEnterpriseRemarkCmd;
import com.jdh.o2oservice.b2b.export.enterpriseremark.dto.EnterpriseRemarkDto;
import com.jdh.o2oservice.b2b.export.enterpriseremark.query.EnterpriseRemarkPageRequest;
import com.jdh.o2oservice.b2b.export.enterpriseremark.query.EnterpriseRemarkRequest;
import com.jdh.o2oservice.b2b.export.enterpriseuser.dto.B2bEnterpriseUserDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 企业备注应用层实现
 *
 * <AUTHOR>
 * @date 2025/03/10
 */
@Slf4j
@Service
public class B2bEnterpriseRemarkApplicationImpl implements B2bEnterpriseRemarkApplication {

    /**
     * 企业备注仓储
     */
    @Resource
    private EnterpriseRemarkRepository enterpriseRemarkRepository;

    /**
     * 企业用户应用
     */
    @Resource
    private B2bEnterpriseUserApplication b2bEnterpriseUserApplication;

    /**
     * ID生成工厂
     */
    @Resource
    private GenerateIdFactory generateIdFactory;

    /**
     * 添加企业备注
     *
     * @param cmd 添加命令
     * @return 是否成功
     */
    @Override
    public Boolean addEnterpriseRemark(AddEnterpriseRemarkCmd cmd) {
        // 参数校验
        if (Objects.isNull(cmd)) {
            throw new BusinessException(SystemErrorCode.PARAM_NULL_ERROR.formatDescription("cmd"));
        }
        if (StringUtils.isBlank(cmd.getContent())) {
            throw new BusinessException(new DynamicErrorCode("S0011", "备注内容不能为空"));
        }
        if (StringUtils.isBlank(cmd.getUserPin())) {
            throw new BusinessException(new DynamicErrorCode("S0011", "用户PIN不能为空"));
        }

        // 生成企业备注ID
        Long enterpriseRemarkId = generateIdFactory.getId();

        // 构建领域命令
        EnterpriseRemarkCmd remarkCmd = EnterpriseRemarkAppConvert.INS.toEnterpriseRemarkCmd(cmd);
        remarkCmd.setEnterpriseRemarkId(enterpriseRemarkId);

        // 创建企业备注
        EnterpriseRemark enterpriseRemark = EnterpriseRemarkFactory.createEnterpriseRemark(remarkCmd);

        // 保存企业备注
        Integer result = enterpriseRemarkRepository.save(enterpriseRemark);
        return result > 0;
    }

    /**
     * 查询企业备注
     *
     * @param request 请求对象
     * @return 企业备注
     */
    @Override
    public EnterpriseRemarkDto queryEnterpriseRemark(EnterpriseRemarkRequest request) {
        // 参数校验
        if (Objects.isNull(request)) {
            throw new BusinessException(SystemErrorCode.PARAM_NULL_ERROR.formatDescription("request"));
        }
        if (Objects.isNull(request.getEnterpriseRemarkId())) {
            throw new BusinessException(new DynamicErrorCode("S0011", "企业备注ID不能为空"));
        }

        // 构建查询对象
        EnterpriseRemarkQuery query = EnterpriseRemarkAppConvert.INS.toEnterpriseRemarkQuery(request);

        // 查询企业备注
        EnterpriseRemark enterpriseRemark = enterpriseRemarkRepository.queryEnterpriseRemark(query);
        if (Objects.isNull(enterpriseRemark)) {
            return null;
        }

        // 转换为DTO
        return EnterpriseRemarkAppConvert.INS.toEnterpriseRemarkDto(enterpriseRemark);
    }

    /**
     * 分页查询企业备注
     *
     * @param request 分页请求对象
     * @return 分页结果
     */
    @Override
    public PageDto<EnterpriseRemarkDto> queryEnterpriseRemarkPage(EnterpriseRemarkPageRequest request) {
        // 参数校验
        if (Objects.isNull(request)) {
            throw new BusinessException(SystemErrorCode.PARAM_NULL_ERROR.formatDescription("request"));
        }
        if (StringUtils.isBlank(request.getUserPin())) {
            throw new BusinessException(new DynamicErrorCode("S0011", "用户PIN不能为空"));
        }

        // 构建查询对象
        EnterpriseRemarkPageQuery query = EnterpriseRemarkAppConvert.INS.toEnterpriseRemarkPageQuery(request);

        // 分页查询企业备注
        Page<EnterpriseRemark> page = enterpriseRemarkRepository.queryEnterpriseRemarkPage(query);

        // 构建分页结果
        PageDto<EnterpriseRemarkDto> result = new PageDto<>();
        result.setPageNum(page.getCurrent());
        result.setPageSize(page.getSize());
        result.setTotalCount(page.getTotal());
        result.setTotalPage(page.getPages());
        result.setList(EnterpriseRemarkAppConvert.INS.toEnterpriseRemarkDtoList(page.getRecords()));
        return result;
    }

    /**
     * 查询企业备注列表
     *
     * @param request 请求对象
     * @return 企业备注列表
     */
    @Override
    public List<EnterpriseRemarkDto> queryEnterpriseRemarkList(EnterpriseRemarkRequest request) {
        // 参数校验
        if (Objects.isNull(request)) {
            throw new BusinessException(SystemErrorCode.PARAM_NULL_ERROR.formatDescription("request"));
        }
        if (Objects.isNull(request.getEnterpriseId())) {
            throw new BusinessException(new DynamicErrorCode("S0011", "企业ID不能为空"));
        }

        // 构建查询对象
        EnterpriseRemarkQuery query = EnterpriseRemarkAppConvert.INS.toEnterpriseRemarkQuery(request);

        // 查询企业备注列表
        List<EnterpriseRemark> enterpriseRemarks = enterpriseRemarkRepository.queryEnterpriseRemarkList(query);

        // 转换为DTO列表
        return EnterpriseRemarkAppConvert.INS.toEnterpriseRemarkDtoList(enterpriseRemarks);
    }

    /**
     * 删除企业备注
     *
     * @param cmd 删除命令
     * @return 是否成功
     */
    @Override
    public Boolean deleteEnterpriseRemark(DeleteEnterpriseRemarkCmd cmd) {
        // 参数校验
        if (Objects.isNull(cmd)) {
            throw new BusinessException(SystemErrorCode.PARAM_NULL_ERROR.formatDescription("cmd"));
        }
        if (Objects.isNull(cmd.getEnterpriseRemarkId())) {
            throw new BusinessException(new DynamicErrorCode("S0011", "企业备注ID不能为空"));
        }
        if (StringUtils.isBlank(cmd.getUserPin())) {
            throw new BusinessException(new DynamicErrorCode("S0011", "用户PIN不能为空"));
        }

        // 查询企业备注
        EnterpriseRemarkQuery query = new EnterpriseRemarkQuery();
        query.setEnterpriseRemarkId(cmd.getEnterpriseRemarkId());
        EnterpriseRemark enterpriseRemark = enterpriseRemarkRepository.queryEnterpriseRemark(query);
        if (Objects.isNull(enterpriseRemark)) {
            throw new BusinessException(new DynamicErrorCode("S0011", "企业备注不存在"));
        }

        // 校验企业备注是否属于当前登录用户的企业
        if (Objects.isNull(cmd.getEnterpriseId()) || !cmd.getEnterpriseId().equals(enterpriseRemark.getEnterpriseId())) {
            throw new BusinessException(new DynamicErrorCode("S0011", "无权删除该企业备注"));
        }

        // 设置更新信息
        enterpriseRemark.setUpdateUser(cmd.getOperator());
        enterpriseRemark.setUpdateTime(new java.util.Date());

        // 删除企业备注
        Integer result = enterpriseRemarkRepository.delete(enterpriseRemark);
        return result > 0;
    }
}
