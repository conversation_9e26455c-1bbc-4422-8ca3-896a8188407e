package com.jdh.o2oservice.b2b.application.enterprise;
import com.jdh.o2oservice.b2b.common.response.PageDto;
import com.jdh.o2oservice.b2b.export.enterprise.cmd.CreateB2bEnterpriseCmd;
import com.jdh.o2oservice.b2b.export.enterprise.cmd.UpdateB2bEnterpriseCmd;
import com.jdh.o2oservice.b2b.export.enterprise.cmd.UpdateB2bEnterpriseStatusCmd;
import com.jdh.o2oservice.b2b.export.enterprise.dto.B2bEnterpriseDto;
import com.jdh.o2oservice.b2b.export.enterprise.dto.B2bEnterpriseEscrowDto;
import com.jdh.o2oservice.b2b.export.enterprise.dto.B2bEnterpriseRelInfoDto;
import com.jdh.o2oservice.b2b.export.enterprise.query.B2bEnterpriseEscrowRequest;
import com.jdh.o2oservice.b2b.export.enterprise.query.B2bEnterprisePageRequest;
import com.jdh.o2oservice.b2b.export.enterprise.query.B2bEnterpriseRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

/**
 * @Description 企业信息服务
 * @Date 2025/2/24 下午3:07
 * <AUTHOR>
 **/
public interface B2bEnterpriseApplication {

    /**
     * 创建企业
     * @param cmd
     * @return
     */
    Boolean createEnterprise(CreateB2bEnterpriseCmd cmd);

    /**
     * 修改企业
     * @param cmd
     * @return
     */
    Boolean updateEnterprise(UpdateB2bEnterpriseCmd cmd);

    /**
     * 修改企业状态
     * @param cmd
     * @return
     */
    Boolean updateEnterpriseStatus(UpdateB2bEnterpriseStatusCmd cmd);

    /**
     * 查询企业
     * @param request
     * @return
     */
    B2bEnterpriseDto queryEnterprise(B2bEnterpriseRequest request);

    /**
     *
     * @param request
     * @return
     */
    B2bEnterpriseDto queryEnterpriseByPromiseId(B2bEnterpriseRequest request);
    /**
     * 分页查询企业
     * @param request
     * @return
     */
    PageDto<B2bEnterpriseDto> queryPageEnterprise(B2bEnterprisePageRequest request);

    /**
     * 查询企业关联信息
     * @param request
     * @return
     */
    B2bEnterpriseRelInfoDto queryEnterpriseRelInfo(B2bEnterpriseRequest request);

    /**
     * 企业代管
     * @param request
     * @return
     */
    B2bEnterpriseEscrowDto enterpriseEscrow(B2bEnterpriseEscrowRequest request);

    /**
     * 企业代管添加cookie
     * @param req
     * @param res
     */
    void enterpriseEscrowAddCookie(HttpServletRequest req, HttpServletResponse res) throws IOException;

    /**
     * 企业代管移除cookie
     * @param req
     * @param res
     */
    void enterpriseEscrowRemoveCookie(HttpServletRequest req, HttpServletResponse res) throws IOException;
}
