package com.jdh.o2oservice.b2b.application.enterprisevoucher;
import com.jdh.o2oservice.b2b.common.response.PageDto;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.model.EnterpriseVoucher;
import com.jdh.o2oservice.b2b.export.enterprisevoucher.cmd.*;
import com.jdh.o2oservice.b2b.export.enterprisevoucher.dto.*;
import com.jdh.o2oservice.b2b.export.enterprisevoucher.query.*;
import com.jdh.o2oservice.b2b.export.openapi.dto.AngelServiceRecordDTO;
import com.jdh.o2oservice.b2b.export.openapi.query.AngelServiceRecordRequest;

import java.util.List;

/**
 * B2bEnterpriseVoucherApplication
 *
 * <AUTHOR>
 * @date 2025/02/27
 */
public interface B2bEnterpriseVoucherApplication {

    /**
     * 提交
     *
     * @param cmd cmd
     * @return {@link Boolean }
     */
    Long submitPromise(SubmitPromiseCmd cmd);

    /**
     * 同步履约状态
     *
     * @param cmd cmd
     * @return {@link Boolean }
     */
    Boolean savePromiseStatus(SyncPromiseStatusCmd cmd);

    /**
     * 修改履约单
     *
     * @param request
     * @return {@link Boolean }
     */
    Boolean modifyEnterpriseVoucher(ModifyPromiseOrderPlatformRequest request);

    /**
     * 查询履约单详情
     * @param request
     * @return
     */
    CompletePromiseDetailsDto queryCompletePromiseDetails(PromiseDetailRequest request);

    /**
     * 查询可用时间
     * @param request
     * @return
     */
    List<PromiseAvailableTimeDto> queryAvailableTime(AvailableTimeRequest request);

    /**
     * 修改预约时间
     * @param cmd
     * @return
     */
    Boolean modifyAppointmentTime(ModifyPromiseTimeCmd cmd);

    /**
     * 作废服务单
     * @param cmd
     * @return
     */
    Boolean invalidVoucher(InvalidVoucherCmd cmd);

    /**
     * 查询录音
     * @param request
     * @return
     */
    List<PromiseCallRecordDto> queryCallRecordList(RecordingRequest request);

    /**
     * 查询录音链接
     * @param request
     * @return
     */
    String queryCallRecordUrl(RecordingRequest request);

    /**
     * 查询服务记录
     * @param request
     * @return
     */
    PromiseServiceRecordDto queryServiceRecord(ServiceRecordRequest request);

    /**
     * 分页查询履约单
     *
     * @param pageRequest pageRequest
     * @return {@link PageDto }<{@link CompletePromiseDetailsDto }>
     */
    PageDto<PromiseDetailsDto> queryPromiseDetailPage(PromiseDetailPageRequest pageRequest);

    /**
     * 导出履约单列表
     * @param pageRequest
     * @return
     */
    Boolean exportPromiseDetailPage(ExportPromiseDetailPageRequest pageRequest);

    /**
     * submitPromiseByUpload
     *
     * @param cmd cmd
     * @return {@link Boolean }
     */
    Boolean submitPromiseByUpload(SubmitPromiseByUploadCmd cmd);

    /**
     * 重新派单
     * @param cmd
     * @return
     */
    Boolean reDispatch(AfreshDispatchCmd cmd);

    /**
     * 查询服务者详情
     * @param request
     * @return
     */
   PromiseAngelDetailDto queryAngelDetail(AngelDetailRequest request);

    /**
     * 定向派单
     * @param cmd
     * @return
     */
    Boolean targetDispatch(AppointDispatchCmd cmd);

    /**
     * 查询派单明细
     * @param request
     * @return
     */
    PageDto<PromiseDispatchDetailDto> queryDispatchDetailList(DispatchDetailRequest request);


    /**
     * 智能文本解析接口
     * @param request
     * @return
     */
    TextParseAddressDto parseAddressText(TextParseAddressRequest request);

    /**
     * 查询意向护士
     * @param request
     * @return
     */
    IntendedAngelDto queryIntendedAngel(QueryIntendedAngelRequest request);

    /**
     * 同步履约单状态
     */
    Boolean syncPromiseStatus(SyncPromiseStatusRequest request);


    /**
     * 查询服务记录
     * @param request
     * @return
     */
    List<AngelServiceRecordDTO> queryAngelServiceRecordList(AngelServiceRecordRequest request);

}
