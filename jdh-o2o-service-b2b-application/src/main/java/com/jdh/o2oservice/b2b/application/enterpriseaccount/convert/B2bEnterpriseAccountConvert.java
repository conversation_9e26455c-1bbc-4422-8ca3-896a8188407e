package com.jdh.o2oservice.b2b.application.enterpriseaccount.convert;

import com.jdh.o2oservice.b2b.application.enterpriseaccount.enums.ContractStatusEnum;
import com.jdh.o2oservice.b2b.application.enterpriseaccount.enums.SettlementPeriodEnum;
import com.jdh.o2oservice.b2b.domain.enterprise.model.JdhB2bEnterpriseContract;
import com.jdh.o2oservice.b2b.domain.enterpriseaccount.model.JdhB2bEnterpriseAccountDetail;
import com.jdh.o2oservice.b2b.domain.enterpriseaccount.repository.model.JdhB2bEnterpriseAccount;
import com.jdh.o2oservice.b2b.domain.enterpriseaccount.repository.query.JdhB2bEnterpriseAccountDetailQuery;
import com.jdh.o2oservice.b2b.domain.enterprisecontract.bo.EnterpriseContractBo;
import com.jdh.o2oservice.b2b.export.enterprise.cmd.B2bEnterpriseContractCmd;
import com.jdh.o2oservice.b2b.export.enterprise.query.B2bEnterpriseRequest;
import com.jdh.o2oservice.b2b.export.enterpriseaccount.cmd.CreateB2bEnterpriseAccountDetailCmd;
import com.jdh.o2oservice.b2b.export.enterpriseaccount.dto.B2bEnterpriseAccountDto;
import com.jdh.o2oservice.b2b.export.enterpriseaccount.dto.B2bEnterpriseAccountInfoDto;
import com.jdh.o2oservice.b2b.export.enterpriseaccount.dto.B2bEnterpriseContractDetailDto;
import com.jdh.o2oservice.common.enums.MedicalPromiseFlagEnum;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;


/**
 * @ClassName:B2bEnterpriseAccountConvert
 * @Description:B2b企业账户Converter
 * @Author: liwenming
 * @Date: 2025/2/25 15:14
 * @Vserion: 1.0
 **/
@Mapper
public interface B2bEnterpriseAccountConvert {

    B2bEnterpriseAccountConvert ins = Mappers.getMapper(B2bEnterpriseAccountConvert.class);

    /**
     *
     * @param query
     * @return
     */
    JdhB2bEnterpriseAccountDetailQuery queryToContext(B2bEnterpriseRequest query);

    /**
     *
     * @param enterpriseContractBo
     * @return
     */
    @Mapping(target = "startTime", source = "startTime",dateFormat = "yyyy-MM-dd")
    @Mapping(target = "endTime", source = "endTime",dateFormat = "yyyy-MM-dd")
    @Mapping(source = "status",target = "statusDesc",qualifiedByName = "getStatusDesc")
    @Mapping(source = "settlementPeriod",target = "settlementPeriodDesc",qualifiedByName = "getSettlementPeriodDesc")
    B2bEnterpriseContractDetailDto convertToContractDetailDto(EnterpriseContractBo enterpriseContractBo);


    @Named("getStatusDesc")
    default String getStatusDesc(Integer status){
        return ContractStatusEnum.getEnumDescByType(status);
    }

    @Named("getSettlementPeriodDesc")
    default String getSettlementPeriodDesc(Integer settlementPeriod){
        return SettlementPeriodEnum.getEnumDescByType(settlementPeriod);
    }
    /**
     *
     * @param cmd
     * @return
     */
    @Mapping(target = "startTime", source = "startTime",dateFormat = "yyyy-MM-dd")
    @Mapping(target = "endTime", source = "endTime",dateFormat = "yyyy-MM-dd")
    @Mapping(target = "createUser", source = "operator")
    @Mapping(target = "updateUser", source = "operator")
    JdhB2bEnterpriseContract convertToJdhB2bEnterpriseContract(B2bEnterpriseContractCmd cmd);

    JdhB2bEnterpriseAccountDetail convertToEnterpriseAccountDetail(CreateB2bEnterpriseAccountDetailCmd cmd);

    B2bEnterpriseAccountInfoDto convertToEnterpriseAccountDto(JdhB2bEnterpriseAccount enterpriseAccount);
}
