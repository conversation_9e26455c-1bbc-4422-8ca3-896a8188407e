package com.jdh.o2oservice.b2b.application.support;

import com.jdh.o2oservice.b2b.common.response.PageDto;
import com.jdh.o2oservice.b2b.export.orderplatform.cmd.CreateOrderPlatformCmd;
import com.jdh.o2oservice.b2b.export.orderplatform.dto.OrderPlatformDto;
import com.jdh.o2oservice.b2b.export.orderplatform.query.OrderPlatformPageRequest;

/**
 * OrderPlatformApplication
 *
 * <AUTHOR>
 * @date 2025/02/25
 */
public interface OrderPlatformApplication {

    /**
     * 创建订单平台
     *
     * @param cmd cmd
     * @return {@link Boolean }
     */
    Boolean createOrderPlatform(CreateOrderPlatformCmd cmd);

    /**
     * 查询订单平台页面
     *
     * @param pageRequest 页面请求
     * @return {@link PageDto }<{@link OrderPlatformDto }>
     */
    PageDto<OrderPlatformDto> queryOrderPlatformPage(OrderPlatformPageRequest pageRequest);
}
