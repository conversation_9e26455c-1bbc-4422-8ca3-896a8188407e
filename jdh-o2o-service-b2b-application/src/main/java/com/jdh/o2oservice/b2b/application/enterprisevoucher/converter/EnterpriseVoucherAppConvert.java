package com.jdh.o2oservice.b2b.application.enterprisevoucher.converter;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.AngelServiceRecordBBo;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.FileUrlBo;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.TextParseAddressBO;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.IntendedAngelBo;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.man.*;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.ctx.SubmitPromiseCtx;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.ctx.QueryIntendedAngelCtx;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.enums.PromiseStatusEnum;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.model.*;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.param.AddressTextParseRpcParam;
import com.jdh.o2oservice.b2b.export.enterprisevoucher.cmd.*;
import com.jdh.o2oservice.b2b.export.enterprisevoucher.dto.*;
import com.jdh.o2oservice.b2b.export.enterprisevoucher.query.AvailableTimeRequest;
import com.jdh.o2oservice.b2b.export.enterprisevoucher.query.TextParseAddressRequest;
import com.jdh.o2oservice.b2b.export.enterprisevoucher.query.QueryIntendedAngelRequest;
import com.jdh.o2oservice.b2b.export.openapi.dto.AngelServiceRecordDTO;
import com.jdh.o2oservice.b2b.export.openapi.dto.EnterpriseVoucherActionLogDto;
import com.jdh.o2oservice.b2b.export.openapi.dto.FileUrlDto;
import com.jdh.o2oservice.b2b.export.openapi.query.GetFileUrlRequest;
import com.jdh.o2oservice.b2b.export.orderplatform.cmd.CreateOrderPlatformCmd;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Objects;

/**
 * EnterpriseVoucherAppConvert
 *
 * <AUTHOR>
 * @date 2025/02/27
 */
@Mapper
public interface EnterpriseVoucherAppConvert {

    /**
     * INS
     */
    EnterpriseVoucherAppConvert INS = Mappers.getMapper(EnterpriseVoucherAppConvert.class);

    @Mapping(target = "extend", ignore = true)
    CreateOrderPlatformCmd convertCreateOrderPlatformCmd(SubmitPromiseCmd cmd);

    /**
     * cmd2Ctx
     *
     * @param cmd cmd
     * @return {@link SubmitPromiseCtx }
     */
    default SubmitPromiseCtx cmd2Ctx(SubmitPromiseCmd cmd){
        if(Objects.isNull(cmd)){
            return null;
        }

        SubmitPromiseCtx ctx = SubmitPromiseCtx.builder().build();
        ctx.setEnterpriseId(cmd.getEnterpriseId());
        ctx.setEnterpriseSkuId(cmd.getEnterpriseSkuId());
        ctx.setSkuId(cmd.getSkuId());
        ctx.setSkuName(cmd.getSkuName());
        ctx.setSkuShortName(cmd.getSkuShortName());
        ctx.setEnterpriseUserId(cmd.getEnterpriseUserId());
        ctx.setEnterpriseUserName(cmd.getEnterpriseUserName());
        ctx.setAppointmentName(cmd.getAppointmentName());
        ctx.setAppointmentPhone(cmd.getAppointmentPhone());
        ctx.setErpPin(cmd.getUserPin());
        ctx.setSourceOrderId(cmd.getSourceOrderId());
        ctx.setSourceOrderPlatform(cmd.getSourceOrderPlatform());

        EnterpriseVoucherExtend extend = EnterpriseVoucherExtend.builder().build();
        SubmitPromiseExtend submitPromiseExtend = cmd.getExtend();
        extend.setAddress(cmd2PromiseAddress(submitPromiseExtend.getPromiseAddress()));
        extend.setPatientList(cmd2PromisePatientList(submitPromiseExtend.getPromisePatientList()));
        extend.setService(PromiseSku.builder()
                .enterpriseSkuId(cmd.getEnterpriseSkuId())
                .skuId(cmd.getSkuId())
                .skuName(cmd.getSkuName())
                .skuShortName(cmd.getSkuShortName())
                .build());
        extend.setAppointmentTime(cmd2PromiseAppointmentTime(submitPromiseExtend.getPromiseAppointmentTime()));
        extend.setIntendedNurse(cmd2PromiseIntendedNurse(submitPromiseExtend.getIntendedNurse()));
        extend.setRemark(cmd.getExtend().getRemark());

        ctx.setExtend(extend);
        return ctx;
    }


    PromiseAddress cmd2PromiseAddress(PromiseAddressInfo promiseAddress);

    PromisePatient cmd2PromisePatient(PromisePatientInfo promisePatient);

    List<PromisePatient> cmd2PromisePatientList(List<PromisePatientInfo> promisePatientList);

    PromiseIntendedNurse cmd2PromiseIntendedNurse(PromiseIntendedNurseInfo promiseIntendedNurse);

    PromiseAppointmentTime cmd2PromiseAppointmentTime(PromiseAppointmentTimeInfo promiseAppointmentTime);

    List<PromiseAvailableTimeDto> toAvailableTimeDto(List<AgencyAppointDateBo> appointDateBos);

    List<PromiseCallRecordDto> toRecordingDTO(List<CallRecordBo> callRecordBos);

    PromiseAngelDetailDto toAngelDetailDto(JdhAngelDetailBo angelDetailBo);

    List<PromiseDispatchDetailDto> toDispatchForManDtoList(List<JdhDispatchForManBo> dispatchForManBoList);

    List<PromiseDetailsDto> toPromiseDetailsList(List<EnterpriseVoucher> enterpriseVoucherList);

    @Mapping(target = "promiseStatusName", expression = "java(getPromiseStatusName(enterpriseVoucher.getPromiseStatus()))")
    PromiseDetailsDto toPromiseDetailsDto(EnterpriseVoucher enterpriseVoucher);

    default String getPromiseStatusName(Integer promiseStatus) {
        if(Objects.isNull(promiseStatus)){
            return "";
        }
        return PromiseStatusEnum.getEnumByStatus(promiseStatus).getDesc();
    }

    CompletePromiseDetailsDto toCompletePromiseDetailsDto(ViaCompletePromiseResBo viaBo);

    PromiseServiceRecordDto toPromiseServiceRecordDto(ManViaCompletePromiseTagBo tagInfo);

    AddressTextParseRpcParam request2RpcParam(TextParseAddressRequest request);

    /**
     *
     * @param addressBO
     * @return
     */
    TextParseAddressDto textParseBo2Dto(TextParseAddressBO addressBO);

    /**
     * 转换意向护士BO为DTO
     * @param bo 意向护士BO
     * @return 意向护士DTO
     */
    IntendedAngelDto toIntendedAngelDto(IntendedAngelBo bo);

    /**
     * 将请求对象转换为上下文对象
     * @param request 请求对象
     * @return 上下文对象
     */
    QueryIntendedAngelCtx request2QueryIntendedAngelCtx(QueryIntendedAngelRequest request);

    AgencyQueryDateBo convertAgencyQueryDateBo(AvailableTimeRequest request);

    GetFileUrlBo convertGetFileUrlBo(GetFileUrlRequest request);

    List<FileUrlDto> convertFileUrlDtoList(List<FileUrlBo> bos);

    List<AngelServiceRecordDTO> convert(List<AngelServiceRecordBBo> bos);
    AngelServiceRecordDTO convert(AngelServiceRecordBBo bo);

    @Mappings({
            @Mapping(target = "actionType", source = "actionName"),
            @Mapping(target = "actionTime", source = "createTime"),
            @Mapping(target = "actionTypeCode", source = "actionName"),
            @Mapping(target = "actionDesc", source = "content")
    })
    EnterpriseVoucherActionLogDto convertEnterpriseVoucherActionLogDto(PromiseTimelineDetailBo bo);

    List<EnterpriseVoucherActionLogDto> convertEnterpriseVoucherActionLogDtoList(List<PromiseTimelineDetailBo> boList);



    CompletePromiseDetailsBDto convert (CompletePromiseDetailsDto dto);

    CompletePromiseDetailsDto convert(CompletePromiseDetailsBDto bDto);

    @Mapping(source = "promiseDetailButtonDto",target = "promiseDetailModifyDto",qualifiedByName = "convertModify")
    PromiseDetailsBDto convert(PromiseDetailsDto dto);


    @Named("convertModify")
    default PromiseDetailModifyDto convertModify(PromiseDetailButtonDto buttonDto){
        if (Objects.isNull(buttonDto)) {
            return null;
        }
        PromiseDetailModifyDto modifyDto = new PromiseDetailModifyDto();
        modifyDto.setModifyAppointmentFlag(buttonDto.getModifyAppointmentFlag());
        modifyDto.setInvalidVoucherFlag(buttonDto.getInvalidVoucherFlag());
        return modifyDto;

    }

}
