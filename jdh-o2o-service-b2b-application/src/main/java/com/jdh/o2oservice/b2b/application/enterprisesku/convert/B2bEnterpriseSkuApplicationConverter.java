package com.jdh.o2oservice.b2b.application.enterprisesku.convert;
import com.jdh.o2oservice.b2b.domain.enterprisesku.model.JdhB2bEnterpriseSku;
import com.jdh.o2oservice.b2b.domain.enterprisesku.rpc.bo.JdhSkuResBo;
import com.jdh.o2oservice.b2b.export.enterprisesku.cmd.CreateB2bEnterpriseSkuCmd;
import com.jdh.o2oservice.b2b.export.enterprisesku.cmd.DeleteB2bEnterpriseSkuCmd;
import com.jdh.o2oservice.b2b.export.enterprisesku.cmd.UpdateB2bEnterpriseSkuCmd;
import com.jdh.o2oservice.b2b.export.enterprisesku.dto.B2bEnterpriseSkuDto;
import com.jdh.o2oservice.b2b.export.enterprisesku.dto.JdhSkuInfoDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Description 企业skuConverter
 * @Date 2025/2/24 下午9:29
 * <AUTHOR>
 **/
@Mapper
public interface B2bEnterpriseSkuApplicationConverter {

    B2bEnterpriseSkuApplicationConverter INS = Mappers.getMapper(B2bEnterpriseSkuApplicationConverter.class);

    JdhB2bEnterpriseSku cmd2Entity(CreateB2bEnterpriseSkuCmd cmd);

    JdhB2bEnterpriseSku cmd2Entity(UpdateB2bEnterpriseSkuCmd cmd);

    JdhB2bEnterpriseSku cmd2Entity(DeleteB2bEnterpriseSkuCmd cmd);

    B2bEnterpriseSkuDto entity2EnterpriseSkuDto(JdhB2bEnterpriseSku enterpriseSku);

    List<B2bEnterpriseSkuDto> entity2EnterpriseSkuDtoList(List<JdhB2bEnterpriseSku> enterpriseSkuList);

    JdhSkuInfoDto toJdhSkuInfoDto(JdhSkuResBo jdhSkuResBo);
}
