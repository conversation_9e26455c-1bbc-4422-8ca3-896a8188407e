package com.jdh.o2oservice.b2b.application.enterpriseuser.convert;
import com.jdh.o2oservice.b2b.domain.enterpriseuser.model.JdhB2bEnterpriseUser;
import com.jdh.o2oservice.b2b.export.enterpriseuser.cmd.CreateB2bEnterpriseUserCmd;
import com.jdh.o2oservice.b2b.export.enterpriseuser.cmd.DeleteB2bEnterpriseUserCmd;
import com.jdh.o2oservice.b2b.export.enterpriseuser.cmd.UpdateB2bEnterpriseUserCmd;
import com.jdh.o2oservice.b2b.export.enterpriseuser.cmd.UpdateB2bEnterpriseUserStatusCmd;
import com.jdh.o2oservice.b2b.export.enterpriseuser.dto.B2bEnterpriseUserDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;

@Mapper
public interface B2bEnterpriseUserApplicationConverter {

    B2bEnterpriseUserApplicationConverter INS = Mappers.getMapper(B2bEnterpriseUserApplicationConverter.class);

    JdhB2bEnterpriseUser cmd2Entity(CreateB2bEnterpriseUserCmd cmd);

    JdhB2bEnterpriseUser cmd2Entity(UpdateB2bEnterpriseUserCmd cmd);

    JdhB2bEnterpriseUser cmd2Entity(UpdateB2bEnterpriseUserStatusCmd cmd);

    JdhB2bEnterpriseUser cmd2Entity(DeleteB2bEnterpriseUserCmd cmd);

    B2bEnterpriseUserDto entity2EnterpriseUserDto(JdhB2bEnterpriseUser enterpriseUser);

    List<B2bEnterpriseUserDto> entity2EnterpriseUserDtoList(List<JdhB2bEnterpriseUser> enterpriseUserList);
}
