package com.jdh.o2oservice.b2b.application.support.converter;

import com.jdh.o2oservice.b2b.domain.support.orderplatform.context.CreateOrderPlatformCtx;
import com.jdh.o2oservice.b2b.domain.support.orderplatform.model.OrderPlatform;
import com.jdh.o2oservice.b2b.export.orderplatform.cmd.CreateOrderPlatformCmd;
import com.jdh.o2oservice.b2b.export.orderplatform.dto.OrderPlatformDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * OrderPlatformConverter
 *
 * <AUTHOR>
 * @date 2025/02/25
 */
@Mapper
public interface OrderPlatformConverter {

    /**
     * INS
     */
    OrderPlatformConverter INS = Mappers.getMapper(OrderPlatformConverter.class);

    /**
     * cmd2Ctx
     *
     * @param cmd cmd
     * @return {@link CreateOrderPlatformCtx }
     */
    CreateOrderPlatformCtx cmd2Ctx(CreateOrderPlatformCmd cmd);

    /**
     * entity2DtoList
     * @param entitys
     * @return
     */
    List<OrderPlatformDto> entity2DtoList(List<OrderPlatform> entitys);

    /**
     * 实体2 dto
     *
     * @param entity 实体
     * @return {@link OrderPlatformDto }
     */
    OrderPlatformDto entity2Dto(OrderPlatform entity);
}
