package com.jdh.o2oservice.b2b.application.enterprisesku;
import com.jdh.o2oservice.b2b.common.response.PageDto;
import com.jdh.o2oservice.b2b.export.enterprisesku.cmd.CreateB2bEnterpriseSkuCmd;
import com.jdh.o2oservice.b2b.export.enterprisesku.cmd.DeleteB2bEnterpriseSkuCmd;
import com.jdh.o2oservice.b2b.export.enterprisesku.cmd.UpdateB2bEnterpriseSkuCmd;
import com.jdh.o2oservice.b2b.export.enterprisesku.dto.B2bEnterpriseSkuDto;
import com.jdh.o2oservice.b2b.export.enterprisesku.dto.JdhSkuInfoDto;
import com.jdh.o2oservice.b2b.export.enterprisesku.query.B2bEnterpriseSkuListRequest;
import com.jdh.o2oservice.b2b.export.enterprisesku.query.B2bEnterpriseSkuPageRequest;
import com.jdh.o2oservice.b2b.export.enterprisesku.query.B2bEnterpriseSkuRequest;

import java.util.List;

/**
 * @Description 企业sku
 * @Date 2025/2/24 下午3:09
 * <AUTHOR>
 **/
public interface B2bEnterpriseSkuApplication {

    /**
     * 创建企业sku
     * @param cmd
     * @return
     */
    Boolean createEnterpriseSku(CreateB2bEnterpriseSkuCmd cmd);

    /**
     * 修改企业sku
     * @param cmd
     * @return
     */
    Boolean updateEnterpriseSku(UpdateB2bEnterpriseSkuCmd cmd);

    /**
     * 删除企业sku
     * @param cmd
     * @return
     */
    Boolean deleteEnterpriseSku(DeleteB2bEnterpriseSkuCmd cmd);

    /**
     * 查询企业sku
     * @param request
     * @return
     */
    B2bEnterpriseSkuDto queryEnterpriseSku(B2bEnterpriseSkuRequest request);

    /**
     * 分页查询企业sku
     * @param request
     * @return
     */
    PageDto<B2bEnterpriseSkuDto> queryPageEnterpriseSku(B2bEnterpriseSkuPageRequest request);

    /**
     * 查询sku
     * @param request
     * @return
     */
    JdhSkuInfoDto querySku(B2bEnterpriseSkuRequest request);

    /**
     * 查询企业sku列表
     * @param request 请求对象
     * @return 企业sku列表
     */
    List<B2bEnterpriseSkuDto> queryEnterpriseSkuList(B2bEnterpriseSkuListRequest request);

    /**
     * 查询企业sku
     * @param request
     * @return
     */
    List<B2bEnterpriseSkuDto> queryEnterpriseSkusByCondition(B2bEnterpriseSkuRequest request);
}
