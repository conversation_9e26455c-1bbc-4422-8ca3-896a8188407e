package com.jdh.o2oservice.b2b.application.enterpriseremark;

import com.jdh.o2oservice.b2b.common.response.PageDto;
import com.jdh.o2oservice.b2b.export.enterpriseremark.cmd.AddEnterpriseRemarkCmd;
import com.jdh.o2oservice.b2b.export.enterpriseremark.cmd.DeleteEnterpriseRemarkCmd;
import com.jdh.o2oservice.b2b.export.enterpriseremark.dto.EnterpriseRemarkDto;
import com.jdh.o2oservice.b2b.export.enterpriseremark.query.EnterpriseRemarkPageRequest;
import com.jdh.o2oservice.b2b.export.enterpriseremark.query.EnterpriseRemarkRequest;

import java.util.List;

/**
 * 企业备注应用层接口
 *
 * <AUTHOR>
 * @date 2025/03/10
 */
public interface B2bEnterpriseRemarkApplication {

    /**
     * 添加企业备注
     *
     * @param cmd 添加命令
     * @return 是否成功
     */
    Boolean addEnterpriseRemark(AddEnterpriseRemarkCmd cmd);

    /**
     * 查询企业备注
     *
     * @param request 请求对象
     * @return 企业备注
     */
    EnterpriseRemarkDto queryEnterpriseRemark(EnterpriseRemarkRequest request);

    /**
     * 分页查询企业备注
     *
     * @param request 分页请求对象
     * @return 分页结果
     */
    PageDto<EnterpriseRemarkDto> queryEnterpriseRemarkPage(EnterpriseRemarkPageRequest request);

    /**
     * 查询企业备注列表
     *
     * @param request 请求对象
     * @return 企业备注列表
     */
    List<EnterpriseRemarkDto> queryEnterpriseRemarkList(EnterpriseRemarkRequest request);

    /**
     * 删除企业备注
     *
     * @param cmd 删除命令
     * @return 是否成功
     */
    Boolean deleteEnterpriseRemark(DeleteEnterpriseRemarkCmd cmd);
}
