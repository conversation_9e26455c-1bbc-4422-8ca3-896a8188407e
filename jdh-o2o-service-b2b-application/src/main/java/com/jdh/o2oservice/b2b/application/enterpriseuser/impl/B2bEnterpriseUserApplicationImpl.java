package com.jdh.o2oservice.b2b.application.enterpriseuser.impl;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.b2b.application.enterpriseuser.B2bEnterpriseUserApplication;
import com.jdh.o2oservice.b2b.application.enterpriseuser.convert.B2bEnterpriseUserApplicationConverter;
import com.jdh.o2oservice.b2b.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.b2b.base.constant.NumConstant;
import com.jdh.o2oservice.b2b.base.exception.BusinessErrorCode;
import com.jdh.o2oservice.b2b.base.exception.BusinessException;
import com.jdh.o2oservice.b2b.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.b2b.common.response.PageDto;
import com.jdh.o2oservice.b2b.domain.enterpriseuser.model.JdhB2bEnterpriseUser;
import com.jdh.o2oservice.b2b.domain.enterpriseuser.model.JdhEnterpriseUserIdentifier;
import com.jdh.o2oservice.b2b.domain.enterpriseuser.repository.JdhB2bEnterpriseUserRepository;
import com.jdh.o2oservice.b2b.domain.enterpriseuser.repository.query.JdhB2bEnterpriseUserQuery;
import com.jdh.o2oservice.b2b.domain.support.common.bo.UserBaseInfoBo;
import com.jdh.o2oservice.b2b.domain.support.common.repository.UserInfoRpcService;
import com.jdh.o2oservice.b2b.export.enterpriseuser.cmd.CreateB2bEnterpriseUserCmd;
import com.jdh.o2oservice.b2b.export.enterpriseuser.cmd.DeleteB2bEnterpriseUserCmd;
import com.jdh.o2oservice.b2b.export.enterpriseuser.cmd.UpdateB2bEnterpriseUserCmd;
import com.jdh.o2oservice.b2b.export.enterpriseuser.cmd.UpdateB2bEnterpriseUserStatusCmd;
import com.jdh.o2oservice.b2b.export.enterpriseuser.dto.B2bEnterpriseUserDto;
import com.jdh.o2oservice.b2b.export.enterpriseuser.query.B2bEnterpriseUserPageRequest;
import com.jdh.o2oservice.b2b.export.enterpriseuser.query.B2bEnterpriseUserRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 企业用户
 */
@Service
@Slf4j
public class B2bEnterpriseUserApplicationImpl implements B2bEnterpriseUserApplication {

    @Resource
    private JdhB2bEnterpriseUserRepository jdhB2bEnterpriseUserRepository;

    @Resource
    private GenerateIdFactory generateIdFactory;

    @Resource
    private UserInfoRpcService userInfoRpcService;

    /**
     * 创建企业用户
     * @param cmd
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.application.enterpriseuser.impl.B2bEnterpriseUserApplicationImpl.createEnterpriseUser")
    public Boolean createEnterpriseUser(CreateB2bEnterpriseUserCmd cmd) {
        // 一个 pin 只能在一个企业下
        this.checkUserPinRepeat(cmd.getUserPin(), null);

        // 校验pin是否合法
        UserBaseInfoBo userPinInfo = userInfoRpcService.getUserBaseInfoByPin(cmd.getUserPin(), UserInfoRpcService.LOAD_TYPE_ONE);
        if (Objects.isNull(userPinInfo)){
            throw new BusinessException(BusinessErrorCode.PIN_ILLEGALITY);
        }

        JdhB2bEnterpriseUser enterpriseUser = B2bEnterpriseUserApplicationConverter.INS.cmd2Entity(cmd);
        enterpriseUser.setEnterpriseUserId(generateIdFactory.getId());
        enterpriseUser.setLastAvailableTime(new Date());
        log.info("B2bEnterpriseUserApplicationImpl createEnterpriseUser enterpriseUser={}", JSON.toJSONString(enterpriseUser));
        int count = jdhB2bEnterpriseUserRepository.save(enterpriseUser);
        if (count < 0){
            throw new BusinessException(BusinessErrorCode.ENTERPRISE_USER_SAVE_FAIL);
        }
        return Boolean.TRUE;
    }

    /**
     * 修改企业用户
     * @param cmd
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.application.enterpriseuser.impl.B2bEnterpriseUserApplicationImpl.updateEnterpriseUser")
    public Boolean updateEnterpriseUser(UpdateB2bEnterpriseUserCmd cmd) {
        JdhB2bEnterpriseUser dbEnterpriseUser = jdhB2bEnterpriseUserRepository.find(JdhEnterpriseUserIdentifier.builder().enterpriseUserId(cmd.getEnterpriseUserId()).build());
        if (Objects.isNull(dbEnterpriseUser)){
            throw new BusinessException(BusinessErrorCode.ENTERPRISE_USER_NO_EXIST);
        }

        // 一个 pin 只能在一个企业下
        this.checkUserPinRepeat(cmd.getUserPin(), cmd.getEnterpriseUserId());

        // 校验pin是否合法
        if (StringUtils.isNotBlank(cmd.getUserPin())){
            UserBaseInfoBo userPinInfo = userInfoRpcService.getUserBaseInfoByPin(cmd.getUserPin(), UserInfoRpcService.LOAD_TYPE_ONE);
            if (Objects.isNull(userPinInfo)){
                throw new BusinessException(BusinessErrorCode.PIN_ILLEGALITY);
            }
        }
        JdhB2bEnterpriseUser updateEnterpriseUser = B2bEnterpriseUserApplicationConverter.INS.cmd2Entity(cmd);
        log.info("B2bEnterpriseUserApplicationImpl updateEnterpriseUser updateEnterpriseUser={}", JSON.toJSONString(updateEnterpriseUser));
        int count = jdhB2bEnterpriseUserRepository.update(updateEnterpriseUser);
        if (count < 0){
            throw new BusinessException(BusinessErrorCode.ENTERPRISE_USER_UPDATE_FAIL);
        }
        return Boolean.TRUE;
    }

    /**
     * 通过userPin 查询
     *
     * @param userPin
     * @return
     */
    @Override
    public B2bEnterpriseUserDto findEnterpriseUserByPin(String userPin) {
        JdhB2bEnterpriseUser dbEnterpriseUser = jdhB2bEnterpriseUserRepository.findEnterpriseUserByPin(userPin);
        return B2bEnterpriseUserApplicationConverter.INS.entity2EnterpriseUserDto(dbEnterpriseUser);
    }

    private void checkUserPinRepeat(String userPin, Long enterpriseUserId) {
        List<JdhB2bEnterpriseUser> dbEnterpriseUserList = jdhB2bEnterpriseUserRepository.findList(JdhB2bEnterpriseUserQuery.builder().userPin(userPin).build());
        if (CollectionUtils.isNotEmpty(dbEnterpriseUserList)){
            dbEnterpriseUserList.forEach(enterpriseUser->{
                if (Objects.isNull(enterpriseUserId)){
                    if (userPin.equals(enterpriseUser.getUserPin())){
                        throw new BusinessException(BusinessErrorCode.ENTERPRISE_USER_EXIST);
                    }
                }else {
                    if (userPin.equals(enterpriseUser.getUserPin()) && !enterpriseUser.getEnterpriseUserId().equals(enterpriseUserId)){
                        throw new BusinessException(BusinessErrorCode.ENTERPRISE_USER_EXIST);
                    }
                }
            });
        }
    }

    /**
     * 修改企业用户状态
     * @param cmd
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.application.enterpriseuser.impl.B2bEnterpriseUserApplicationImpl.updateEnterpriseUserStatus")
    public Boolean updateEnterpriseUserStatus(UpdateB2bEnterpriseUserStatusCmd cmd) {
        JdhB2bEnterpriseUser dbEnterpriseUser = jdhB2bEnterpriseUserRepository.find(JdhEnterpriseUserIdentifier.builder().enterpriseUserId(cmd.getEnterpriseUserId()).build());
        if (Objects.isNull(dbEnterpriseUser)){
            throw new BusinessException(BusinessErrorCode.ENTERPRISE_USER_NO_EXIST);
        }
        JdhB2bEnterpriseUser updateEnterpriseUser = B2bEnterpriseUserApplicationConverter.INS.cmd2Entity(cmd);
        if (NumConstant.NUM_1.equals(cmd.getAvailable())){
            updateEnterpriseUser.setLastAvailableTime(new Date());
        }
        log.info("B2bEnterpriseUserApplicationImpl updateEnterpriseUserStatus updateEnterpriseUser={}", JSON.toJSONString(updateEnterpriseUser));
        int count = jdhB2bEnterpriseUserRepository.updateEnterpriseUserStatus(updateEnterpriseUser);
        if (count < 0){
            throw new BusinessException(BusinessErrorCode.ENTERPRISE_USER_UPDATE_FAIL);
        }
        return Boolean.TRUE;
    }

    /**
     * 删除企业用户
     * @param cmd
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.application.enterpriseuser.impl.B2bEnterpriseUserApplicationImpl.deleteEnterpriseUser")
    public Boolean deleteEnterpriseUser(DeleteB2bEnterpriseUserCmd cmd) {
        JdhB2bEnterpriseUser deleteEnterpriseUser = B2bEnterpriseUserApplicationConverter.INS.cmd2Entity(cmd);
        log.info("B2bEnterpriseUserApplicationImpl deleteEnterpriseUser deleteEnterpriseUser={}", JSON.toJSONString(deleteEnterpriseUser));
        int count = jdhB2bEnterpriseUserRepository.delete(deleteEnterpriseUser);
        if (count < 0){
            throw new BusinessException(BusinessErrorCode.ENTERPRISE_USER_DELETE_FAIL);
        }
        return Boolean.TRUE;
    }

    /**
     * 查询企业用户
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.application.enterpriseuser.impl.B2bEnterpriseUserApplicationImpl.queryEnterpriseUser")
    public B2bEnterpriseUserDto queryEnterpriseUser(B2bEnterpriseUserRequest request) {
        JdhB2bEnterpriseUser dbEnterpriseUser = jdhB2bEnterpriseUserRepository.find(JdhEnterpriseUserIdentifier.builder().enterpriseUserId(request.getEnterpriseUserId()).build());
        if (Objects.isNull(dbEnterpriseUser)){
            throw new BusinessException(BusinessErrorCode.ENTERPRISE_USER_NO_EXIST);
        }
        return B2bEnterpriseUserApplicationConverter.INS.entity2EnterpriseUserDto(dbEnterpriseUser);
    }

    /**
     * 分页查询企业用户
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.application.enterpriseuser.impl.B2bEnterpriseUserApplicationImpl.queryPageEnterpriseUser")
    public PageDto<B2bEnterpriseUserDto> queryPageEnterpriseUser(B2bEnterpriseUserPageRequest request) {
        JdhB2bEnterpriseUserQuery enterpriseUserQuery = JdhB2bEnterpriseUserQuery.builder()
                .enterpriseId(request.getEnterpriseId())
                .userName(request.getUserName())
                .userPin(request.getUserPin())
                .pageNum(request.getPageNum())
                .pageSize(request.getPageSize())
                .build();
        Page<JdhB2bEnterpriseUser> page = jdhB2bEnterpriseUserRepository.queryPageEnterpriseUser(enterpriseUserQuery);
        log.info("B2bEnterpriseUserApplicationImpl queryPageEnterpriseUser enterpriseUserQuery={}, page={}", JSON.toJSONString(enterpriseUserQuery), JSON.toJSONString(page));
        PageDto<B2bEnterpriseUserDto> result = new PageDto<>();
        result.setTotalPage(page.getPages());
        result.setPageNum(page.getCurrent());
        result.setPageSize(page.getSize());
        result.setTotalCount(page.getTotal());
        result.setList(B2bEnterpriseUserApplicationConverter.INS.entity2EnterpriseUserDtoList(page.getRecords()));
        return result;
    }
}
