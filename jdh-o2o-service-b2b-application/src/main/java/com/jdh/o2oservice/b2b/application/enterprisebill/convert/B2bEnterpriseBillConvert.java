package com.jdh.o2oservice.b2b.application.enterprisebill.convert;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.b2b.common.response.PageDto;
import com.jdh.o2oservice.b2b.domain.enterprisebill.context.B2bEnterpriseBillQueryContext;
import com.jdh.o2oservice.b2b.domain.enterprisebill.enums.EnterpriseBillStatusEnum;
import com.jdh.o2oservice.b2b.domain.enterprisebill.enums.PromiseBillStatusEnum;
import com.jdh.o2oservice.b2b.domain.enterprisebill.model.JdhB2bEnterpriseBill;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.model.EnterpriseVoucher;
import com.jdh.o2oservice.b2b.export.enterprisebill.cmd.B2bEnterpriseBillCmd;
import com.jdh.o2oservice.b2b.export.enterprisebill.dto.B2bEnterpriseBillDetailDto;
import com.jdh.o2oservice.b2b.export.enterprisebill.dto.B2bEnterpriseBillDto;
import com.jdh.o2oservice.b2b.export.enterprisebill.query.B2bEnterpriseBillRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;


/**
 * @ClassName:B2bEnterpriseBillConvert
 * @Description:B2b企业账单Converter
 * @Author: liwenming
 * @Date: 2025/2/25 15:14
 * @Vserion: 1.0
 **/
@Mapper
public interface B2bEnterpriseBillConvert {

    B2bEnterpriseBillConvert ins = Mappers.getMapper(B2bEnterpriseBillConvert.class);

    /**
     *
     * @param query
     * @return
     */
    B2bEnterpriseBillQueryContext queryToContext(B2bEnterpriseBillRequest query);

    @Mapping(target = "totalPage", source = "pages")
    @Mapping(target = "pageNum", source = "current")
    @Mapping(target = "pageSize", source = "size")
    @Mapping(target = "totalCount", source = "total")
    @Mapping(target = "list", source = "records")
    PageDto<B2bEnterpriseBillDto> entity2B2bEnterpriseBillDtoPage(Page<JdhB2bEnterpriseBill> jdhB2bEnterpriseBillPage);

    /**
     *
     * @param settlement
     * @return
     */
    @Mapping(target = "billStatusDesc", source = "billStatus", qualifiedByName = "billStatusConvert")
    B2bEnterpriseBillDto entity2B2bEnterpriseBillDto(JdhB2bEnterpriseBill settlement);

    /**
     *
     * @param billStatus
     * @return
     */
    @Named("billStatusConvert")
    default String billStatusConvert(Integer billStatus) {
        return EnterpriseBillStatusEnum.getBillStatusDescByStatus(billStatus);
    }

    /**
     *
     * @param b2bEnterpriseBillCmd
     * @return
     */
    @Mapping(target = "updateUser", source = "userPin")
    JdhB2bEnterpriseBill cmdToJdhB2bEnterpriseBill(B2bEnterpriseBillCmd b2bEnterpriseBillCmd);

    /**
     *
     * @param enterpriseVoucher
     * @return
     */
    B2bEnterpriseBillDetailDto queryToB2bEnterpriseBillDetailDto(EnterpriseVoucher enterpriseVoucher);


    /**
     *
     * @param promiseBillStatus
     * @return
     */
    default String promiseBillStatusConvert(Integer promiseBillStatus) {
        return PromiseBillStatusEnum.getPromiseBillStatusDescByStatus(promiseBillStatus);
    }

}
