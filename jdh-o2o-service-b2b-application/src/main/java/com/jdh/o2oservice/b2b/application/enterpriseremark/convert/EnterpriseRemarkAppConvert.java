package com.jdh.o2oservice.b2b.application.enterpriseremark.convert;

import com.jdh.o2oservice.b2b.domain.enterpriseremark.cmd.EnterpriseRemarkCmd;
import com.jdh.o2oservice.b2b.domain.enterpriseremark.model.EnterpriseRemark;
import com.jdh.o2oservice.b2b.domain.enterpriseremark.query.EnterpriseRemarkPageQuery;
import com.jdh.o2oservice.b2b.domain.enterpriseremark.query.EnterpriseRemarkQuery;
import com.jdh.o2oservice.b2b.export.enterpriseremark.cmd.AddEnterpriseRemarkCmd;
import com.jdh.o2oservice.b2b.export.enterpriseremark.dto.EnterpriseRemarkDto;
import com.jdh.o2oservice.b2b.export.enterpriseremark.query.EnterpriseRemarkPageRequest;
import com.jdh.o2oservice.b2b.export.enterpriseremark.query.EnterpriseRemarkRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 企业备注应用层转换器
 *
 * <AUTHOR>
 * @date 2025/03/10
 */
@Mapper
public interface EnterpriseRemarkAppConvert {

    /**
     * 实例
     */
    EnterpriseRemarkAppConvert INS = Mappers.getMapper(EnterpriseRemarkAppConvert.class);

    /**
     * 领域模型转DTO
     *
     * @param enterpriseRemark 领域模型
     * @return DTO
     */
    EnterpriseRemarkDto toEnterpriseRemarkDto(EnterpriseRemark enterpriseRemark);

    /**
     * 领域模型列表转DTO列表
     *
     * @param enterpriseRemarks 领域模型列表
     * @return DTO列表
     */
    List<EnterpriseRemarkDto> toEnterpriseRemarkDtoList(List<EnterpriseRemark> enterpriseRemarks);

    /**
     * 请求对象转查询对象
     *
     * @param request 请求对象
     * @return 查询对象
     */
    EnterpriseRemarkQuery toEnterpriseRemarkQuery(EnterpriseRemarkRequest request);

    /**
     * 分页请求对象转分页查询对象
     *
     * @param request 分页请求对象
     * @return 分页查询对象
     */
    EnterpriseRemarkPageQuery toEnterpriseRemarkPageQuery(EnterpriseRemarkPageRequest request);

    /**
     * 添加命令对象转领域命令对象
     *
     * @param cmd 添加命令对象
     * @return 领域命令对象
     */
    @Mapping(target = "enterpriseRemarkId", ignore = true)
    EnterpriseRemarkCmd toEnterpriseRemarkCmd(AddEnterpriseRemarkCmd cmd);
}
