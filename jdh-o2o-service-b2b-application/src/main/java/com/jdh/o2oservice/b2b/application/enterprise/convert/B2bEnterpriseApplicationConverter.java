package com.jdh.o2oservice.b2b.application.enterprise.convert;
import com.jdh.o2oservice.b2b.domain.enterprise.model.JdhB2bEnterprise;
import com.jdh.o2oservice.b2b.domain.enterprise.model.JdhB2bEnterpriseContract;
import com.jdh.o2oservice.b2b.domain.enterprisesku.model.JdhB2bEnterpriseSku;
import com.jdh.o2oservice.b2b.domain.enterpriseuser.model.JdhB2bEnterpriseUser;
import com.jdh.o2oservice.b2b.export.enterprise.cmd.CreateB2bEnterpriseCmd;
import com.jdh.o2oservice.b2b.export.enterprise.cmd.UpdateB2bEnterpriseCmd;
import com.jdh.o2oservice.b2b.export.enterprise.cmd.UpdateB2bEnterpriseStatusCmd;
import com.jdh.o2oservice.b2b.export.enterprise.dto.B2bEnterpriseDto;
import com.jdh.o2oservice.b2b.export.enterprise.dto.B2bEnterpriseRelSkuDto;
import com.jdh.o2oservice.b2b.export.enterprise.dto.B2bEnterpriseRelUserDto;
import com.jdh.o2oservice.b2b.export.enterpriseaccount.dto.B2bEnterpriseAcountContractDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Description 企业Converter
 * @Date 2025/2/24 下午9:28
 * <AUTHOR>
 **/
@Mapper
public interface B2bEnterpriseApplicationConverter {

    B2bEnterpriseApplicationConverter INS = Mappers.getMapper(B2bEnterpriseApplicationConverter.class);

    JdhB2bEnterprise cmd2Entity(CreateB2bEnterpriseCmd cmd);

    JdhB2bEnterprise cmd2Entity(UpdateB2bEnterpriseCmd cmd);

    JdhB2bEnterprise cmd2Entity(UpdateB2bEnterpriseStatusCmd cmd);

    B2bEnterpriseDto entity2EnterpriseDto(JdhB2bEnterprise enterprise);

    List<B2bEnterpriseDto> entity2EnterpriseDtoList(List<JdhB2bEnterprise> enterpriseList);

    List<B2bEnterpriseRelSkuDto> entity2EnterpriseRelSkuDto(List<JdhB2bEnterpriseSku> b2bEnterpriseSkuList);

    List<B2bEnterpriseRelUserDto> entity2EnterpriseRelUserDto(List<JdhB2bEnterpriseUser> enterpriseUserList);

    B2bEnterpriseAcountContractDto entity2EnterpriseContractDto(JdhB2bEnterpriseContract enterpris);

}
