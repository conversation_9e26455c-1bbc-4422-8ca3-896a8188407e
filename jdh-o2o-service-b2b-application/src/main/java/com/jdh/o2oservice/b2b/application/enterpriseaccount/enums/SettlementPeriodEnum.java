package com.jdh.o2oservice.b2b.application.enterpriseaccount.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 结算周期
 * @Author: lwm
 * @Date: 2025/5/13 3:33 下午
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum SettlementPeriodEnum {
    /**
     * 1-日 2-月 3-季度 4-年 5-周 6-半月 7-其他时间
     */
    DAY(1, "日"),
    MONTH(2, "月"),
    QUARTER(3, "季度"),
    YEAR(4, "年"),
    WEEK(5, "周"),
    HALF_MONTH(6, "半月"),
    OTHER(7, "其他时间"),
    ;

    /**
     *
     */
    private Integer type;
    /**
     *
     */
    private String desc;

    /**
     *
     * @param type
     * @return
     */
    public static String getEnumDescByType(Integer type) {
        if (Objects.isNull(type)) {
            return "";
        }
        for (SettlementPeriodEnum statusEnum : SettlementPeriodEnum.values()) {
            if (Objects.equals(statusEnum.getType(), type)) {
                return statusEnum.getDesc();
            }
        }
        return "";
    }

}
