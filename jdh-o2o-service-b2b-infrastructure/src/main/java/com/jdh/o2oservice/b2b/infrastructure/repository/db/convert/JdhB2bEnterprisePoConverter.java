package com.jdh.o2oservice.b2b.infrastructure.repository.db.convert;

import com.jdh.o2oservice.b2b.domain.enterprise.model.JdhB2bEnterprise;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.po.JdhB2bEnterprisePo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
 * @Description JdhB2bEnterprisePoConverter
 * @Date 2025/2/25 上午11:06
 * <AUTHOR>
 **/
@Mapper
public interface JdhB2bEnterprisePoConverter {

    JdhB2bEnterprisePoConverter INSTANCE = Mappers.getMapper(JdhB2bEnterprisePoConverter.class);

    JdhB2bEnterprise po2Entity(JdhB2bEnterprisePo jdhB2bEnterprisePo);

    List<JdhB2bEnterprise> po2Entity(List<JdhB2bEnterprisePo> list);
}
