package com.jdh.o2oservice.b2b.infrastructure.repository.db.convert;
import com.jdh.o2oservice.b2b.domain.support.operationlog.mode.JdhB2bOperationLog;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.po.JdhB2bOperationLogPo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface JdhB2bOperationLogPoConverter {

    JdhB2bOperationLogPoConverter INSTANCE = Mappers.getMapper(JdhB2bOperationLogPoConverter.class);


    List<JdhB2bOperationLog> po2Entity(List<JdhB2bOperationLogPo> list);

    JdhB2bOperationLog po2Entity(JdhB2bOperationLogPo po);
}
