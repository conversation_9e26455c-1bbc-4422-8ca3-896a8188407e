package com.jdh.o2oservice.b2b.infrastructure.repository.db.mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.po.JdhB2bEnterpriseAccountDetailPo;
import org.apache.ibatis.annotations.Mapper;

/**
 * @ClassName:JdhB2bEnterpriseAccountDetailPo
 * @Description:B2b企业账户明细
 * @Author: liwenming
 * @Date: 2025/2/24 15:14
 * @Vserion: 1.0
 **/
@Mapper
public interface JdhB2bEnterpriseAccountDetailMapper extends BaseMapper<JdhB2bEnterpriseAccountDetailPo> {

}