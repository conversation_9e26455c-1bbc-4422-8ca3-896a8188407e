package com.jdh.o2oservice.b2b.infrastructure.rpc;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jd.addresstranslation.api.address.JDAddressDistrictService;
import com.jd.addresstranslation.api.base.BaseResponse;
import com.jd.addresstranslation.api.base.JDDistrictInfo;
import com.jd.lbs.jdlbsapi.c2c.TextParsingService;
import com.jd.lbs.jdlbsapi.dto.c2c.TextParseReqDto;
import com.jd.lbs.jdlbsapi.dto.c2c.TextParseResult;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdh.o2oservice.b2b.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.b2b.base.constant.CommonConstant;
import com.jdh.o2oservice.b2b.base.exception.BusinessErrorCode;
import com.jdh.o2oservice.b2b.base.exception.BusinessException;
import com.jdh.o2oservice.b2b.base.exception.SystemErrorCode;
import com.jdh.o2oservice.b2b.base.exception.SystemException;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.TextParseAddressBO;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.param.AddressTextParseRpcParam;
import com.jdh.o2oservice.b2b.domain.support.address.AddressRpc;
import com.jdh.o2oservice.b2b.domain.support.address.bo.JDDistrictBo;
import com.jdh.o2oservice.b2b.infrastructure.rpc.converter.JDDistrictConverter;
import com.jdh.o2oservice.b2b.infrastructure.rpc.converter.JdhAddressRpcConverter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 地址转换
 *
 * <AUTHOR>
 * @date 2019-12-09 15:26
 */
@Service("addressRpc")
public class AddressRpcImpl implements AddressRpc {
    
    /**
     * log
     */
    private static final Logger log = LoggerFactory.getLogger(AddressRpcImpl.class);
    
    /**
     * SUCCESS
     */
    private static final int SUCCESS = 200;

    /**
     * SUCCESS_CONFLICT
     * 成功解析地址，但解析的地址与用户输入的地址有出入
     */
    private static final int SUCCESS_CONFLICT = 504;

    /**
     * 没有下级
     */
    private static final int NONE_CHILDREN = 503;
    
    /**
     * 接口文档：https://cf.jd.com/pages/viewpage.action?pageId=148161039
     * jdAddressDistrictService
     */
    @Resource
    private JDAddressDistrictService jdAddressDistrictService;

    /**
     * 智能文本解析
     */
    @Resource
    private TextParsingService textParsingService;

    /**
     * 获取京标的省
     *
     * @return
     */
    @Override
    @LogAndAlarm
    public List<JDDistrictBo> getProvinces() {
        try{
            BaseResponse<List<JDDistrictInfo>> result = jdAddressDistrictService.getProvinces("health_examination_jdaddressdistrict");
            log.info("AddressRpcImpl -> getProvinces end, result={}", JSON.toJSONString(result));
            if (result == null || SUCCESS != result.getStatus()) {
                throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
            }

            return JDDistrictConverter.INSTANCE.dtoListToBoList(result.getResult());
        }catch (Throwable e){
            Profiler.businessAlarm("com.jd.health.medical.examination.rpc.impl.AddressRpcImpl.getProvinces", e.getMessage());
            log.error("AddressRpcImpl -> getProvinces exception", e);
            throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
        }
    }

    /**
     * 获取京标子级
     *
     * @param jdParentCode
     * @return
     */
    @Override
    //@LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.AddressRpcImpl.getChildren")
    public List<JDDistrictBo> getChildren(Integer jdParentCode) {
        //log.info("AddressRpcImpl -> getChildren start, jdParentCode={}", jdParentCode);
        try {
            BaseResponse<List<JDDistrictInfo>> result = jdAddressDistrictService.getChildren("health_examination_jdaddressdistrict", jdParentCode);
            //log.info("AddressRpcImpl -> getChildren end, jdParentCode={}, result={}", jdParentCode, JSON.toJSONString(result));
            if (result != null && NONE_CHILDREN == result.getStatus()) {
                return Lists.newArrayList();
            }
            if (result == null || SUCCESS != result.getStatus()) {
                throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
            }
            return JDDistrictConverter.INSTANCE.dtoListToBoList(result.getResult());
        } catch (Throwable e) {
            Profiler.businessAlarm("com.jd.health.medical.examination.rpc.impl.AddressRpcImpl.getChildren", e.getMessage());
            log.error("AddressRpcImpl -> getChildren exception, jdParentCode={}", jdParentCode, e);
            throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
        }
    }

    /**
     * 智能文本解析
     * https://lbsapi.jd.com/iframe.html?childURL=docid=2-75&childNav=1-17
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm
    public TextParseAddressBO parseText(AddressTextParseRpcParam param) {
        try{
            TextParseReqDto textParseReqDto = JdhAddressRpcConverter.instance.convertToTextParseReqDto(param);
            log.info("AddressRpcImpl -> parseText start, textParseReqDto={}",JSON.toJSONString(textParseReqDto));
            com.jd.lbs.jdlbsapi.dto.BaseResponse<TextParseResult> result = textParsingService.parseText(CommonConstant.APP_KEY_FOR_ADDRESS_TEXT_PARSE,textParseReqDto);
            log.info("AddressRpcImpl -> parseText end, result={}", JSON.toJSONString(result));
            ArrayList<Integer> successStatusList = Lists.newArrayList(SUCCESS_CONFLICT, SUCCESS);
            if (result == null || result.getResult() == null || !successStatusList.contains(result.getStatus())
                    || result.getResult().getAddressInfo() == null){
                throw new BusinessException(BusinessErrorCode.USER_ADDRESS_TEXT_PARSE_ERROR);
            }
            return JdhAddressRpcConverter.instance.convertToTextParseAddressBO(result.getResult());
        }catch (BusinessException e){
            log.error("AddressRpcImpl -> parseText business exception, param={}", JSON.toJSONString(param), e);
            throw e;
        }catch (Throwable e){
            log.error("AddressRpcImpl -> parseText Throwable, param={}", JSON.toJSONString(param), e);
            throw new BusinessException(BusinessErrorCode.USER_ADDRESS_TEXT_PARSE_ERROR);
        }
    }
}
