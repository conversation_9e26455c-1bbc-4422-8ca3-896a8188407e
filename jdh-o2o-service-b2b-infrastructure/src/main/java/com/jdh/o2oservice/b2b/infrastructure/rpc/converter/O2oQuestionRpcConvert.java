package com.jdh.o2oservice.b2b.infrastructure.rpc.converter;

import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.O2oQuestionBo;
import com.jdh.o2oservice.export.product.dto.QuestionDTO;
import com.jdh.o2oservice.export.product.dto.careform.QuestionDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/9/22
 */
@Mapper
public interface O2oQuestionRpcConvert {


    O2oQuestionRpcConvert INSTANCE = Mappers.getMapper(O2oQuestionRpcConvert.class);


    List<O2oQuestionBo> convert(List<QuestionDTO> questionDtos);


}
