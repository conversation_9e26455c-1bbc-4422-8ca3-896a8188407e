package com.jdh.o2oservice.b2b.infrastructure.repository.db;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.b2b.domain.enterpriseremark.model.EnterpriseRemark;
import com.jdh.o2oservice.b2b.domain.enterpriseremark.query.EnterpriseRemarkPageQuery;
import com.jdh.o2oservice.b2b.domain.enterpriseremark.query.EnterpriseRemarkQuery;
import com.jdh.o2oservice.b2b.domain.enterpriseremark.repository.EnterpriseRemarkRepository;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.convert.JdhB2bEnterpriseRemarkPoConverter;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.mapper.JdhB2bEnterpriseRemarkMapper;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.po.JdhB2bEnterpriseRemarkPo;
import com.jdh.o2oservice.b2b.base.enums.YnStatusEnum;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 企业备注仓储实现
 *
 * <AUTHOR>
 * @date 2025/03/10
 */
@Repository
public class EnterpriseRemarkRepositoryImpl implements EnterpriseRemarkRepository {

    /**
     * 企业备注Mapper
     */
    @Resource
    private JdhB2bEnterpriseRemarkMapper jdhB2bEnterpriseRemarkMapper;

    /**
     * 保存企业备注
     *
     * @param enterpriseRemark 企业备注
     * @return 影响行数
     */
    @Override
    public Integer save(EnterpriseRemark enterpriseRemark) {
        JdhB2bEnterpriseRemarkPo po = JdhB2bEnterpriseRemarkPoConverter.INSTANCE.entity2Po(enterpriseRemark);
        if (Objects.nonNull(po.getId())) {
            // 更新
            LambdaUpdateWrapper<JdhB2bEnterpriseRemarkPo> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.eq(JdhB2bEnterpriseRemarkPo::getEnterpriseRemarkId, po.getEnterpriseRemarkId());
            return jdhB2bEnterpriseRemarkMapper.update(po, updateWrapper);
        } else {
            // 插入
            return jdhB2bEnterpriseRemarkMapper.insert(po);
        }
    }

    /**
     * 查询企业备注
     *
     * @param query 查询条件
     * @return 企业备注
     */
    @Override
    public EnterpriseRemark queryEnterpriseRemark(EnterpriseRemarkQuery query) {
        LambdaQueryWrapper<JdhB2bEnterpriseRemarkPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Objects.nonNull(query.getEnterpriseRemarkId()),
                JdhB2bEnterpriseRemarkPo::getEnterpriseRemarkId, query.getEnterpriseRemarkId());
        queryWrapper.eq(Objects.nonNull(query.getEnterpriseId()),
                JdhB2bEnterpriseRemarkPo::getEnterpriseId, query.getEnterpriseId());

        if (CollectionUtils.isEmpty(queryWrapper.getExpression().getNormal())) {
            return null;
        }

        queryWrapper.eq(JdhB2bEnterpriseRemarkPo::getYn, YnStatusEnum.YES.getCode());
        JdhB2bEnterpriseRemarkPo remarkPo = jdhB2bEnterpriseRemarkMapper.selectOne(queryWrapper);
        return JdhB2bEnterpriseRemarkPoConverter.INSTANCE.po2Entity(remarkPo);
    }

    /**
     * 分页查询企业备注
     *
     * @param query 查询条件
     * @return 分页结果
     */
    @Override
    public Page<EnterpriseRemark> queryEnterpriseRemarkPage(EnterpriseRemarkPageQuery query) {
        Page<JdhB2bEnterpriseRemarkPo> page = new Page<>(query.getPageNum(), query.getPageSize());
        LambdaQueryWrapper<JdhB2bEnterpriseRemarkPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Objects.nonNull(query.getEnterpriseId()),
                JdhB2bEnterpriseRemarkPo::getEnterpriseId, query.getEnterpriseId());
        queryWrapper.eq(JdhB2bEnterpriseRemarkPo::getYn, YnStatusEnum.YES.getCode());
        queryWrapper.orderByDesc(JdhB2bEnterpriseRemarkPo::getCreateTime);

        Page<JdhB2bEnterpriseRemarkPo> poPage = jdhB2bEnterpriseRemarkMapper.selectPage(page, queryWrapper);
        Page<EnterpriseRemark> resultPage = new Page<>();
        resultPage.setCurrent(poPage.getCurrent());
        resultPage.setSize(poPage.getSize());
        resultPage.setTotal(poPage.getTotal());
        resultPage.setPages(poPage.getPages());
        resultPage.setRecords(JdhB2bEnterpriseRemarkPoConverter.INSTANCE.po2EntityList(poPage.getRecords()));
        return resultPage;
    }

    /**
     * 查询企业备注列表
     *
     * @param query 查询条件
     * @return 企业备注列表
     */
    @Override
    public List<EnterpriseRemark> queryEnterpriseRemarkList(EnterpriseRemarkQuery query) {
        LambdaQueryWrapper<JdhB2bEnterpriseRemarkPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Objects.nonNull(query.getEnterpriseId()),
                JdhB2bEnterpriseRemarkPo::getEnterpriseId, query.getEnterpriseId());
        queryWrapper.eq(JdhB2bEnterpriseRemarkPo::getYn, YnStatusEnum.YES.getCode());
        queryWrapper.orderByDesc(JdhB2bEnterpriseRemarkPo::getCreateTime);

        List<JdhB2bEnterpriseRemarkPo> poList = jdhB2bEnterpriseRemarkMapper.selectList(queryWrapper);
        return JdhB2bEnterpriseRemarkPoConverter.INSTANCE.po2EntityList(poList);
    }

    /**
     * 删除企业备注
     *
     * @param enterpriseRemark 企业备注
     * @return 影响行数
     */
    @Override
    public Integer delete(EnterpriseRemark enterpriseRemark) {
        LambdaUpdateWrapper<JdhB2bEnterpriseRemarkPo> updateWrapper = Wrappers.lambdaUpdate();
        JdhB2bEnterpriseRemarkPo po = new JdhB2bEnterpriseRemarkPo();
        updateWrapper.set(JdhB2bEnterpriseRemarkPo::getYn, YnStatusEnum.NO.getCode())
                .eq(JdhB2bEnterpriseRemarkPo::getEnterpriseRemarkId, enterpriseRemark.getEnterpriseRemarkId());
        po.setUpdateUser(enterpriseRemark.getUpdateUser());
        po.setUpdateTime(enterpriseRemark.getUpdateTime());
        return jdhB2bEnterpriseRemarkMapper.update(po, updateWrapper);
    }
}
