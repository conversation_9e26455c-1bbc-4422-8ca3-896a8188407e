package com.jdh.o2oservice.b2b.infrastructure.rpc;
import com.alibaba.fastjson.JSON;
import com.jd.uim2.facade.jsf.Uim2DimPermissionFacade;
import com.jd.uim2.facade.jsf.Uim2RoleFacade;
import com.jd.uim2.facade.request.dim.Uim2DimResCodeRequest;
import com.jd.uim2.facade.request.user.Uim2ErpRequest;
import com.jd.uim2.facade.response.Uim2Response;
import com.jd.uim2.facade.response.res.Uim2DimResourceDto;
import com.jd.uim2.facade.response.role.Uim2RoleDto;
import com.jdh.o2oservice.b2b.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.b2b.base.exception.BusinessException;
import com.jdh.o2oservice.b2b.base.exception.SystemErrorCode;
import com.jdh.o2oservice.b2b.common.config.UimB2bConfig;
import com.jdh.o2oservice.b2b.domain.support.user.uim.bo.Uim2DimResourceBO;
import com.jdh.o2oservice.b2b.domain.support.user.uim.bo.Uim2RoleBO;
import com.jdh.o2oservice.b2b.domain.support.user.uim.rpc.RoleRpc;
import com.jdh.o2oservice.b2b.infrastructure.rpc.converter.RoleRpcConvert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * uim系统角色接口
 *
 * <AUTHOR>
 * @date 2023-09-21 21:24
 */
@Slf4j
@Service
public class RoleRpcImpl implements RoleRpc {

    /**
     * uim配置信息
     */
    @Resource
    UimB2bConfig uimConfig;

    /**
     * 角色接口
     */
    @Resource
    Uim2RoleFacade uim2RoleFacade;

    /**
     * uim2DimPermissionFacade
     */
    @Resource
    private Uim2DimPermissionFacade uim2DimPermissionFacade;

    /**
     * 成功
     */
    private static final String SUCCESS = "SUCCESS";

    /**
     * 查询用户拥有的全部角色
     *
     * @param erp req
     * @return list
     */
    @Override
    @LogAndAlarm
    public List<Uim2RoleBO> getRole(String erp) {
        try {
            Uim2ErpRequest uim2ErpRequest = new Uim2ErpRequest();
            uim2ErpRequest.setAppKey(uimConfig.getAppKey());
            uim2ErpRequest.setAppToken(uimConfig.getAppToken());
            uim2ErpRequest.setTenantCode(uimConfig.getTenantCode());
            uim2ErpRequest.setLang(uimConfig.getLang());
            uim2ErpRequest.setErp(erp);
            Uim2Response<List<Uim2RoleDto>> uim2Response = uim2RoleFacade.getRole(uim2ErpRequest);
            log.info("RoleRpcImpl -> getRole  end,uim2Response:{}", JSON.toJSONString(uim2Response));
            if (SUCCESS.equals(uim2Response.getCode())) {
                return RoleRpcConvert.ins.toUim2RoleBOList(uim2Response.getData());
            }
            return Collections.emptyList();
        } catch (Exception e) {
            log.error("RoleRpcImpl -> getRole Exception", e);
            throw new BusinessException(SystemErrorCode.UNKNOWN_ERROR);
        }
    }

    /**
     * getDimResource
     *
     * @param erp ERP
     * @return {@link List}<{@link Uim2DimResourceBO}>
     */
    @Override
    @LogAndAlarm
    public List<Uim2DimResourceBO> getDimResource(String erp) {
        try {
            Uim2DimResCodeRequest uim2DimResCodeRequest = new Uim2DimResCodeRequest();
            uim2DimResCodeRequest.setErp(erp);
            uim2DimResCodeRequest.setAppKey(uimConfig.getAppKey());
            uim2DimResCodeRequest.setAppToken(uimConfig.getAppToken());
            uim2DimResCodeRequest.setTenantCode(uimConfig.getTenantCode());
            uim2DimResCodeRequest.setTenantCode(uimConfig.getTenantCode());
            uim2DimResCodeRequest.setDimResCode(uimConfig.getDimResCode());

            Uim2Response<List<Uim2DimResourceDto>> listUim2Response = uim2DimPermissionFacade.queryDimResValueList(uim2DimResCodeRequest);
            log.info("RoleRpcImpl -> getDimResource  end,listUim2Response:{}", JSON.toJSONString(listUim2Response));
            if (SUCCESS.equals(listUim2Response.getCode())) {
                return RoleRpcConvert.ins.toUim2DimResourceBOList(listUim2Response.getData());
            }
            return Collections.emptyList();
        } catch (Exception e) {
            log.error("RoleRpcImpl -> getDimResource Exception", e);
            throw new BusinessException(SystemErrorCode.UNKNOWN_ERROR);
        }
    }
}