package com.jdh.o2oservice.b2b.infrastructure.rpc;

import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.O2oQuestionBo;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.rpc.O2oQuestionRpc;
import com.jdh.o2oservice.b2b.infrastructure.rpc.converter.O2oQuestionRpcConvert;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.product.QuestionReadExport;
import com.jdh.o2oservice.export.product.dto.QuestionDTO;
import com.jdh.o2oservice.export.product.query.QuestionPageQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2025/9/22
 */
@Component
@Slf4j
public class O2oQuestionRpcImpl implements O2oQuestionRpc {

    @Resource
    private QuestionReadExport questionReadExport;


    @Override
    public List<O2oQuestionBo> queryQuestionList(List<Long> questionIdList) {
        QuestionPageQuery questionPageQuery = new QuestionPageQuery();
        questionPageQuery.setQuesIds(questionIdList);
        Response<List<QuestionDTO>> response = questionReadExport.queryQuestionList(questionPageQuery);
        if (Objects.nonNull(response) && response.isSuccess()){
            return O2oQuestionRpcConvert.INSTANCE.convert(response.getData());
        }
        return Collections.emptyList();
    }



}
