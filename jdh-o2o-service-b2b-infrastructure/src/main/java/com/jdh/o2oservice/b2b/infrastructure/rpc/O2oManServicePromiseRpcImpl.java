package com.jdh.o2oservice.b2b.infrastructure.rpc;
import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.b2b.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.b2b.base.exception.BusinessErrorCode;
import com.jdh.o2oservice.b2b.base.exception.BusinessException;
import com.jdh.o2oservice.b2b.base.exception.DynamicErrorCode;
import com.jdh.o2oservice.b2b.common.response.PageDto;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.FileUrlBo;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.PromiseTimelineBo;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.QueryPromiseTimelineBo;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.man.*;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.rpc.O2oManServicePromiseRpc;
import com.jdh.o2oservice.b2b.infrastructure.rpc.converter.ManServicePromiseRpcConvert;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.angel.dto.JdhAngelDetailDto;
import com.jdh.o2oservice.export.angel.dto.JdhStationDto;
import com.jdh.o2oservice.export.angel.query.AngelDetailRequest;
import com.jdh.o2oservice.export.dispatch.DispatchJsfExport;
import com.jdh.o2oservice.export.dispatch.cmd.ReDispatchCmd;
import com.jdh.o2oservice.export.dispatch.cmd.TargetDispatchCmd;
import com.jdh.o2oservice.export.dispatch.dto.DispatchTargetStatusDto;
import com.jdh.o2oservice.export.dispatch.dto.JdhDispatchForManDTO;
import com.jdh.o2oservice.export.dispatch.query.DispatchDetailForManRequest;
import com.jdh.o2oservice.export.product.dto.AgencyAppointDateDto;
import com.jdh.o2oservice.export.product.query.AgencyQueryDateRequest;
import com.jdh.o2oservice.export.promise.PromiseJsfExport;
import com.jdh.o2oservice.export.promise.cmd.AgentModifyPromiseCmd;
import com.jdh.o2oservice.export.promise.dto.PromiseTimelineDto;
import com.jdh.o2oservice.export.promise.query.QueryPromiseTimelineRequest;
import com.jdh.o2oservice.export.support.CallJsfExport;
import com.jdh.o2oservice.export.support.FileManageJsfExport;
import com.jdh.o2oservice.export.support.ManViaJsfExport;
import com.jdh.o2oservice.export.support.dto.CallRecordDto;
import com.jdh.o2oservice.export.support.dto.FileUrlDto;
import com.jdh.o2oservice.export.support.query.GetFileUrlRequest;
import com.jdh.o2oservice.export.support.query.QueryCallRecordRequest;
import com.jdh.o2oservice.export.via.dto.ViaCompletePromiseDto;
import com.jdh.o2oservice.export.via.query.ViaCompletePromiseRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @Description 运营服务履约
 * @Date 2025/3/4 下午6:33
 * <AUTHOR>
 **/
@Slf4j
@Service
public class O2oManServicePromiseRpcImpl implements O2oManServicePromiseRpc {

    @Resource
    private CallJsfExport callJsfExport;

    @Resource
    private PromiseJsfExport promiseJsfExport;

    @Resource
    private ManViaJsfExport manViaJsfExport;

    @Resource
    private DispatchJsfExport dispatchJsfExport;

    @Resource
    FileManageJsfExport fileManageJsfExport;

    /**
     * 外呼记录列表
     * @param bo
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.infrastructure.rpc.ManServicePromiseRpcImpl.queryCallRecordList")
    public List<CallRecordBo> queryCallRecordList(QueryCallRecordBo bo) {
        try{
            QueryCallRecordRequest param = new QueryCallRecordRequest();
            param.setPromiseId(bo.getPromiseId());
            Response<List<CallRecordDto>> result = callJsfExport.queryCallRecordList(param);
            log.info("ManServicePromiseRpcImpl queryCallRecordList param={}, result={}", JSON.toJSONString(param), JSON.toJSONString(result));
            if(Objects.isNull(result)) {
                throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
            }
            if(Boolean.FALSE.equals(result.isSuccess())){
                throw new BusinessException(new DynamicErrorCode(result.getCode(), result.getMsg()));
            }
            return ManServicePromiseRpcConvert.INS.toCallRecordBOList(result.getData());
        } catch (BusinessException be) {
            log.error("ManServicePromiseRpcImpl -> queryCallRecordList business exception", be);
            throw new BusinessException(be.getErrorCode());
        } catch (Exception e){
            log.error("ManServicePromiseRpcImpl queryCallRecordList error e", e);
            throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
        }
    }


    /**
     * 查询外呼url
     * @param bo
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.infrastructure.rpc.OperationServicePromiseRpcImpl.queryCallRecordUrl")
    public String queryCallRecordUrl(QueryCallRecordBo bo) {
        try{
            QueryCallRecordRequest param = new QueryCallRecordRequest();
            param.setCallId(bo.getCallId());
            Response<String> result = callJsfExport.queryCallRecordUrl(param);
            log.info("ManServicePromiseRpcImpl queryCallRecordUrl param={}, result={}", JSON.toJSONString(param), JSON.toJSONString(result));
            if(Objects.isNull(result)) {
                throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
            }
            if(Boolean.FALSE.equals(result.isSuccess())){
                throw new BusinessException(new DynamicErrorCode(result.getCode(), result.getMsg()));
            }
            return result.getData();
        } catch (BusinessException be) {
            log.error("ManServicePromiseRpcImpl -> queryCallRecordUrl business exception", be);
            throw new BusinessException(be.getErrorCode());
        } catch (Exception e){
            log.error("ManServicePromiseRpcImpl queryCallRecordUrl error e", e);
            throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
        }
    }

    /**
     * 重新派单
     * @param bo
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.infrastructure.rpc.OperationServicePromiseRpcImpl.reDispatch")
    public Boolean reDispatch(ReDispatchBo bo) {
        try{
            ReDispatchCmd param = new ReDispatchCmd();
            param.setPromiseId(bo.getPromiseId());
            param.setDispatchId(bo.getDispatchId());
            param.setOperator(bo.getOperator());
            param.setRoleType(bo.getRoleType());
            param.setReason(bo.getReason());
            Response<Boolean> result = dispatchJsfExport.reDispatch(param);
            log.info("ManServicePromiseRpcImpl reDispatch param={}, result={}", JSON.toJSONString(param), JSON.toJSONString(result));
            if(Objects.isNull(result)) {
                throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
            }
            if(Boolean.FALSE.equals(result.isSuccess())){
                throw new BusinessException(new DynamicErrorCode(result.getCode(), result.getMsg()));
            }
            return result.getData();
        } catch (BusinessException be) {
            log.error("ManServicePromiseRpcImpl -> reDispatch business exception", be);
            throw new BusinessException(be.getErrorCode());
        } catch (Exception e){
            log.error("ManServicePromiseRpcImpl reDispatch error e", e);
            throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
        }
    }

    /**
     * 指定派单
     * @param bo
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.infrastructure.rpc.OperationServicePromiseRpcImpl.targetDispatch")
    public DispatchTargetStatusBo targetDispatch(TargetDispatchBo bo) {
        try{
            TargetDispatchCmd param = new TargetDispatchCmd();
            param.setPromiseId(bo.getPromiseId());
            param.setDispatchId(bo.getDispatchId());
            param.setOperator(bo.getOperator());
            param.setRoleType(bo.getRoleType());
            param.setReason(bo.getReason());
            param.setTargetAngelId(bo.getTargetAngelId());
            Response<DispatchTargetStatusDto> result = dispatchJsfExport.targetDispatch(param);
            log.info("ManServicePromiseRpcImpl targetDispatch param={}, result={}", JSON.toJSONString(param), JSON.toJSONString(result));
            if(Objects.isNull(result)) {
                throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
            }
            if(Boolean.FALSE.equals(result.isSuccess())){
                throw new BusinessException(new DynamicErrorCode(result.getCode(), result.getMsg()));
            }
            return ManServicePromiseRpcConvert.INS.toDispatchTargetStatusBo(result.getData());
        } catch (BusinessException be) {
            log.error("ManServicePromiseRpcImpl -> targetDispatch business exception", be);
            throw new BusinessException(be.getErrorCode());
        } catch (Exception e){
            log.error("ManServicePromiseRpcImpl targetDispatch error e", e);
            throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
        }
    }

    /**
     * 查询服务者详情
     * @param bo
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.infrastructure.rpc.OperationServicePromiseRpcImpl.queryAngelDetail")
    public JdhAngelDetailBo queryAngelDetail(AngelDetailBo bo) {
        try{
            AngelDetailRequest param = new AngelDetailRequest();
            param.setAngelId(bo.getAngelId());
            Response<JdhAngelDetailDto> result = dispatchJsfExport.queryAngelDetail(param);
            log.info("ManServicePromiseRpcImpl queryAngelDetail param={}, result={}", JSON.toJSONString(param), JSON.toJSONString(result));
            if(Objects.isNull(result)) {
                throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
            }
            if(Boolean.FALSE.equals(result.isSuccess())){
                throw new BusinessException(new DynamicErrorCode(result.getCode(), result.getMsg()));
            }
            return ManServicePromiseRpcConvert.INS.toJdhAngelDetailBo(result.getData());
        } catch (BusinessException be) {
            log.error("ManServicePromiseRpcImpl -> queryAngelDetail business exception", be);
            throw new BusinessException(be.getErrorCode());
        } catch (Exception e){
            log.error("ManServicePromiseRpcImpl queryAngelDetail error e", e);
            throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
        }
    }

    /**
     * 查询预约时间
     * @param bo
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.infrastructure.rpc.ManServicePromiseRpcImpl.queryAvailableTime")
    public List<AgencyAppointDateBo> queryAvailableTime(AgencyQueryDateBo bo) {
        try{
            AgencyQueryDateRequest param = AgencyQueryDateRequest.builder()
                    .promiseId(bo.getPromiseId())
                    .userPin(bo.getUserPin())
                    .parentDateId(bo.getParentDateId())
                    .fullAddress(bo.getFullAddress())
                    .skuIds(bo.getSkuIds())
                    .showTimeType(bo.getShowTimeType())
                    .build();
            Response<List<AgencyAppointDateDto>> result = promiseJsfExport.queryAvailableTime(param);
            log.info("ManServicePromiseRpcImpl queryAvailableTime param={}, result={}", JSON.toJSONString(param), JSON.toJSONString(result));
            if(Objects.isNull(result)) {
                throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
            }
            if(Boolean.FALSE.equals(result.isSuccess())){
                throw new BusinessException(new DynamicErrorCode(result.getCode(), result.getMsg()));
            }
            return ManServicePromiseRpcConvert.INS.toAgencyAppointDateBOList(result.getData());
        } catch (BusinessException be) {
            log.error("ManServicePromiseRpcImpl -> queryAvailableTime business exception", be);
            throw new BusinessException(be.getErrorCode());
        } catch (Exception e){
            log.error("ManServicePromiseRpcImpl queryAvailableTime error e", e);
            throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
        }
    }

    @Override
    @LogAndAlarm
    public List<JdhStationBo> queryStationDtoList(AgencyQueryDateBo bo) {
        try{
            AgencyQueryDateRequest param = AgencyQueryDateRequest.builder()
                    .promiseId(bo.getPromiseId())
                    .userPin(bo.getUserPin())
                    .parentDateId(bo.getParentDateId())
                    .fullAddress(bo.getFullAddress())
                    .skuIds(bo.getSkuIds())
                    .showTimeType(bo.getShowTimeType())
                    .build();
            Response<List<JdhStationDto>> result = promiseJsfExport.queryStationDtoList(param);
            log.info("ManServicePromiseRpcImpl queryStationDtoList param={}, result={}", JSON.toJSONString(param), JSON.toJSONString(result));
            if(Objects.isNull(result)) {
                throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
            }
            if(Boolean.FALSE.equals(result.isSuccess())){
                throw new BusinessException(new DynamicErrorCode(result.getCode(), result.getMsg()));
            }
            return ManServicePromiseRpcConvert.INS.toJdhStationDtoList(result.getData());
        } catch (BusinessException be) {
            log.error("ManServicePromiseRpcImpl -> queryStationDtoList business exception", be);
            throw new BusinessException(be.getErrorCode());
        } catch (Exception e){
            log.error("ManServicePromiseRpcImpl queryStationDtoList error e", e);
            throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
        }
    }


    /**
     * 修改预约时间
     * @param bo
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.infrastructure.rpc.ManServicePromiseRpcImpl.modifyAppointmentTime")
    public Boolean modifyAppointmentTime(AgentModifyPromiseBo bo) {
        try{
            AgentModifyPromiseCmd param = new AgentModifyPromiseCmd();
            param.setPromiseId(bo.getPromiseId());
            param.setAppointmentStartTime(bo.getAppointmentStartTime());
            param.setAppointmentEndTime(bo.getAppointmentEndTime());
            param.setDateType(bo.getDateType());
            param.setIsImmediately(bo.getIsImmediately());
            param.setOperatorRoleType(bo.getOperatorRoleType());
            param.setOperator(bo.getOperator());
            Response<Boolean> result = promiseJsfExport.modifyAppointmentTime(param);
            log.info("ManServicePromiseRpcImpl modifyAppointmentTime param={}, result={}", JSON.toJSONString(param), JSON.toJSONString(result));
            if(Objects.isNull(result)) {
                throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
            }
            if(Boolean.FALSE.equals(result.isSuccess())){
                throw new BusinessException(new DynamicErrorCode(result.getCode(), result.getMsg()));
            }
            return result.getData();
        } catch (BusinessException be) {
            log.error("ManServicePromiseRpcImpl -> modifyAppointmentTime business exception", be);
            throw new BusinessException(be.getErrorCode());
        } catch (Exception e){
            log.error("ManServicePromiseRpcImpl modifyAppointmentTime error e", e);
            throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
        }
    }

    /**
     * 查询完整B2b promise信息
     * @param bo
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.infrastructure.rpc.OperationServicePromiseRpcImpl.queryCompleteB2bPromise")
    public ViaCompletePromiseResBo queryCompleteB2bPromise(ViaCompletePromiseReqBo bo) {
        try{
            ViaCompletePromiseRequest param = new ViaCompletePromiseRequest();
            param.setPromiseId(bo.getPromiseId());
            Response<ViaCompletePromiseDto> result = manViaJsfExport.queryCompleteB2bPromise(param);
            log.info("ManServicePromiseRpcImpl queryCompleteB2bPromise param={}, result={}", JSON.toJSONString(param), JSON.toJSONString(result));
            if(Objects.isNull(result)) {
                throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
            }
            if(Boolean.FALSE.equals(result.isSuccess())){
                throw new BusinessException(new DynamicErrorCode(result.getCode(), result.getMsg()));
            }
            if (Objects.nonNull(result.getData())){
                return JSON.parseObject(JSON.toJSONString(result.getData()), ViaCompletePromiseResBo.class);
            }
        } catch (BusinessException be) {
            log.error("ManServicePromiseRpcImpl -> queryCompleteB2bPromise business exception", be);
            throw new BusinessException(be.getErrorCode());
        } catch (Exception e){
            log.error("ManServicePromiseRpcImpl queryCompleteB2bPromise error e", e);
            throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
        }
        return null;
    }

    /**
     * 查询派单明细
     * @param bo
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.infrastructure.rpc.OperationServicePromiseRpcImpl.queryDispatchDetailList")
    public PageDto<JdhDispatchForManBo> queryDispatchDetailList(DispatchDetailForManBo bo) {
        try{
            DispatchDetailForManRequest param = new DispatchDetailForManRequest();
            param.setDispatchId(bo.getDispatchId());
            param.setDispatchRound(bo.getDispatchRound());
            param.setPageNum(bo.getPageNum());
            param.setPageSize(bo.getPageSize());
            Response<com.jdh.o2oservice.common.result.response.PageDto<JdhDispatchForManDTO>> result = dispatchJsfExport.queryDispatchDetailList(param);
            log.info("OperationServicePromiseRpcImpl queryDispatchDetailList param={}, result={}", JSON.toJSONString(param), JSON.toJSONString(result));
            if(Objects.isNull(result)) {
                throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
            }
            if(Boolean.FALSE.equals(result.isSuccess())){
                throw new BusinessException(new DynamicErrorCode(result.getCode(), result.getMsg()));
            }
            com.jdh.o2oservice.common.result.response.PageDto<JdhDispatchForManDTO> page = result.getData();
            PageDto<JdhDispatchForManBo> pageDto = new PageDto<>();
            pageDto.setTotalPage(page.getTotalPage());
            pageDto.setPageNum(page.getPageNum());
            pageDto.setPageSize(page.getPageSize());
            pageDto.setTotalCount(page.getTotalCount());
            pageDto.setList(ManServicePromiseRpcConvert.INS.toJdhDispatchForManBoList(page.getList()));
            return pageDto;
        } catch (BusinessException be) {
            log.error("ManServicePromiseRpcImpl -> queryDispatchDetailList business exception", be);
            throw new BusinessException(be.getErrorCode());
        } catch (Exception e){
            log.error("ManServicePromiseRpcImpl queryDispatchDetailList error e", e);
            throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
        }
    }

    @Override
    @LogAndAlarm
    public List<FileUrlBo> queryMultiFileUrl(GetFileUrlBo bo) {
        try{
            GetFileUrlRequest request = ManServicePromiseRpcConvert.INS.toGetFileUrlRequest(bo);
            Response<List<FileUrlDto>> result = fileManageJsfExport.getMultiFileUrl(request);
            log.info("OperationServicePromiseRpcImpl queryMultiFileUrl request={}, result={}", JSON.toJSONString(request), JSON.toJSONString(result));
            if(Objects.isNull(result)) {
                throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
            }
            if(Boolean.FALSE.equals(result.isSuccess())){
                throw new BusinessException(new DynamicErrorCode(result.getCode(), result.getMsg()));
            }
            return ManServicePromiseRpcConvert.INS.toFileUrlBo(result.getData());
        } catch (BusinessException be) {
            log.error("ManServicePromiseRpcImpl -> queryDispatchDetailList business exception", be);
            throw new BusinessException(be.getErrorCode());
        } catch (Exception e){
            log.error("ManServicePromiseRpcImpl queryDispatchDetailList error e", e);
            throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
        }
    }

    @Override
    @LogAndAlarm
    public PromiseTimelineBo queryPromiseTimeline(QueryPromiseTimelineBo bo) {
        try{
            QueryPromiseTimelineRequest request = ManServicePromiseRpcConvert.INS.toQueryPromiseTimelineRequest(bo);
            Response<PromiseTimelineDto> result = promiseJsfExport.queryPromiseTimeline(request);
            log.info("OperationServicePromiseRpcImpl queryPromiseTimeline request={}, result={}", JSON.toJSONString(request), JSON.toJSONString(result));
            if(Objects.isNull(result)) {
                throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
            }
            if(Boolean.FALSE.equals(result.isSuccess())){
                throw new BusinessException(new DynamicErrorCode(result.getCode(), result.getMsg()));
            }
            return ManServicePromiseRpcConvert.INS.toPromiseTimelineBo(result.getData());
        } catch (BusinessException be) {
            log.error("ManServicePromiseRpcImpl -> queryPromiseTimeline business exception", be);
            throw new BusinessException(be.getErrorCode());
        } catch (Exception e){
            log.error("ManServicePromiseRpcImpl queryPromiseTimeline error e", e);
            throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
        }
    }

}
