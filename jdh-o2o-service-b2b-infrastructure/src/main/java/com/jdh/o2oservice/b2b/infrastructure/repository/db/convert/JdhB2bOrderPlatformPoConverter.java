package com.jdh.o2oservice.b2b.infrastructure.repository.db.convert;

import com.jdh.o2oservice.b2b.domain.support.orderplatform.model.OrderPlatform;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.po.JdhB2bEnterpriseOrderPlatformPo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Description JdhB2bOrderPlatformPoConverter
 * @Date 2025/2/25 上午11:06
 * <AUTHOR>
 **/
@Mapper
public interface JdhB2bOrderPlatformPoConverter {

    /**
     * INS
     */
    JdhB2bOrderPlatformPoConverter INS = Mappers.getMapper(JdhB2bOrderPlatformPoConverter.class);

    /**
     * po2Entity
     * @param po
     * @return
     */
    OrderPlatform po2Entity(JdhB2bEnterpriseOrderPlatformPo po);

    /**
     * po2实体列表
     *
     * @param po po
     * @return {@link List }<{@link OrderPlatform }>
     */
    List<OrderPlatform> po2EntityList(List<JdhB2bEnterpriseOrderPlatformPo> po);

    /**
     * entity2Po
     * @param orderPlatform
     * @return
     */
    JdhB2bEnterpriseOrderPlatformPo entity2Po(OrderPlatform orderPlatform);

}
