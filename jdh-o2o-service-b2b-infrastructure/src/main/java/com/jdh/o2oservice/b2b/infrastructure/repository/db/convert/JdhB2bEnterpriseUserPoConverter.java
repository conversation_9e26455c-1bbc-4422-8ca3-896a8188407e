package com.jdh.o2oservice.b2b.infrastructure.repository.db.convert;
import com.jdh.o2oservice.b2b.domain.enterpriseuser.model.JdhB2bEnterpriseUser;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.po.JdhB2bEnterpriseUserPo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface JdhB2bEnterpriseUserPoConverter {

    JdhB2bEnterpriseUserPoConverter INSTANCE = Mappers.getMapper(JdhB2bEnterpriseUserPoConverter.class);

    JdhB2bEnterpriseUser po2Entity(JdhB2bEnterpriseUserPo jdhB2bEnterpriseUserPo);

    List<JdhB2bEnterpriseUser> po2Entity(List<JdhB2bEnterpriseUserPo> list);
}
