package com.jdh.o2oservice.b2b.infrastructure.repository.db.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * JdhB2bEnterpriseRemarkPo
 *
 * <AUTHOR>
 * @date 2025/02/26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "jdh_b2b_enterprise_remark",autoResultMap = true)
public class JdhB2bEnterpriseRemarkPo extends JdhBasicPo{

    /**
    * 主键
    */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
    * 企业ID
    */
    private Long enterpriseId;

    /**
    * 企业服务单id
    */
    private Long enterpriseRemarkId;

    /**
    * 备注内容
    */
    private String content;

}