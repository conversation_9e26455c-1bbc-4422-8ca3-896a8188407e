package com.jdh.o2oservice.b2b.infrastructure.repository.db.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;


/**
 * JdhB2bEnterpriseContractPo
 *
 * <AUTHOR>
 * @date 2025/03/07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "jdh_b2b_enterprise_contract",autoResultMap = true)
public class JdhB2bEnterpriseContractPo extends JdhBasicPo{

    /**
    * 主键
    */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
    * 企业ID
    */
    private Long enterpriseId;
    /**
     * 合同ID
     */
    private Long contractId;
    /**
     * 合同编号
     */
    private String contractNumber;
    /**
     * 合同名称
     */
    private String name;
    /**
     * 合同主体
     */
    private Integer ou;
    /**
     * 有效标识：1/0
     */
    private Integer valid;
    /**
     * 合同开始时间
     */
    private Date startTime;
    /**
     * 合同结束时间
     */
    private Date endTime;
    /**
     * 合同状态
     */
    private Integer status;
    /**
     * 结算周期:1-日 2-月 3-季度 4-年 5-周 6-半月 7-其他时间
     */
    private Integer settlementPeriod;
    /**
     * 结算天数
     */
    private Integer settlementDays;
    /**
     * 合同扩展字段
     */
    @TableField("extMap")
    private String extMap;
    /**
    * 扩展信息
    */
    private String extend;
}