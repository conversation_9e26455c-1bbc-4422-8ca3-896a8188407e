package com.jdh.o2oservice.b2b.infrastructure.repository.db;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.b2b.base.enums.YnStatusEnum;
import com.jdh.o2oservice.b2b.domain.enterprise.model.JdhB2bEnterprise;
import com.jdh.o2oservice.b2b.domain.enterprise.model.JdhEnterpriseIdentifier;
import com.jdh.o2oservice.b2b.domain.enterprise.repository.JdhB2bEnterpriseRepository;
import com.jdh.o2oservice.b2b.domain.enterprise.repository.query.JdhB2bEnterpriseQuery;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.convert.JdhB2bEnterprisePoConverter;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.convert.JdhBasicPoConverter;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.mapper.JdhB2bEnterpriseMapper;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.po.JdhB2bEnterprisePo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @Description 企业信息
 * @Date 2025/2/24 下午6:25
 * <AUTHOR>
 **/
@Repository
@Slf4j
public class JdhB2bEnterpriseRepositoryImpl implements JdhB2bEnterpriseRepository {

    @Resource
    private JdhB2bEnterpriseMapper jdhB2bEnterpriseMapper;

    /**
     * 保存
     *
     * @param entity
     * @return count
     */
    @Override
    public int save(JdhB2bEnterprise entity) {
        JdhB2bEnterprisePo po = JdhB2bEnterprisePo.builder().verticalCode(entity.getVerticalCode())
                .enterpriseId(entity.getEnterpriseId())
                .name(entity.getName())
                .shortName(entity.getShortName())
                .needIntendedNurse(entity.getNeedIntendedNurse())
                .director(entity.getDirector())
                .internalRemark(entity.getInternalRemark())
                .projectInformation(entity.getProjectInformation())
                .extend(entity.getExtend())
                .createUser(entity.getOperator())
                .updateUser(entity.getOperator())
                .needApi(entity.getNeedApi())
                .needAngelServiceRecord(entity.getNeedAngelServiceRecord())
                .needAngelServiceRecord(entity.getNeedAngelServiceRecord())
                .build();
        JdhBasicPoConverter.initInsertBasicPo(po);
        return jdhB2bEnterpriseMapper.insert(po);
    }

    /**
     * 更新
     *
     * @param entity
     * @return count
     */
    @Override
    public int update(JdhB2bEnterprise entity) {
        LambdaUpdateWrapper<JdhB2bEnterprisePo> updateWrapper = Wrappers.lambdaUpdate();
        JdhB2bEnterprisePo po = JdhB2bEnterprisePo.builder().build();
        updateWrapper.set(entity.getAvailable() != null, JdhB2bEnterprisePo::getAvailable, entity.getAvailable())
                .set(StringUtils.isNotBlank(entity.getName()), JdhB2bEnterprisePo::getName, entity.getName())
                .set(StringUtils.isNotBlank(entity.getShortName() ), JdhB2bEnterprisePo::getShortName, entity.getShortName())
                .set(entity.getNeedIntendedNurse() != null, JdhB2bEnterprisePo::getNeedIntendedNurse, entity.getNeedIntendedNurse())
                .set(StringUtils.isNotBlank(entity.getDirector()), JdhB2bEnterprisePo::getDirector, entity.getDirector())
                .set(StringUtils.isNotBlank(entity.getInternalRemark()), JdhB2bEnterprisePo::getInternalRemark, entity.getInternalRemark())
                .set(StringUtils.isNotBlank(entity.getProjectInformation()), JdhB2bEnterprisePo::getProjectInformation, entity.getProjectInformation())
                .set(Objects.nonNull(entity.getNeedApi()), JdhB2bEnterprisePo::getNeedApi, entity.getNeedApi())
                .set(Objects.nonNull(entity.getNeedAngelServiceRecord()), JdhB2bEnterprisePo::getNeedAngelServiceRecord, entity.getNeedAngelServiceRecord())
                .set(Objects.nonNull(entity.getNeedPromiseTimeline()), JdhB2bEnterprisePo::getNeedPromiseTimeline, entity.getNeedPromiseTimeline())
                .setSql("`version` = version+1")
                .eq(JdhB2bEnterprisePo::getEnterpriseId, entity.getEnterpriseId());
        Date now = new Date();
        po.setUpdateUser(entity.getOperator());
        po.setUpdateTime(now);
        return jdhB2bEnterpriseMapper.update(po, updateWrapper);
    }

    /**
     * 更新企业状态
     *
     * @param entity
     * @return count
     */
    @Override
    public int updateEnterpriseStatus(JdhB2bEnterprise entity) {
        LambdaUpdateWrapper<JdhB2bEnterprisePo> updateWrapper = Wrappers.lambdaUpdate();
        JdhB2bEnterprisePo po = JdhB2bEnterprisePo.builder().build();
        updateWrapper.set(JdhB2bEnterprisePo::getAvailable, entity.getAvailable())
                .setSql("`version` = version+1")
                .eq(JdhB2bEnterprisePo::getEnterpriseId, entity.getEnterpriseId());
        Date now = new Date();
        po.setUpdateUser(entity.getUpdateUser());
        po.setUpdateTime(now);
        return jdhB2bEnterpriseMapper.update(po, updateWrapper);
    }

    /**
     * 通过Identify 查询
     * @param identifier
     * @return
     */
    @Override
    public JdhB2bEnterprise find(JdhEnterpriseIdentifier identifier) {
        Long enterpriseId = identifier.getEnterpriseId();
        LambdaQueryWrapper<JdhB2bEnterprisePo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhB2bEnterprisePo::getEnterpriseId, enterpriseId)
                .eq(JdhB2bEnterprisePo::getYn, YnStatusEnum.YES.getCode());
        JdhB2bEnterprisePo jdhB2bEnterprisePo = jdhB2bEnterpriseMapper.selectOne(queryWrapper);
        return JdhB2bEnterprisePoConverter.INSTANCE.po2Entity(jdhB2bEnterprisePo);
    }

    /**
     * 分页查询企业列表
     * @param query
     * @return
     */
    @Override
    public Page<JdhB2bEnterprise> queryPageEnterprise(JdhB2bEnterpriseQuery query) {
        LambdaQueryWrapper<JdhB2bEnterprisePo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(query.getEnterpriseId() != null, JdhB2bEnterprisePo::getEnterpriseId, query.getEnterpriseId())
                .like(StringUtils.isNotBlank(query.getName()), JdhB2bEnterprisePo::getName, query.getName())
                .like(StringUtils.isNotBlank(query.getShortName()), JdhB2bEnterprisePo::getShortName, query.getShortName())
                .eq(query.getAvailable() != null, JdhB2bEnterprisePo::getAvailable, query.getAvailable())
                .eq(StringUtils.isNotBlank(query.getVerticalCode()), JdhB2bEnterprisePo::getVerticalCode, query.getVerticalCode())
                .eq(JdhB2bEnterprisePo::getYn, YnStatusEnum.YES.getCode())
                .orderByDesc(JdhB2bEnterprisePo::getCreateTime);
        Page<JdhB2bEnterprisePo> param = new Page<>(query.getPageNum(), query.getPageSize());
        IPage<JdhB2bEnterprisePo> iPage = jdhB2bEnterpriseMapper.selectPage(param, queryWrapper);
        List<JdhB2bEnterprise> dataList = JdhB2bEnterprisePoConverter.INSTANCE.po2Entity(iPage.getRecords());
        return JdhBasicPoConverter.initPage(iPage, dataList);
    }

    /**
     * 企业列表
     * @param query
     * @return
     */
    public List<JdhB2bEnterprise> findList(JdhB2bEnterpriseQuery query) {
        LambdaQueryWrapper<JdhB2bEnterprisePo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(query.getEnterpriseId() != null, JdhB2bEnterprisePo::getEnterpriseId, query.getEnterpriseId())
                .in(CollectionUtils.isNotEmpty(query.getEnterpriseIdList()), JdhB2bEnterprisePo::getEnterpriseId, query.getEnterpriseIdList())
                .like(StringUtils.isNotBlank(query.getName()), JdhB2bEnterprisePo::getName, query.getName())
                .like(StringUtils.isNotBlank(query.getShortName()), JdhB2bEnterprisePo::getShortName, query.getShortName())
                .eq(JdhB2bEnterprisePo::getYn, YnStatusEnum.YES.getCode());
        List<JdhB2bEnterprisePo> poList = jdhB2bEnterpriseMapper.selectList(queryWrapper);
        return JdhB2bEnterprisePoConverter.INSTANCE.po2Entity(poList);
    }
}
