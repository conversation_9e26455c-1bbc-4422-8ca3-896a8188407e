package com.jdh.o2oservice.b2b.infrastructure.repository.db.po;

import lombok.Data;

import java.util.Date;

/**
* @description JdhBusinessModePo简介
* <AUTHOR>
* @date 2023-12-22 10:35:07
*/
@Data
public class JdhBasicPo {

    /**
     * 数据来源分支
     */
    private String branch;
    /**
     * 版本号
     */
    private Integer version;

    /**
     * 是否有效 0：无效；1：有效
     */
    private Integer yn;

    /**
     * 创建人pin
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人pin
     */
    private String updateUser;

    /**
     * 更新时间
     */
    private Date updateTime;
}