package com.jdh.o2oservice.b2b.infrastructure.rpc.converter;

import com.jd.lbs.jdlbsapi.dto.c2c.TextParseReqDto;
import com.jd.lbs.jdlbsapi.dto.c2c.TextParseResult;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.TextParseAddressBO;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.param.AddressTextParseRpcParam;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface JdhAddressRpcConverter {

    JdhAddressRpcConverter instance = Mappers.getMapper(JdhAddressRpcConverter.class);

    /**
     *
     * @param param
     * @return
     */
    TextParseReqDto convertToTextParseReqDto(AddressTextParseRpcParam param);

    /**
     *
     * @param textParseResult
     * @return
     */
    TextParseAddressBO convertToTextParseAddressBO(TextParseResult textParseResult);
}
