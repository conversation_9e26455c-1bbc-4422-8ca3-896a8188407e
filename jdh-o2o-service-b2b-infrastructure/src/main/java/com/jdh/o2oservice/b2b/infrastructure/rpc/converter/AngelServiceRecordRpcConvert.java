package com.jdh.o2oservice.b2b.infrastructure.rpc.converter;

import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.AngelServiceRecordBBo;
import com.jdh.o2oservice.export.angelpromise.dto.AngelServiceRecordBDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/9/19
 */
@Mapper
public interface AngelServiceRecordRpcConvert {


    /**
     * 获取AngelServiceRecordRpcConvert接口的实例。
     */
    AngelServiceRecordRpcConvert INSTANCE = Mappers.getMapper(AngelServiceRecordRpcConvert.class);


    /**
     * 将 AngelServiceRecordBDto 列表转换为 AngelServiceRecordBBo 列表
     * @param angelServiceRecordBDtoList AngelServiceRecordBDto 对象列表
     * @return 转换后的 AngelServiceRecordBBo 对象列表
     */
    List<AngelServiceRecordBBo> convert(List<AngelServiceRecordBDto> angelServiceRecordBDtoList);

}
