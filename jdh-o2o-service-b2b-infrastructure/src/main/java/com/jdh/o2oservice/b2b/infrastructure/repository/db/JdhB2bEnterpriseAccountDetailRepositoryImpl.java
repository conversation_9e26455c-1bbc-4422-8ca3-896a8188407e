package com.jdh.o2oservice.b2b.infrastructure.repository.db;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jdh.o2oservice.b2b.base.enums.YnStatusEnum;
import com.jdh.o2oservice.b2b.domain.enterpriseaccount.model.JdhB2bEnterpriseAccountDetail;
import com.jdh.o2oservice.b2b.domain.enterpriseaccount.model.JdhEnterpriseAccountDetailIdentifier;
import com.jdh.o2oservice.b2b.domain.enterpriseaccount.repository.JdhB2bEnterpriseAccountDetailRepository;
import com.jdh.o2oservice.b2b.domain.enterpriseaccount.repository.query.JdhB2bEnterpriseAccountDetailQuery;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.convert.JdhB2bEnterpriseAccountPoConverter;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.convert.JdhBasicPoConverter;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.mapper.JdhB2bEnterpriseAccountDetailMapper;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.po.JdhB2bEnterpriseAccountDetailPo;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.po.JdhB2bEnterprisePo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName:JdhB2bEnterpriseAccountDetailRepositoryImpl
 * @Description: B2b企业账户明细
 * @Author: liwenming
 * @Date: 2025/3/11 14:14
 * @Vserion: 1.0
 **/
@Repository
@Slf4j
public class JdhB2bEnterpriseAccountDetailRepositoryImpl implements JdhB2bEnterpriseAccountDetailRepository {



    /** */
    @Resource
    private JdhB2bEnterpriseAccountDetailMapper jdhB2bEnterpriseAccountDetailMapper;


    /**
     * 通过Identify 查询
     *
     * @param identifier
     * @return
     */
    @Override
    public JdhB2bEnterpriseAccountDetail find(JdhEnterpriseAccountDetailIdentifier identifier) {
        return null;
    }

    /**
     * 查询
     *
     * @param queryContext
     * @return
     */
    @Override
    public JdhB2bEnterpriseAccountDetail queryJdhB2bEnterpriseAccountDetail(JdhB2bEnterpriseAccountDetailQuery queryContext) {
        LambdaQueryWrapper<JdhB2bEnterpriseAccountDetailPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(queryContext.getEnterpriseId()),JdhB2bEnterpriseAccountDetailPo::getEnterpriseId,queryContext.getEnterpriseId())
                .eq(Objects.nonNull(queryContext.getPromiseId()),JdhB2bEnterpriseAccountDetailPo::getSourceReceiptId,queryContext.getPromiseId())
                .eq(Objects.nonNull(queryContext.getSourceReceiptType()),JdhB2bEnterpriseAccountDetailPo::getSourceReceiptType,queryContext.getSourceReceiptType())
                .eq(Objects.nonNull(queryContext.getFreezeType()),JdhB2bEnterpriseAccountDetailPo::getFreezeType,queryContext.getFreezeType())
                .in(CollUtil.isNotEmpty(queryContext.getSourceReceiptIdList()),JdhB2bEnterpriseAccountDetailPo::getSourceReceiptId,queryContext.getSourceReceiptIdList())
                .eq(JdhB2bEnterpriseAccountDetailPo::getYn, YnStatusEnum.YES.getCode());
        JdhB2bEnterpriseAccountDetailPo jdhB2bEnterpriseAccountDetailPo = jdhB2bEnterpriseAccountDetailMapper.selectOne(queryWrapper);
        if(Objects.isNull(jdhB2bEnterpriseAccountDetailPo)){
            return null;
        }
        return JdhB2bEnterpriseAccountPoConverter.INSTANCE.po2Entity(jdhB2bEnterpriseAccountDetailPo);
    }

    /**
     * 查询DetailList
     *
     * @param queryContext
     * @return
     */
    @Override
    public List<JdhB2bEnterpriseAccountDetail> queryEnterpriseAccountDetailList(JdhB2bEnterpriseAccountDetailQuery queryContext) {
        LambdaQueryWrapper<JdhB2bEnterpriseAccountDetailPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(queryContext.getEnterpriseId()),JdhB2bEnterpriseAccountDetailPo::getEnterpriseId,queryContext.getEnterpriseId())
                .eq(Objects.nonNull(queryContext.getSourceReceiptId()),JdhB2bEnterpriseAccountDetailPo::getSourceReceiptId,queryContext.getSourceReceiptId())
                .in(CollUtil.isNotEmpty(queryContext.getSourceReceiptIdList()),JdhB2bEnterpriseAccountDetailPo::getSourceReceiptId,queryContext.getSourceReceiptIdList())
                .eq(Objects.nonNull(queryContext.getSourceReceiptType()),JdhB2bEnterpriseAccountDetailPo::getSourceReceiptType,queryContext.getSourceReceiptType())
                .eq(Objects.nonNull(queryContext.getFreezeType()),JdhB2bEnterpriseAccountDetailPo::getFreezeType,queryContext.getFreezeType())
                .eq(JdhB2bEnterpriseAccountDetailPo::getYn, YnStatusEnum.YES.getCode())
                .orderByDesc(JdhB2bEnterpriseAccountDetailPo::getCreateTime);
        List<JdhB2bEnterpriseAccountDetailPo> accountDetailPoList = jdhB2bEnterpriseAccountDetailMapper.selectList(queryWrapper);
        if(CollUtil.isEmpty(accountDetailPoList)){
            return Collections.emptyList();
        }
        return JdhB2bEnterpriseAccountPoConverter.INSTANCE.po2EntityList(accountDetailPoList);
    }

    /**
     * 保存
     * @param jdhB2bEnterpriseAccountDetail
     * @return
     */
    @Override
    public int save(JdhB2bEnterpriseAccountDetail jdhB2bEnterpriseAccountDetail) {
        JdhB2bEnterpriseAccountDetailPo po = JdhB2bEnterpriseAccountDetailPo.builder()
                .enterpriseId(jdhB2bEnterpriseAccountDetail.getEnterpriseId())
                .accountId(jdhB2bEnterpriseAccountDetail.getAccountId())
                .accountDetailId(jdhB2bEnterpriseAccountDetail.getAccountDetailId())
                .sourceReceiptType(jdhB2bEnterpriseAccountDetail.getSourceReceiptType())
                .sourceReceiptId(jdhB2bEnterpriseAccountDetail.getSourceReceiptId())
                .freezeAmount(jdhB2bEnterpriseAccountDetail.getFreezeAmount())
                .freezeType(jdhB2bEnterpriseAccountDetail.getFreezeType())
                .extend(jdhB2bEnterpriseAccountDetail.getExtend())
                .createUser(jdhB2bEnterpriseAccountDetail.getOperator())
                .updateUser(jdhB2bEnterpriseAccountDetail.getOperator())
                .build();
        JdhBasicPoConverter.initInsertBasicPo(po);
        return jdhB2bEnterpriseAccountDetailMapper.insert(po);
    }
}
