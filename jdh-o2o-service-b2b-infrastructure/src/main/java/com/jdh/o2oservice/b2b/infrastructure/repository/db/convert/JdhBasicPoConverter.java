package com.jdh.o2oservice.b2b.infrastructure.repository.db.convert;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.b2b.base.constant.NumConstant;
import com.jdh.o2oservice.b2b.base.enums.YnStatusEnum;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.po.JdhBasicPo;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.po.JdhBasicTimePo;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import java.util.Date;
import java.util.List;

/**
 * 通用基础的PO 转换
 * @author: yangxiyu
 * @date: 2024/1/17 9:47 下午
 * @version: 1.0
 */
@Component
public class JdhBasicPoConverter {

    /**
     * 当前配置
     */
    private static String ACTIVE;
    /**
     * 初始注入maven环境
     */
    @Value("${spring.profiles.active}")
    public void setActive(String value){
        ACTIVE = value;
    }

    /**
     *
     * @param po
     */
    public static void initInsertBasicPo(JdhBasicPo po){
        Date cur = new Date();
        po.setCreateTime(cur);
        po.setUpdateTime(cur);
        po.setYn(YnStatusEnum.YES.getCode());
        po.setBranch(ACTIVE);
        po.setVersion(NumConstant.NUM_1);
    }
    /**
     *
     * @param po
     */
    public static void initUpdateBasicPo(JdhBasicPo po){
        Date cur = new Date();
        po.setUpdateTime(cur);
        po.setBranch(ACTIVE);
    }

    /**
     *
     * @param po
     */
    public static void initInsertBasicTimePo(JdhBasicTimePo po){
        Date cur = new Date();
        po.setCreateTime(cur);
        po.setUpdateTime(cur);
        po.setYn(YnStatusEnum.YES.getCode());
    }

    /**
     *
     * @param sourcePage
     * @param record
     * @param <T>
     * @return
     */
    public static  <T> Page<T> initPage(IPage<?> sourcePage, List<T> record){
        Page<T> res = new Page<>();
        res.setRecords(record);
        res.setTotal(sourcePage.getTotal());
        res.setPages(sourcePage.getPages());
        res.setSize(sourcePage.getSize());
        res.setCurrent(sourcePage.getCurrent());
        return res;
    }
}
