package com.jdh.o2oservice.b2b.infrastructure.rpc;

import com.alibaba.fastjson.JSON;
import com.jd.contract.api.ContractQueryApi;
import com.jd.contract.api.model.ContractBaseDTO;
import com.jd.contract.api.model.response.GetBaseContractResponse;
import com.jdh.o2oservice.b2b.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.b2b.base.exception.BusinessErrorCode;
import com.jdh.o2oservice.b2b.base.exception.BusinessException;
import com.jdh.o2oservice.b2b.base.exception.SystemErrorCode;
import com.jdh.o2oservice.b2b.domain.enterprisecontract.bo.EnterpriseContractBo;
import com.jdh.o2oservice.b2b.domain.enterprisecontract.rpc.EnterpriseContractRpc;
import com.jdh.o2oservice.b2b.infrastructure.rpc.converter.EnterpriseContractRpcConvert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.BeanCreationException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;


/**
 * @Description 企业合同信息RPC
 *
 * @Date 2025/4/29 下午8:15
 * <AUTHOR>
@Slf4j
@Service
public class EnterpriseContractRpcImpl implements EnterpriseContractRpc {

    /**
     * contractQueryApi
     */
    @Resource
    private ContractQueryApi contractQueryApi;


    /**
     *
     * @param contractNumber
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.infrastructure.rpc.EnterpriseContractRpcImpl.queryContractInfo")
    public EnterpriseContractBo queryContractInfo(String contractNumber) {
        try{
            GetBaseContractResponse response = contractQueryApi.getContractBaseInfo(contractNumber,null);
            log.info("EnterpriseContractRpcImpl queryContractInfo response={}", JSON.toJSONString(response));
            if(Objects.nonNull(response)){
                if(response.getResult().isSuccess()){
                    ContractBaseDTO contractBaseDTO = response.getContractBaseDTO();
                    return EnterpriseContractRpcConvert.INS.toDispatchTargetStatusBo(contractBaseDTO);
                }else {
                    throw new BusinessException(SystemErrorCode.CONTRACT_NOT_EXIST);
                }
            }
            throw new BusinessException(SystemErrorCode.CONTRACT_NOT_EXIST);
        }catch (BusinessException be) {
            log.error("EnterpriseContractRpcImpl -> queryContractInfo business exception", be);
            throw be;
        }catch (Exception e) {
            log.error("EnterpriseContractRpcImpl -> queryContractInfo exception", e);
            throw e;
        }
    }
}
