package com.jdh.o2oservice.b2b.infrastructure.repository.db;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.b2b.base.enums.YnStatusEnum;
import com.jdh.o2oservice.b2b.domain.enterprisesku.model.JdhB2bEnterpriseSku;
import com.jdh.o2oservice.b2b.domain.enterprisesku.model.JdhEnterpriseSkuIdentifier;
import com.jdh.o2oservice.b2b.domain.enterprisesku.repository.JdhB2bEnterpriseSkuRepository;
import com.jdh.o2oservice.b2b.domain.enterprisesku.repository.query.JdhB2bEnterpriseSkuQuery;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.convert.JdhB2bEnterpriseSkuPoConverter;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.convert.JdhBasicPoConverter;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.mapper.JdhB2bEnterpriseSkuMapper;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.po.JdhB2bEnterpriseSkuPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @Description 企业sku
 * @Date 2025/2/24 下午6:29
 * <AUTHOR>
 **/
@Repository
@Slf4j
public class JdhB2bEnterpriseSkuRepositoryImpl implements JdhB2bEnterpriseSkuRepository {

    @Resource
    private JdhB2bEnterpriseSkuMapper jdhB2bEnterpriseSkuMapper;

    /**
     * 保存
     *
     * @param entity
     * @return count
     */
    @Override
    public int save(JdhB2bEnterpriseSku entity) {
        JdhB2bEnterpriseSkuPo po = JdhB2bEnterpriseSkuPo.builder().enterpriseId(entity.getEnterpriseId())
                .enterpriseSkuId(entity.getEnterpriseSkuId())
                .skuId(entity.getSkuId())
                .channelPrice(entity.getChannelPrice())
                .priceType(entity.getPriceType())
                .singlePrice(entity.getSinglePrice())
                .nurseOutCancelDeduction(entity.getNurseOutCancelDeduction())
                .nurseServedCancelDeduction(entity.getNurseServedCancelDeduction())
                .contractNumber(entity.getContractNumber())
                .extend(entity.getExtend())
                .createUser(entity.getOperator())
                .updateUser(entity.getOperator())
                .build();
        JdhBasicPoConverter.initInsertBasicPo(po);
        return jdhB2bEnterpriseSkuMapper.insert(po);
    }

    /**
     * 更新
     *
     * @param entity
     * @return count
     */
    @Override
    public int update(JdhB2bEnterpriseSku entity) {
        LambdaUpdateWrapper<JdhB2bEnterpriseSkuPo> updateWrapper = Wrappers.lambdaUpdate();
        JdhB2bEnterpriseSkuPo po = JdhB2bEnterpriseSkuPo.builder().build();
        updateWrapper.set(entity.getChannelPrice() != null, JdhB2bEnterpriseSkuPo::getChannelPrice, entity.getChannelPrice())
                .set(entity.getPriceType() != null, JdhB2bEnterpriseSkuPo::getPriceType, entity.getPriceType())
                .set(entity.getChannelPrice() != null, JdhB2bEnterpriseSkuPo::getChannelPrice, entity.getChannelPrice())
                .set(entity.getSinglePrice() != null, JdhB2bEnterpriseSkuPo::getSinglePrice, entity.getSinglePrice())
                .set(entity.getNurseOutCancelDeduction() != null, JdhB2bEnterpriseSkuPo::getNurseOutCancelDeduction, entity.getNurseOutCancelDeduction())
                .set(entity.getNurseServedCancelDeduction() != null, JdhB2bEnterpriseSkuPo::getNurseServedCancelDeduction, entity.getNurseServedCancelDeduction())
                .set(StringUtils.isNotBlank(entity.getContractNumber()), JdhB2bEnterpriseSkuPo::getContractNumber, entity.getContractNumber())
                .set(entity.getSkuId() != null, JdhB2bEnterpriseSkuPo::getSkuId, entity.getSkuId())
                .setSql("`version` = version+1")
                .eq(JdhB2bEnterpriseSkuPo::getEnterpriseSkuId, entity.getEnterpriseSkuId());
        Date now = new Date();
        po.setUpdateUser(entity.getOperator());
        po.setUpdateTime(now);
        return jdhB2bEnterpriseSkuMapper.update(po, updateWrapper);
    }

    /**
     * 删除
     *
     * @param entity
     * @return count
     */
    @Override
    public int delete(JdhB2bEnterpriseSku entity) {
        LambdaUpdateWrapper<JdhB2bEnterpriseSkuPo> updateWrapper = Wrappers.lambdaUpdate();
        JdhB2bEnterpriseSkuPo po = JdhB2bEnterpriseSkuPo.builder().build();
        updateWrapper.set(JdhB2bEnterpriseSkuPo::getYn, YnStatusEnum.NO.getCode())
                .setSql("`version` = version+1")
                .eq(JdhB2bEnterpriseSkuPo::getEnterpriseSkuId, entity.getEnterpriseSkuId());
        Date now = new Date();
        po.setUpdateUser(entity.getUpdateUser());
        po.setUpdateTime(now);
        return jdhB2bEnterpriseSkuMapper.update(po, updateWrapper);
    }

    /**
     * 通过Identify 查询
     * @param identifier
     * @return
     */
    @Override
    public JdhB2bEnterpriseSku find(JdhEnterpriseSkuIdentifier identifier) {
        Long enterpriseSkuId = identifier.getEnterpriseSkuId();
        LambdaQueryWrapper<JdhB2bEnterpriseSkuPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhB2bEnterpriseSkuPo::getEnterpriseSkuId, enterpriseSkuId).eq(JdhB2bEnterpriseSkuPo::getYn, YnStatusEnum.YES.getCode());
        JdhB2bEnterpriseSkuPo jdhB2bEnterpriseSkuPo = jdhB2bEnterpriseSkuMapper.selectOne(queryWrapper);
        return JdhB2bEnterpriseSkuPoConverter.INSTANCE.po2Entity(jdhB2bEnterpriseSkuPo);
    }

    /**
     * 分页查询企业sku列表
     * @param query
     * @return
     */
    @Override
    public Page<JdhB2bEnterpriseSku> queryPageEnterpriseSku(JdhB2bEnterpriseSkuQuery query) {
        LambdaQueryWrapper<JdhB2bEnterpriseSkuPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(query.getEnterpriseSkuId() != null, JdhB2bEnterpriseSkuPo::getEnterpriseSkuId, query.getEnterpriseSkuId())
                .eq(query.getEnterpriseId() != null, JdhB2bEnterpriseSkuPo::getEnterpriseId, query.getEnterpriseId())
                .eq(JdhB2bEnterpriseSkuPo::getYn, YnStatusEnum.YES.getCode())
                .orderByDesc(JdhB2bEnterpriseSkuPo::getCreateTime);
        Page<JdhB2bEnterpriseSkuPo> param = new Page<>(query.getPageNum(), query.getPageSize());
        IPage<JdhB2bEnterpriseSkuPo> iPage = jdhB2bEnterpriseSkuMapper.selectPage(param, queryWrapper);
        List<JdhB2bEnterpriseSku> dataList = JdhB2bEnterpriseSkuPoConverter.INSTANCE.po2Entity(iPage.getRecords());
        return JdhBasicPoConverter.initPage(iPage, dataList);
    }

    /**
     * 企业sku列表
     * @param query
     * @return
     */
    @Override
    public List<JdhB2bEnterpriseSku> findList(JdhB2bEnterpriseSkuQuery query) {
        LambdaQueryWrapper<JdhB2bEnterpriseSkuPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(query.getEnterpriseId() != null, JdhB2bEnterpriseSkuPo::getEnterpriseId, query.getEnterpriseId())
                .in(CollectionUtils.isNotEmpty(query.getEnterpriseIdList()), JdhB2bEnterpriseSkuPo::getEnterpriseId, query.getEnterpriseIdList())
                .in(CollectionUtils.isNotEmpty(query.getEnterpriseSkuIdList()), JdhB2bEnterpriseSkuPo::getEnterpriseSkuId, query.getEnterpriseSkuIdList())
                .eq(query.getEnterpriseSkuId() != null, JdhB2bEnterpriseSkuPo::getEnterpriseId, query.getEnterpriseSkuId())
                .eq(query.getSkuId() != null, JdhB2bEnterpriseSkuPo::getSkuId, query.getSkuId())
                .eq(StringUtils.isNotBlank(query.getContractNumber()), JdhB2bEnterpriseSkuPo::getContractNumber, query.getContractNumber())
                .eq(JdhB2bEnterpriseSkuPo::getYn, YnStatusEnum.YES.getCode());
        List<JdhB2bEnterpriseSkuPo> poList = jdhB2bEnterpriseSkuMapper.selectList(queryWrapper);
        return JdhB2bEnterpriseSkuPoConverter.INSTANCE.po2Entity(poList);
    }
}
