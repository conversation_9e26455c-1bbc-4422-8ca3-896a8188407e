package com.jdh.o2oservice.b2b.infrastructure.rpc.converter;

import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.IntendedAngelBo;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.ctx.QueryIntendedAngelCtx;
import com.jdh.o2oservice.export.trade.dto.IntendedAngelDTO;
import com.jdh.o2oservice.export.trade.query.QueryIntendedAngelParam;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface O2oServiceTradeRpcConvert {

    /**
     * 实例
     */
    O2oServiceTradeRpcConvert INS = Mappers.getMapper(O2oServiceTradeRpcConvert.class);


    QueryIntendedAngelParam ctx2QueryIntendedAngelParam(QueryIntendedAngelCtx ctx);
    IntendedAngelBo toIntendedAngelBo(IntendedAngelDTO dto);
}
