package com.jdh.o2oservice.b2b.infrastructure.rpc.converter;
import com.jd.contract.api.model.ContractBaseDTO;
import com.jdh.o2oservice.b2b.domain.enterprisecontract.bo.EnterpriseContractBo;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.man.*;
import com.jdh.o2oservice.export.angel.dto.JdhAngelDetailDto;
import com.jdh.o2oservice.export.dispatch.dto.DispatchTargetStatusDto;
import com.jdh.o2oservice.export.dispatch.dto.JdhDispatchForManDTO;
import com.jdh.o2oservice.export.product.dto.AgencyAppointDateDto;
import com.jdh.o2oservice.export.support.dto.CallRecordDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Description EnterpriseContractRpcConvert
 * @Date 2025/4/29 16:31
 * <AUTHOR>
 **/
@Mapper
public interface EnterpriseContractRpcConvert {

    EnterpriseContractRpcConvert INS = Mappers.getMapper(EnterpriseContractRpcConvert.class);

    /**
     *
     * @param contractBaseDTO
     * @return
     */
    EnterpriseContractBo toDispatchTargetStatusBo(ContractBaseDTO contractBaseDTO);
}
