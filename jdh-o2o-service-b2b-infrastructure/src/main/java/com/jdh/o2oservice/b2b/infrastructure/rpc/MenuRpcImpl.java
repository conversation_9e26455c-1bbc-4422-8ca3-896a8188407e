package com.jdh.o2oservice.b2b.infrastructure.rpc;
import com.alibaba.fastjson.JSON;
import com.jd.enterprise.api.client.HrUserClient;
import com.jd.enterprise.api.client.HrUserService;
import com.jd.enterprise.api.util.AsiaHrMdmHolder;
import com.jd.enterprise.api.vo.UserVo;
import com.jd.uim2.facade.jsf.Uim2MenuFacade;
import com.jd.uim2.facade.request.user.Uim2UserRequest;
import com.jd.uim2.facade.response.Uim2Response;
import com.jd.uim2.facade.response.menu.Uim2MenuDto;
import com.jdh.o2oservice.b2b.base.exception.ArgumentsException;
import com.jdh.o2oservice.b2b.base.exception.BusinessErrorCode;
import com.jdh.o2oservice.b2b.base.exception.BusinessException;
import com.jdh.o2oservice.b2b.base.exception.SystemErrorCode;
import com.jdh.o2oservice.b2b.common.config.UimB2bConfig;
import com.jdh.o2oservice.b2b.domain.support.user.uim.bo.Uim2MenuBO;
import com.jdh.o2oservice.b2b.domain.support.user.uim.bo.UserBO;
import com.jdh.o2oservice.b2b.domain.support.user.uim.param.Uim2UserContext;
import com.jdh.o2oservice.b2b.domain.support.user.uim.rpc.MenuRpc;
import com.jdh.o2oservice.b2b.infrastructure.rpc.converter.MenuRpcConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: duanqiaona1
 * @Date: 2024/4/25 9:32 上午
 * @Description:
 */
@Service
@Slf4j
public class MenuRpcImpl implements MenuRpc {

    /**
     * Uim2菜单
     */
    @Autowired
    Uim2MenuFacade uim2MenuFacade;


    @Resource
    private HrUserService hrUserService;

    /**
     * 工具类
     */
    private HrUserClient hrUserClient;
    /**
     * uim配置信息
     */
    @Resource
    UimB2bConfig uimConfig;


    @Value("${com.jd.enterprise.api.client.hr.appCode}")
    private String appCode;

    @Value("${com.jd.enterprise.api.client.hr.tenantCode}")
    private String tenantCode;

    @Value("${com.jd.enterprise.api.client.hr.safetyKey}")
    private String safetyKey;

    @PostConstruct
    private void init() {
        new AsiaHrMdmHolder(tenantCode, appCode, safetyKey);
        this.hrUserClient = new HrUserClient(hrUserService);
    }


    @Override
    public List<Uim2MenuBO> getMenuTree(Uim2UserContext userContext) {
        try {
            Uim2UserRequest userRequest = MenuRpcConvert.ins.context2Uim2UserRequest(userContext, uimConfig);
            Uim2Response<List<Uim2MenuDto>> uim2Response = uim2MenuFacade.getMenuTree(userRequest);
            log.info("MenuRpcImpl -> getMenuTree  end,uim2Response:{}", JSON.toJSONString(uim2Response));
            if ("SUCCESS".equals(uim2Response.getCode())) {
                return MenuRpcConvert.ins.dto2Uim2MenuBOS(uim2Response.getData());
            }
            throw new ArgumentsException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
        } catch (Exception e) {
            log.error("MenuRpcImpl -> getMenuTree Exception", e);
            throw new ArgumentsException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
        }
    }

    /**
     * 根据ERP编码获取用户基本信息
     *
     * @param erpCode
     * @return
     */
    @Override
    public UserBO getUserInfo(String erpCode) {
        try {
            UserVo userVo = this.hrUserClient.getUserBaseInfoByUserNameG11n(erpCode);
            if (userVo != null && StringUtils.isNotBlank(userVo.getUserName())) {
                return MenuRpcConvert.ins.vo2UserBO(userVo);
            }
        } catch (Exception e) {
            log.error("查询用户信息异常: erp: {}, error: {}", erpCode, e.getMessage(), e);
            throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
        }
        return null;
    }
}
