package com.jdh.o2oservice.b2b.infrastructure.factory;

import com.global.service.id.GlobalServiceID;
import com.jdh.o2oservice.b2b.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.b2b.base.enums.EnvEnum;
import com.jdh.o2oservice.b2b.base.exception.SystemErrorCode;
import com.jdh.o2oservice.b2b.base.exception.SystemException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.Queue;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * 全局唯一ID工厂
 * @author: yangxiyu
 * @date: 2022/12/9 9:17 上午
 * @version: 1.0
 */
@Component("factoryFailBack")
@Slf4j
public class GenerateIdFactoryFailBack {
    /**
     * 全局唯一报告表
     */
    public static final int  EXAMINATIONID= 2;

    /**
     * 当前配置
     */
    private static String ACTIVE;

    /**
     * 初始注入maven环境
     */
    @Value("${spring.profiles.active}")
    public void setActive(String value){
        ACTIVE = value;
    }

    /**
     * globalServiceID
     */
    @Resource
    private GlobalServiceID globalServiceIDFailBack;


    /**
     * @return
     * @throws Exception
     */
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.factory.GenerateIdFactoryFailBack.getIdStr")
    public String getIdStr() {
        try {
            if (EnvEnum.DEV_TEST.getCode().equals(ACTIVE)){
                return ""+System.currentTimeMillis();
            }
            long id = globalServiceIDFailBack.getShortId();
            return ""+id;
        }catch (Exception e){
            log.error("GenerateIdService->getId is fail", e);
            throw new SystemException(SystemErrorCode.GENERATE_ID_PRODUCTION_ERROR);
        }
    }

    /**
     * @return
     */
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.factory.GenerateIdFactoryFailBack.getId")
    public Long getId() {
        try {
            if (EnvEnum.DEV_TEST.getCode().equals(ACTIVE)){
                return System.currentTimeMillis();
            }
            return globalServiceIDFailBack.getShortId();
        }catch (Exception e){
            log.error("GenerateIdService->getId is fail", e);
            throw new SystemException(SystemErrorCode.GENERATE_ID_PRODUCTION_ERROR);
        }
    }

    /**
     *
     * @param requiredNum
     * @return
     */
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.factory.GenerateIdFactoryFailBack.getBatchId")
    public Queue<Long> getBatchId(int requiredNum) {
        try {
            if (EnvEnum.DEV_TEST.getCode().equals(ACTIVE)){
                Queue<Long> res = new LinkedBlockingQueue<>(requiredNum);
                for (int i = 0; i < requiredNum; i++){
                    res.add(System.currentTimeMillis());
                }
                return res;
            }
            // getBatchShortId 最多返回50个
            LinkedBlockingDeque<Long> ids = new LinkedBlockingDeque<>();
            if(requiredNum > 50){
                int times = requiredNum/50;
                if ((requiredNum % 50) != 0){
                    times += 1;
                }
                for (int i = 0; i < times; i++){
                    ids.addAll(globalServiceIDFailBack.getBatchShortId(50));
                }
            }else{
                ids.addAll(globalServiceIDFailBack.getBatchShortId(requiredNum));

            }
            return ids;
        }catch (Exception e){
            log.error("GenerateIdService->getBatchId 生产serviceId 失败", e);
            throw new SystemException(SystemErrorCode.GENERATE_ID_PRODUCTION_ERROR);
        }
    }

    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.factory.GenerateIdFactoryFailBack.getReportId")
    public Long getReportId() {
        try {
            if (EnvEnum.DEV_TEST.getCode().equals(ACTIVE)){
                return System.currentTimeMillis();
            }
            return globalServiceIDFailBack.getId(EXAMINATIONID);
        }catch (Exception e){
            log.error("GenerateIdService->getId is fail", e);
            throw new SystemException(SystemErrorCode.GENERATE_ID_PRODUCTION_ERROR);
        }
    }
}
