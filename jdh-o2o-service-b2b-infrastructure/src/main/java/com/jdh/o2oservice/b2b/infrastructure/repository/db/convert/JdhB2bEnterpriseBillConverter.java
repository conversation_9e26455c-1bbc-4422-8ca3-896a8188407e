package com.jdh.o2oservice.b2b.infrastructure.repository.db.convert;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.b2b.base.enums.YnStatusEnum;
import com.jdh.o2oservice.b2b.domain.enterprisebill.context.B2bEnterpriseBillQueryContext;
import com.jdh.o2oservice.b2b.domain.enterprisebill.model.JdhB2bEnterpriseBill;
import com.jdh.o2oservice.b2b.domain.enterprisebill.model.JdhB2bEnterpriseBillDetail;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.po.JdhB2bEnterpriseBillDetailPo;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.po.JdhB2bEnterpriseBillPo;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Objects;

/**
 * @ClassName:JdhB2bEnterpriseBillConverter
 * @Description:B2b企业账单Converter
 * @Author: liwenming
 * @Date: 2025/2/25 15:14
 * @Vserion: 1.0
 **/
@Mapper
public interface JdhB2bEnterpriseBillConverter {

    JdhB2bEnterpriseBillConverter INSTANCE = Mappers.getMapper(JdhB2bEnterpriseBillConverter.class);

    JdhB2bEnterpriseBillPo convertToPo(JdhB2bEnterpriseBill jdhB2bEnterpriseBill);

    JdhB2bEnterpriseBill convertToEntity(JdhB2bEnterpriseBillPo jdhB2bEnterpriseBillPo);

    List<JdhB2bEnterpriseBill> convertToEntityList(List<JdhB2bEnterpriseBillPo> jdhB2bEnterpriseBillPos);

    Page<JdhB2bEnterpriseBill> dao2JdhB2bEnterpriseBillPage(IPage<JdhB2bEnterpriseBillPo> page);

    Page<JdhB2bEnterpriseBillDetail> dao2JdhB2bEnterpriseBillDetailPage(IPage<JdhB2bEnterpriseBillDetailPo> page);

    JdhB2bEnterpriseBillDetailPo convertToDetailPo(JdhB2bEnterpriseBillDetail jdhB2bEnterpriseBillDetail);

    /**
     *
     * @param queryContext
     * @return
     */
    @Named("getQueryWrapper")
    default LambdaQueryWrapper<JdhB2bEnterpriseBillPo> getQueryWrapper(B2bEnterpriseBillQueryContext queryContext) {
        LambdaQueryWrapper<JdhB2bEnterpriseBillPo> queryWrapper = new LambdaQueryWrapper<>();
        if (Objects.nonNull(queryContext.getEnterpriseId())) {
            queryWrapper.eq(JdhB2bEnterpriseBillPo::getEnterpriseId, queryContext.getEnterpriseId());
        }
        if (StringUtils.isNoneBlank(queryContext.getBillDate())) {
            queryWrapper.eq(JdhB2bEnterpriseBillPo::getBillDate, queryContext.getBillDate());
        }
        queryWrapper.eq(JdhB2bEnterpriseBillPo::getYn, YnStatusEnum.YES.getCode());
        queryWrapper.orderByDesc(JdhB2bEnterpriseBillPo::getId);
        return queryWrapper;
    }


    /**
     *
     * @param queryContext
     * @return
     */
    @Named("getQueryDetailWrapper")
    default LambdaQueryWrapper<JdhB2bEnterpriseBillDetailPo> getQueryDetailWrapper(B2bEnterpriseBillQueryContext queryContext) {
        LambdaQueryWrapper<JdhB2bEnterpriseBillDetailPo> queryWrapper = new LambdaQueryWrapper<>();
        if (Objects.nonNull(queryContext.getEnterpriseId())) {
            queryWrapper.eq(JdhB2bEnterpriseBillDetailPo::getEnterpriseId, queryContext.getEnterpriseId());
        }
        if (Objects.nonNull(queryContext.getBillId())) {
            queryWrapper.eq(JdhB2bEnterpriseBillDetailPo::getBillId, queryContext.getBillId());
        }
        if (CollUtil.isNotEmpty(queryContext.getPromiseStatusSet())) {
            queryWrapper.in(JdhB2bEnterpriseBillDetailPo::getStatus, queryContext.getPromiseStatusSet());
        }
        if (CollUtil.isNotEmpty(queryContext.getEnterpriseSkuIdSet())) {
            queryWrapper.in(JdhB2bEnterpriseBillDetailPo::getSkuName, queryContext.getEnterpriseSkuIdSet());
        }
        if (Objects.nonNull(queryContext.getPromiseId())) {
            queryWrapper.eq(JdhB2bEnterpriseBillDetailPo::getPromiseId, queryContext.getPromiseId());
        }

        if (Objects.nonNull(queryContext.getOrderTimeStart())) {
            queryWrapper.gt(JdhB2bEnterpriseBillDetailPo::getOrderTime, queryContext.getOrderTimeStart());
        }
        if (Objects.nonNull(queryContext.getOrderTimeEnd())) {
            queryWrapper.lt(JdhB2bEnterpriseBillDetailPo::getOrderTime, queryContext.getOrderTimeEnd());
        }
        if (Objects.nonNull(queryContext.getEnterpriseVoucherId())) {
            queryWrapper.eq(JdhB2bEnterpriseBillDetailPo::getEnterpriseVoucherId, queryContext.getEnterpriseVoucherId());
        }
        queryWrapper.eq(JdhB2bEnterpriseBillDetailPo::getYn, YnStatusEnum.YES.getCode());
        queryWrapper.orderByDesc(JdhB2bEnterpriseBillDetailPo::getId);
        return queryWrapper;
    }
}
