package com.jdh.o2oservice.b2b.infrastructure.rpc;
import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.b2b.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.b2b.base.exception.BusinessErrorCode;
import com.jdh.o2oservice.b2b.base.exception.BusinessException;
import com.jdh.o2oservice.b2b.base.exception.DynamicErrorCode;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.O2oAngelWorkBo;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.O2oAngelWorkQueryBo;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.rpc.O2oServiceAngelWorkRpc;
import com.jdh.o2oservice.b2b.infrastructure.rpc.converter.O2oServiceAngelWorkRpcConvert;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.angelpromise.AngelWorkReadGwExport;
import com.jdh.o2oservice.export.angelpromise.dto.AngelWorkDto;
import com.jdh.o2oservice.export.angelpromise.query.AngelWorkQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class O2oServiceAngelWorkRpcImpl implements O2oServiceAngelWorkRpc {

    @Resource
    private AngelWorkReadGwExport angelWorkReadGwExport;

    /**
     * 查询工单列表
     * @param bo
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.infrastructure.rpc.O2oServiceAngelWorkRpcImpl.getAngelWorkList")
    public List<O2oAngelWorkBo> getAngelWorkList(O2oAngelWorkQueryBo bo) {
        try {
            AngelWorkQuery angelWorkQuery = AngelWorkQuery.builder()
                    .promiseIdList(bo.getPromiseIdList())
                    .build();
            log.info("O2oServiceAngelWorkRpcImpl -> getAngelWorkList angelWorkQuery:{}", JSON.toJSONString(angelWorkQuery));
            Response<List<AngelWorkDto>> response = angelWorkReadGwExport.getAngelWorkList(angelWorkQuery);
            log.info("O2oServiceAngelWorkRpcImpl -> getAngelWorkList response:{}", JSON.toJSONString(response));
            if(Objects.isNull(response)) {
                throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
            }
            if(Boolean.FALSE.equals(response.isSuccess())){
                throw new BusinessException(new DynamicErrorCode(response.getCode(), response.getMsg()));
            }
            return O2oServiceAngelWorkRpcConvert.INS.toO2oAngelWorkBo(response.getData());
        } catch (BusinessException be) {
            log.error("O2oServiceAngelWorkRpcImpl -> getAngelWorkList business exception", be);
            throw new BusinessException(be.getErrorCode());
        } catch (Exception e) {
            log.error("O2oServiceAngelWorkRpcImpl -> getAngelWorkList exception", e);
            throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
        }
    }
}
