package com.jdh.o2oservice.b2b.infrastructure.rpc;

import cn.hutool.json.JSONUtil;
import com.jdh.o2oservice.b2b.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.AngelServiceRecordBBo;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.O2oAngelServiceRecordBo;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.rpc.O2oAngelServiceRecordRpc;
import com.jdh.o2oservice.b2b.infrastructure.rpc.converter.AngelServiceRecordRpcConvert;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.angelpromise.AngelServiceRecordJsfExport;
import com.jdh.o2oservice.export.angelpromise.dto.AngelServiceRecordBDto;
import com.jdh.o2oservice.export.ztools.cmd.QueryAngelServiceRecordFlowCmd;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2025/9/19
 */
@Component
@Slf4j
public class O2oAngelServiceRecordRpcImpl implements O2oAngelServiceRecordRpc {

    /**
     * 注入的AngelServiceRecordJsfExport对象
     */
    @Resource
    private AngelServiceRecordJsfExport angelServiceRecordJsfExport;

    /**
     * B端查询服务记录
     * @param bo
     * @return
     */
    @Override
    @LogAndAlarm
    public List<AngelServiceRecordBBo> queryAngelServiceBRecordFlow(O2oAngelServiceRecordBo bo) {
        QueryAngelServiceRecordFlowCmd cmd = new QueryAngelServiceRecordFlowCmd();
        cmd.setPromiseId(bo.getPromiseId());
        Response<List<AngelServiceRecordBDto>> response = angelServiceRecordJsfExport.queryAngelServiceBRecordFlow(cmd);
        log.info("O2oAngelServiceRecordRpcImpl->queryAngelServiceBRecordFlow,response={}", JSONUtil.toJsonStr(response));
        if (Objects.nonNull(response) && response.isSuccess()){
            return AngelServiceRecordRpcConvert.INSTANCE.convert(response.getData());
        }
        //TODO 是否需要报错

        return Collections.emptyList();
    }
}
