package com.jdh.o2oservice.b2b.infrastructure.repository.db.po;

import lombok.Data;

import java.util.Date;

/**
* @description JdhBusinessModePo简介
* <AUTHOR>
* @date 2023-12-22 10:35:07
*/
@Data
public class JdhBasicTimePo {



    /**
     * 是否有效 0：无效；1：有效
     */
    private Integer yn;



    /**
     * 创建时间
     */
    private Date createTime;


    /**
     * 更新时间
     */
    private Date updateTime;
}