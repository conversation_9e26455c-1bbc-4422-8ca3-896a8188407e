package com.jdh.o2oservice.b2b.infrastructure.repository.db.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jdh.o2oservice.b2b.base.mybatisplus.AcesCipherIndexHandler;
import com.jdh.o2oservice.b2b.base.mybatisplus.AcesCipherTextHandler;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


/**
 * JdhB2bEnterpriseVoucherPo
 *
 * <AUTHOR>
 * @date 2025/02/26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "jdh_b2b_enterprise_voucher",autoResultMap = true)
public class JdhB2bEnterpriseVoucherPo extends JdhBasicPo{

    /**
    * 主键
    */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
    * 企业ID
    */
    private Long enterpriseId;

    /**
    * 企业服务单id
    */
    private Long enterpriseVoucherId;

    /**
    * 服务单id
    */
    private Long voucherId;

    /**
    * 履约单id
    */
    private Long promiseId;

    /**
    * 企业服务skuId
    */
    private Long enterpriseSkuId;

    /**
    * skuId
    */
    //private Long skuId;

    /**
    * sku名称
    */
    //private String skuName;

    /**
    * sku短名称
    */
    //private String skuShortName;

    /**
    * 履约单状态：待接单/已接单/护士已出发/开始服务/服务完成/已取消
    */
    private Integer promiseStatus;

    /**
    * 用户pin
    */
    private String customerUserPin;

    /**
    * 企业用户id
    */
    private Long enterpriseUserId;

    /**
    * 用户名称
    */
    //private String enterpriseUserName;

    /**
     * 预约人电话
     */
/*    @TableField(typeHandler = AcesCipherTextHandler.class)
    private String appointmentPhone;*/

    /**
     * 预约人电话 - index
     */
/*    @TableField(typeHandler = AcesCipherIndexHandler.class)
    private String appointmentPhoneIndex;*/

    /**
     * 预约人姓名
     */
/*    @TableField(typeHandler = AcesCipherTextHandler.class)
    private String appointmentName;*/

    /**
     * 预约人姓名 - index
     */
/*    @TableField(typeHandler = AcesCipherIndexHandler.class)
    private String appointmentNameIndex;*/

    /**
     * 卡号
     */
    //private String code;

    /**
    * erp_pin
    */
    private String erpPin;

    /**
    * 源订单号
    */
    private String sourceOrderId;

    /**
    * 源订单平台
    */
    private String sourceOrderPlatform;

    /**
    * 扩展信息
    */
    private String extend;

    /**
     * 是否统计 1有效 0无效
     */
    private Integer statistics;

    /**
     * 金额
     */
    private BigDecimal totalAmount;
}