package com.jdh.o2oservice.b2b.infrastructure.repository.db.convert;
import com.jdh.o2oservice.b2b.domain.enterprisesku.model.JdhB2bEnterpriseSku;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.po.JdhB2bEnterpriseSkuPo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Description JdhB2bEnterpriseSkuPoConverter
 * @Date 2025/2/25 上午11:06
 * <AUTHOR>
 **/
@Mapper
public interface JdhB2bEnterpriseSkuPoConverter {

    JdhB2bEnterpriseSkuPoConverter INSTANCE = Mappers.getMapper(JdhB2bEnterpriseSkuPoConverter.class);

    JdhB2bEnterpriseSku po2Entity(JdhB2bEnterpriseSkuPo jdhB2bEnterpriseSkuPo);

    List<JdhB2bEnterpriseSku> po2Entity(List<JdhB2bEnterpriseSkuPo> list);
}
