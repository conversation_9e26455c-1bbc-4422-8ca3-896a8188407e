package com.jdh.o2oservice.b2b.infrastructure.repository.db.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jdh.o2oservice.b2b.base.model.AggregateCode;
import com.jdh.o2oservice.b2b.base.model.DomainCode;
import com.jdh.o2oservice.b2b.domain.enterpriseaccount.repository.model.JdhEnterpriseAccountIdentifier;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;


/**
 * JdhB2bEnterpriseAccountDetailPo
 *
 * <AUTHOR>
 * @date 2025/03/07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "jdh_b2b_enterprise_account_detail",autoResultMap = true)
public class JdhB2bEnterpriseAccountDetailPo extends JdhBasicPo{

    /**
    * 主键
    */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
    * 企业ID
    */
    private Long enterpriseId;
    /**
     * 账户Id
     */
    private Long accountId;
    /**
     * 账户明细Id
     */
    private Long accountDetailId;
    /**
     * 来源单据类型：1:voucher, 2:账单打款
     */
    private Integer sourceReceiptType;
    /**
     * 来源单据id
     */
    private Long sourceReceiptId;
    /**
     * 冻结金额
     */
    private BigDecimal freezeAmount;
    /**
     * 类型：1-冻结 2-释放
     */
    private Integer freezeType;

    /**
     * 扩展信息
     */
    private String extend;

    /**
     * 是否有效 1有效 0无效
     */
    private Integer yn;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 数据来源分支
     */
    private String branch;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 更新时间
     */
    private Date updateTime;

}