package com.jdh.o2oservice.b2b.infrastructure.repository.db.convert;

import com.jdh.o2oservice.b2b.domain.enterpriseremark.model.EnterpriseRemark;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.po.JdhB2bEnterpriseRemarkPo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 企业备注PO转换器
 *
 * <AUTHOR>
 * @date 2025/03/10
 */
@Mapper
public interface JdhB2bEnterpriseRemarkPoConverter {

    /**
     * 实例
     */
    JdhB2bEnterpriseRemarkPoConverter INSTANCE = Mappers.getMapper(JdhB2bEnterpriseRemarkPoConverter.class);

    /**
     * PO转领域模型
     *
     * @param remarkPo PO对象
     * @return 领域模型
     */
    EnterpriseRemark po2Entity(JdhB2bEnterpriseRemarkPo remarkPo);

    /**
     * PO列表转领域模型列表
     *
     * @param remarkPos PO对象列表
     * @return 领域模型列表
     */
    List<EnterpriseRemark> po2EntityList(List<JdhB2bEnterpriseRemarkPo> remarkPos);

    /**
     * 领域模型转PO
     *
     * @param enterpriseRemark 领域模型
     * @return PO对象
     */
    JdhB2bEnterpriseRemarkPo entity2Po(EnterpriseRemark enterpriseRemark);
}
