package com.jdh.o2oservice.b2b.infrastructure.rpc;

import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.b2b.base.exception.BusinessErrorCode;
import com.jdh.o2oservice.b2b.base.exception.BusinessException;
import com.jdh.o2oservice.b2b.base.exception.DynamicErrorCode;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.IntendedAngelBo;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.ctx.QueryIntendedAngelCtx;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.rpc.O2oServiceTradeRpc;
import com.jdh.o2oservice.b2b.infrastructure.rpc.converter.O2oServiceTradeRpcConvert;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.trade.TradeJsfExport;
import com.jdh.o2oservice.export.trade.dto.IntendedAngelDTO;
import com.jdh.o2oservice.export.trade.query.QueryIntendedAngelParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * O2oServicePromiseRpcImpl
 *
 * <AUTHOR>
 * @date 2025/02/27
 */
@Slf4j
@Service
public class O2oServiceTradeRpcImpl implements O2oServiceTradeRpc {

    /**
     * tradeJsfExport
     */
    @Resource
    private TradeJsfExport tradeJsfExport;

    @Override
    public IntendedAngelBo queryIntendedAngel(QueryIntendedAngelCtx ctx) {
        try {
            QueryIntendedAngelParam queryIntendedAngelParam = O2oServiceTradeRpcConvert.INS.ctx2QueryIntendedAngelParam(ctx);
            log.info("O2oServiceTradeRpcImpl -> queryIntendedAngel queryIntendedAngelParam:{}", JSON.toJSONString(queryIntendedAngelParam));
            Response<IntendedAngelDTO> response = tradeJsfExport.queryIntendedAngel(queryIntendedAngelParam);
            log.info("O2oServiceTradeRpcImpl -> queryIntendedAngel response:{}", JSON.toJSONString(response));
            if (Objects.isNull(response)) {
                throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
            }
            if (Boolean.FALSE.equals(response.isSuccess())) {
                throw new BusinessException(new DynamicErrorCode(response.getCode(), response.getMsg()));
            }
            return O2oServiceTradeRpcConvert.INS.toIntendedAngelBo(response.getData());
        } catch (BusinessException be) {
            log.error("O2oServiceTradeRpcImpl -> queryIntendedAngel business exception", be);
            throw new BusinessException(be.getErrorCode());
        } catch (Exception e) {
            log.error("O2oServiceTradeRpcImpl -> queryIntendedAngel exception", e);
            throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
        }
    }
}
