package com.jdh.o2oservice.b2b.infrastructure.repository.db;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.cmd.EnterpriseVoucherCmd;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.po.JdhB2bEnterpriseSkuPo;
import org.apache.commons.lang3.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.b2b.base.enums.YnStatusEnum;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.model.EnterpriseVoucher;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.query.EnterpriseVoucherPageQuery;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.query.EnterpriseVoucherQuery;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.repository.EnterpriseVoucherRepository;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.convert.JdhB2bEnterpriseVoucherPoConverter;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.mapper.JdhB2bEnterpriseVoucherMapper;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.po.JdhB2bEnterpriseVoucherPo;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * EnterpriseVoucherRepositoryImpl
 *
 * <AUTHOR>
 * @date 2025/02/27
 */
@Repository
public class EnterpriseVoucherRepositoryImpl implements EnterpriseVoucherRepository {

    /**
     * jdhB2bEnterpriseVoucherMapper
     */
    @Autowired
    private JdhB2bEnterpriseVoucherMapper jdhB2bEnterpriseVoucherMapper;


    /**
     * save
     *
     * @param enterpriseVoucher enterpriseVoucher
     * @return {@link Integer }
     */
    @Override
    public Integer save(EnterpriseVoucher enterpriseVoucher) {
        JdhB2bEnterpriseVoucherPo po = JdhB2bEnterpriseVoucherPoConverter.INSTANCE.entity2Po(enterpriseVoucher);
        if(Objects.nonNull(po.getId())){
            //更新
            LambdaUpdateWrapper<JdhB2bEnterpriseVoucherPo> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.eq(JdhB2bEnterpriseVoucherPo::getEnterpriseVoucherId, po.getEnterpriseVoucherId());
            return jdhB2bEnterpriseVoucherMapper.update(po,updateWrapper);
        }else {
            //插入
            return jdhB2bEnterpriseVoucherMapper.insert(po);
        }
    }

    /**
     * queryEnterpriseVoucher
     *
     * @param query query
     * @return {@link EnterpriseVoucher }
     */
    @Override
    public EnterpriseVoucher queryEnterpriseVoucher(EnterpriseVoucherQuery query) {
        LambdaQueryWrapper<JdhB2bEnterpriseVoucherPo> queryMapper = Wrappers.lambdaQuery();
        queryMapper.eq(Objects.nonNull(query.getEnterpriseVoucherId()),JdhB2bEnterpriseVoucherPo::getEnterpriseVoucherId, query.getEnterpriseVoucherId());
        queryMapper.eq(Objects.nonNull(query.getPromiseId()),JdhB2bEnterpriseVoucherPo::getPromiseId, query.getPromiseId());
        queryMapper.eq(Objects.nonNull(query.getVoucherId()),JdhB2bEnterpriseVoucherPo::getVoucherId, query.getVoucherId());
        queryMapper.eq(Objects.nonNull(query.getEnterpriseId()),JdhB2bEnterpriseVoucherPo::getEnterpriseId, query.getEnterpriseId());
        if (CollectionUtils.isEmpty(queryMapper.getExpression().getNormal())){
            return null;
        }
        queryMapper.eq(JdhB2bEnterpriseVoucherPo::getYn, YnStatusEnum.YES.getCode());
        JdhB2bEnterpriseVoucherPo voucherPo = jdhB2bEnterpriseVoucherMapper.selectOne(queryMapper);
        return JdhB2bEnterpriseVoucherPoConverter.INSTANCE.po2Entity(voucherPo);
    }

    /**
     * queryEnterpriseVoucherPage
     *
     * @param query 查询
     * @return {@link Page }<{@link EnterpriseVoucher }>
     */
    @Override
    public Page<EnterpriseVoucher> queryEnterpriseVoucherPage(EnterpriseVoucherPageQuery query) {
        Page<JdhB2bEnterpriseVoucherPo> page = new Page<>(query.getPageNum(), query.getPageSize());
        LambdaQueryWrapper<JdhB2bEnterpriseVoucherPo> queryMapper = Wrappers.lambdaQuery();
        queryMapper.eq(Objects.nonNull(query.getEnterpriseVoucherId()),JdhB2bEnterpriseVoucherPo::getEnterpriseVoucherId, query.getEnterpriseVoucherId());
        queryMapper.eq(Objects.nonNull(query.getPromiseId()),JdhB2bEnterpriseVoucherPo::getPromiseId, query.getPromiseId());
        queryMapper.in(CollectionUtils.isNotEmpty(query.getEnterpriseVoucherIdList()),JdhB2bEnterpriseVoucherPo::getEnterpriseVoucherId, query.getEnterpriseVoucherIdList());
        queryMapper.in(CollectionUtils.isNotEmpty(query.getEnterpriseSkuIdSet()),JdhB2bEnterpriseVoucherPo::getEnterpriseSkuId, query.getEnterpriseSkuIdSet());
        queryMapper.in(CollectionUtils.isNotEmpty(query.getEnterpriseIdList()),JdhB2bEnterpriseVoucherPo::getEnterpriseId, query.getEnterpriseIdList());
        queryMapper.eq(JdhB2bEnterpriseVoucherPo::getYn, YnStatusEnum.YES.getCode());
        queryMapper.between(Objects.nonNull(query.getCreateTimeStart()) && Objects.nonNull(query.getCreateTimeEnd()),JdhB2bEnterpriseVoucherPo::getCreateTime, query.getCreateTimeStart(), query.getCreateTimeEnd());
        queryMapper.in(CollUtil.isNotEmpty(query.getPromiseStatusSet()),JdhB2bEnterpriseVoucherPo::getPromiseStatus, query.getPromiseStatusSet());
        queryMapper.orderByDesc(JdhB2bEnterpriseVoucherPo::getCreateTime);

        Page<JdhB2bEnterpriseVoucherPo> voucherPoPage = jdhB2bEnterpriseVoucherMapper.selectPage(page, queryMapper);

        Page<EnterpriseVoucher> returnPage = new Page<>();
        returnPage.setTotal(voucherPoPage.getTotal());
        returnPage.setCurrent(voucherPoPage.getCurrent());
        returnPage.setSize(voucherPoPage.getSize());
        returnPage.setPages(voucherPoPage.getPages());
        returnPage.setRecords(JdhB2bEnterpriseVoucherPoConverter.INSTANCE.po2EntityList(voucherPoPage.getRecords()));
        return returnPage;
    }

    /**
     * queryEnterpriseVoucherPage
     *
     * @param query 查询
     * @return {@link Page }<{@link EnterpriseVoucher }>
     */
    @Override
    public List<EnterpriseVoucher> queryEnterpriseVoucherList(EnterpriseVoucherPageQuery query) {
        LambdaQueryWrapper<JdhB2bEnterpriseVoucherPo> queryMapper = Wrappers.lambdaQuery();
        queryMapper.in(CollUtil.isNotEmpty(query.getEnterpriseVoucherIdList()),JdhB2bEnterpriseVoucherPo::getEnterpriseVoucherId, query.getEnterpriseVoucherIdList());
        queryMapper.in(CollUtil.isNotEmpty(query.getPromiseStatusList()),JdhB2bEnterpriseVoucherPo::getPromiseStatus, query.getPromiseStatusList());
        queryMapper.eq(Objects.nonNull(query.getEnterpriseId()),JdhB2bEnterpriseVoucherPo::getEnterpriseId, query.getEnterpriseId());
        queryMapper.ge(StringUtils.isNoneBlank(query.getLastMonthFirstDay()),JdhB2bEnterpriseVoucherPo::getUpdateTime, query.getLastMonthFirstDay());
        queryMapper.lt(StringUtils.isNoneBlank(query.getLastMonthLastDay()),JdhB2bEnterpriseVoucherPo::getUpdateTime, query.getLastMonthLastDay());
        queryMapper.eq(Objects.nonNull(query.getStatistics()),JdhB2bEnterpriseVoucherPo::getStatistics, query.getStatistics());
        queryMapper.eq(JdhB2bEnterpriseVoucherPo::getYn, YnStatusEnum.YES.getCode());
        List<JdhB2bEnterpriseVoucherPo> voucherPos = jdhB2bEnterpriseVoucherMapper.selectList(queryMapper);
        return JdhB2bEnterpriseVoucherPoConverter.INSTANCE.po2EntityList(voucherPos);
    }

    /**
     * updateVoucherStatistics
     *
     * @param enterpriseVoucherCmd
     * @return {@link Integer }
     */
    @Override
    public Integer updateVoucherStatistics(EnterpriseVoucherCmd enterpriseVoucherCmd) {
        LambdaUpdateWrapper<JdhB2bEnterpriseVoucherPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(JdhB2bEnterpriseVoucherPo::getStatistics, 1)
                .setSql("`version` = version+1")
                .in(JdhB2bEnterpriseVoucherPo::getEnterpriseVoucherId, enterpriseVoucherCmd.getEnterpriseVoucherIdList())
                .eq(JdhB2bEnterpriseVoucherPo::getStatistics, 0);
        return jdhB2bEnterpriseVoucherMapper.update(null,updateWrapper);
    }
}
