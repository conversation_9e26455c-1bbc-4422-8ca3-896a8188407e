package com.jdh.o2oservice.b2b.infrastructure.repository.es.convert;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.parser.Feature;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * <AUTHOR> limeng363
 * @Date : 2024-05-07 18:43
 * @Desc :
 */
@Slf4j
public  final class EsBeanUtils {

    /**
     * @param map
     * @param beanClass
     * @return
     */
    public static Object makeMapToObject(Map<String, Object> map, Class beanClass) throws Exception {
        Object obj = null;
        if (map == null) {
            return obj;
        }
        try {
            obj = beanClass.newInstance();

            org.apache.commons.beanutils.BeanUtils.populate(obj, map);
        }catch (Exception e){
            log.error("EsBeanUtils.makeMapToObject", e);
            throw new Exception(String.format("BeanUtils.makeMapToObject.is.error:%s", null!=obj?obj.getClass():"beanClass is null"));
        }
        return obj;
    }

    /**
     * 将对象转为MAP
     *
     * @param obj
     * @return
     */
    public static Map<String, Object> makeObjectToMap(Object obj) throws Exception{
        Map<String, Object> result = null;
        if(obj == null) {
            return result;
        }
        try {
            TypeReference<Map<String, Object>> reference = new TypeReference<Map<String, Object>>() {
            };
            int disableDecimalFeature = JSON.DEFAULT_PARSER_FEATURE & ~Feature.UseBigDecimal.getMask();
            result = JSON.parseObject(JSON.toJSONString(obj), reference.getType(), disableDecimalFeature, Feature.OrderedField);
        }catch (Exception e){
            log.error("EsBeanUtils.makeObjectToMap", e);
            throw new Exception(String.format("BeanUtils.makeObjectToMap.is.error:%s",obj.getClass()));
        }
        return result;
    }


    /**
     * 将引用对象转为Json 字符串
     * @param parameter
     * @return
     */
    public static String makeObjectToJson(Object parameter)throws Exception{
        String result = null;
        if(null == parameter){
            throw new Exception("EsClientFactory.makeObjectToJson.parameter.is.null");
        }
        try{
            result = JSON.toJSONString(parameter);
        }catch (Exception e){
            log.error("EsBeanUtils.makeObjectToJson", e);
            throw new Exception("EsClientFactory.makeObjectToJson.is.system.error:parameter type error");
        }
        return result;
    }
}
