package com.jdh.o2oservice.b2b.infrastructure.repository.db;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.b2b.domain.enterprisebill.context.B2bEnterpriseBillQueryContext;
import com.jdh.o2oservice.b2b.domain.enterprisebill.model.JdhB2bEnterpriseBillDetail;
import com.jdh.o2oservice.b2b.domain.enterprisebill.repository.JdhB2bEnterpriseBillDetailRepository;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.convert.JdhB2bEnterpriseBillConverter;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.mapper.JdhB2bEnterpriseBillDetailMapper;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.po.JdhB2bEnterpriseBillDetailPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @ClassName:JdhB2bEnterpriseBillDetailRepositoryImpl
 * @Description: B2b企业账单明细
 * @Author: liwenming
 * @Date: 2025/2/24 15:14
 * @Vserion: 1.0
 **/
@Repository
@Slf4j
public class JdhB2bEnterpriseBillDetailRepositoryImpl implements JdhB2bEnterpriseBillDetailRepository {


    /** */
    @Autowired
    private JdhB2bEnterpriseBillDetailMapper jdhB2bEnterpriseBillDetailMapper;
    /**
     * 查询企业账单列表
     *
     * @param queryContext
     * @return
     */
    @Override
    public Page<JdhB2bEnterpriseBillDetail> queryEnterpriseBillDetailPage(B2bEnterpriseBillQueryContext queryContext) {
        log.info("JdhB2bEnterpriseBillDetailRepositoryImpl queryEnterpriseBillDetailPage queryContext={}", JSON.toJSONString(queryContext));
        Page<JdhB2bEnterpriseBillDetailPo> param = new Page<>(queryContext.getPageNum(), queryContext.getPageSize());
        LambdaQueryWrapper<JdhB2bEnterpriseBillDetailPo> queryWrapper = JdhB2bEnterpriseBillConverter.INSTANCE.getQueryDetailWrapper(queryContext);
        IPage<JdhB2bEnterpriseBillDetailPo> page = jdhB2bEnterpriseBillDetailMapper.selectPage(param, queryWrapper);
        return JdhB2bEnterpriseBillConverter.INSTANCE.dao2JdhB2bEnterpriseBillDetailPage(page);
    }

    /**
     * @param detailList
     * @return
     */
    @Override
    public Boolean batchSaveEnterpriseBillDetail(List<JdhB2bEnterpriseBillDetail> detailList) {
        detailList.forEach(billDetail -> {
            JdhB2bEnterpriseBillDetailPo entity = JdhB2bEnterpriseBillConverter.INSTANCE.convertToDetailPo(billDetail);
            jdhB2bEnterpriseBillDetailMapper.insert(entity);
        });
        return Boolean.TRUE;
    }


}
