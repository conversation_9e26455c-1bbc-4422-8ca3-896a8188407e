package com.jdh.o2oservice.b2b.infrastructure.repository.db;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.b2b.domain.enterprisebill.context.B2bEnterpriseBillQueryContext;
import com.jdh.o2oservice.b2b.domain.enterprisebill.model.JdhB2bEnterpriseBill;
import com.jdh.o2oservice.b2b.domain.enterprisebill.repository.JdhB2bEnterpriseBillRepository;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.convert.JdhB2bEnterpriseBillConverter;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.mapper.JdhB2bEnterpriseBillMapper;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.po.JdhB2bEnterpriseBillPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;

/**
 * @ClassName:JdhB2bEnterpriseBillRepository
 * @Description: B2b企业账单
 * @Author: liwenming
 * @Date: 2025/2/24 15:14
 * @Vserion: 1.0
 **/
@Repository
@Slf4j
public class JdhB2bEnterpriseBillRepositoryImpl implements JdhB2bEnterpriseBillRepository {

    /** */
    @Autowired
    private JdhB2bEnterpriseBillMapper jdhB2bEnterpriseBillMapper;


    /**
     * 查询企业账单
     *
     * @param queryContext
     * @return
     */
    @Override
    public JdhB2bEnterpriseBill queryEnterpriseBill(B2bEnterpriseBillQueryContext queryContext) {
        LambdaQueryWrapper<JdhB2bEnterpriseBillPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(JdhB2bEnterpriseBillPo::getBillId,queryContext.getBillId());
        JdhB2bEnterpriseBillPo jdhB2bEnterpriseBillPo = jdhB2bEnterpriseBillMapper.selectOne(queryWrapper);
        if(Objects.isNull(jdhB2bEnterpriseBillPo)){
            return null;
        }
        return JdhB2bEnterpriseBillConverter.INSTANCE.convertToEntity(jdhB2bEnterpriseBillPo);
    }

    /**
     * 查询企业账单列表
     * @param queryContext
     * @return
     */
    @Override
    public Page<JdhB2bEnterpriseBill> queryEnterpriseBillPage(B2bEnterpriseBillQueryContext queryContext) {
        log.info("JdhB2bEnterpriseBillRepositoryImpl queryEnterpriseBillPage queryContext={}", JSON.toJSONString(queryContext));
        Page<JdhB2bEnterpriseBillPo> param = new Page<>(queryContext.getPageNum(), queryContext.getPageSize());
        LambdaQueryWrapper<JdhB2bEnterpriseBillPo> queryWrapper = JdhB2bEnterpriseBillConverter.INSTANCE.getQueryWrapper(queryContext);
        IPage<JdhB2bEnterpriseBillPo> page = jdhB2bEnterpriseBillMapper.selectPage(param, queryWrapper);
        return JdhB2bEnterpriseBillConverter.INSTANCE.dao2JdhB2bEnterpriseBillPage(page);
    }

    /**
     * 查询企业某月账单
     *
     * @param queryContext
     * @return
     */
    @Override
    public JdhB2bEnterpriseBill queryEnterpriseBillByDate(B2bEnterpriseBillQueryContext queryContext) {
        LambdaQueryWrapper<JdhB2bEnterpriseBillPo> queryWrapper = JdhB2bEnterpriseBillConverter.INSTANCE.getQueryWrapper(queryContext);
        List<JdhB2bEnterpriseBillPo> list = jdhB2bEnterpriseBillMapper.selectList(queryWrapper);
        if(CollUtil.isEmpty(list)){
            return null;
        }
        JdhB2bEnterpriseBillPo jdhB2bEnterpriseBillPo = list.get(0);
        return JdhB2bEnterpriseBillConverter.INSTANCE.convertToEntity(jdhB2bEnterpriseBillPo);
    }

    /**
     * 更新企业账单
     *
     * @param jdhB2bEnterpriseBill
     * @return
     */
    @Override
    public Integer updateEnterpriseBill(JdhB2bEnterpriseBill jdhB2bEnterpriseBill) {
        LambdaUpdateWrapper<JdhB2bEnterpriseBillPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(Objects.nonNull(jdhB2bEnterpriseBill.getBillStatus()),JdhB2bEnterpriseBillPo::getBillStatus, jdhB2bEnterpriseBill.getBillStatus())
                .set(Objects.nonNull(jdhB2bEnterpriseBill.getAdjustAmount()),JdhB2bEnterpriseBillPo::getAdjustAmount, jdhB2bEnterpriseBill.getAdjustAmount())
                .set(StringUtils.isNoneBlank(jdhB2bEnterpriseBill.getAdjustDescribe()),JdhB2bEnterpriseBillPo::getAdjustDescribe, jdhB2bEnterpriseBill.getAdjustDescribe())
                .set(Objects.nonNull(jdhB2bEnterpriseBill.getFinalAmount()),JdhB2bEnterpriseBillPo::getFinalAmount, jdhB2bEnterpriseBill.getFinalAmount())
                .set(StringUtils.isNoneBlank(jdhB2bEnterpriseBill.getExtend()),JdhB2bEnterpriseBillPo::getExtend, jdhB2bEnterpriseBill.getExtend())
                .set(StringUtils.isNoneBlank(jdhB2bEnterpriseBill.getUpdateUser()),JdhB2bEnterpriseBillPo::getUpdateUser, jdhB2bEnterpriseBill.getUpdateUser())
                .eq(JdhB2bEnterpriseBillPo::getBillId, jdhB2bEnterpriseBill.getBillId());
        return jdhB2bEnterpriseBillMapper.update(null, updateWrapper);
    }

    /**
     * 保存企业账单
     *
     * @param jdhB2bEnterpriseBill
     * @return
     */
    @Override
    public Integer saveEnterpriseBill(JdhB2bEnterpriseBill jdhB2bEnterpriseBill) {
        JdhB2bEnterpriseBillPo jdhB2bEnterpriseBillPo = JdhB2bEnterpriseBillConverter.INSTANCE.convertToPo(jdhB2bEnterpriseBill);
        return jdhB2bEnterpriseBillMapper.insert(jdhB2bEnterpriseBillPo);
    }
}
