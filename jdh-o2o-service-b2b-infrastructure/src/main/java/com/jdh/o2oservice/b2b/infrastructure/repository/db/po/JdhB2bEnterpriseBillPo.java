package com.jdh.o2oservice.b2b.infrastructure.repository.db.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName:JdhB2bEnterpriseBillPo
 * @Description:B2b企业账单
 * @Author: liwenming
 * @Date: 2025/2/24 15:14
 * @Vserion: 1.0
 **/
@Data
@TableName(value = "jdh_b2b_enterprise_bill",autoResultMap = true)
public class JdhB2bEnterpriseBillPo {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 账单Id
     */
    private Long billId;
    /**
     * 企业Id
     */
    private Long enterpriseId;

    /**
     * 账单金额
     */
    private BigDecimal billAmount;

    /**
     * 调账金额
     */
    private BigDecimal adjustAmount;

    /**
     * 调账说明
     */
    private String adjustDescribe;

    /**
     * 最终金额
     */
    private BigDecimal finalAmount;

    /**
     * 账单状态 1-待确认 2-已确认 3-已打款 4-已到账 5-已作废
     */
    private Integer billStatus;
    /**
     * 账期
     */
    private String billDate;
    /**
     * 扩展信息
     */
    private String extend;

    /**
     * 版本号
     */
    private Integer version;
    /**
     * 数据来源分支
     */
    private String branch;

    /**
     * 是否有效 1有效 0 无效
     */
    private Integer yn;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改人
     */
    private String updateUser;
    /**
     * 修改时间
     */
    private Date updateTime;
}
