package com.jdh.o2oservice.b2b.infrastructure.rpc.converter;

import com.jd.enterprise.api.vo.UserVo;
import com.jd.uim2.facade.request.user.Uim2UserRequest;
import com.jd.uim2.facade.response.menu.Uim2MenuDto;
import com.jdh.o2oservice.b2b.common.config.UimB2bConfig;
import com.jdh.o2oservice.b2b.domain.support.user.uim.bo.Uim2MenuBO;
import com.jdh.o2oservice.b2b.domain.support.user.uim.bo.UserBO;
import com.jdh.o2oservice.b2b.domain.support.user.uim.param.Uim2UserContext;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Author: duanqiaona1
 * @Date: 2024/4/25 9:58 上午
 * @Description:
 */
@Mapper
public interface MenuRpcConvert {
    MenuRpcConvert ins = Mappers.getMapper(MenuRpcConvert.class);


    @Mapping(target = "appKey", source = "uimConfig.appKey")
    @Mapping(target = "appToken", source = "uimConfig.appToken")
    @Mapping(target = "tenantCode", source = "uimConfig.tenantCode")
    Uim2UserRequest context2Uim2UserRequest(Uim2UserContext userContext, UimB2bConfig uimConfig);

    List<Uim2MenuBO> dto2Uim2MenuBOS(List<Uim2MenuDto> menuDtos);


    @Mapping(target = "pin", source = "userVo.userName")
    @Mapping(target = "nick", source = "userVo.realName")
    @Mapping(target = "headImg", source = "userVo.headImg")
    UserBO vo2UserBO(UserVo userVo);
}
