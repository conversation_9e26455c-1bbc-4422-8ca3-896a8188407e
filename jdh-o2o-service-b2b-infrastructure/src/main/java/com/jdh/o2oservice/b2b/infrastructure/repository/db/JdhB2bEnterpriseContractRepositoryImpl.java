package com.jdh.o2oservice.b2b.infrastructure.repository.db;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.b2b.base.enums.YnStatusEnum;
import com.jdh.o2oservice.b2b.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.b2b.domain.enterprise.model.JdhB2bEnterpriseContract;
import com.jdh.o2oservice.b2b.domain.enterprise.repository.JdhB2bEnterpriseContractRepository;
import com.jdh.o2oservice.b2b.domain.enterprise.repository.query.JdhB2bEnterpriseContractQuery;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.convert.JdhB2bEnterpriseContractConverter;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.convert.JdhBasicPoConverter;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.mapper.JdhB2bEnterpriseContractMapper;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.po.JdhB2bEnterpriseContractPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @Description 企业合同
 * @Date 2025/3/11 下午6:27
 * <AUTHOR>
@Repository
@Slf4j
public class JdhB2bEnterpriseContractRepositoryImpl implements JdhB2bEnterpriseContractRepository {

    /** */
    @Resource
    private JdhB2bEnterpriseContractMapper jdhB2bEnterpriseContractMapper;
    /** */
    @Resource
    private GenerateIdFactory generateIdFactory;


    /**
     * 保存
     *
     * @param jdhB2bEnterprise
     * @return count
     */
    @Override
    public int save(JdhB2bEnterpriseContract jdhB2bEnterprise) {
        JdhB2bEnterpriseContractPo po = JdhB2bEnterpriseContractConverter.INSTANCE.entityToPo(jdhB2bEnterprise);;
        if(Objects.nonNull(po.getContractId())){
            //更新
            LambdaUpdateWrapper<JdhB2bEnterpriseContractPo> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.eq(JdhB2bEnterpriseContractPo::getContractId, po.getContractId());
            return jdhB2bEnterpriseContractMapper.update(po,updateWrapper);
        }else {
            Long contractId = generateIdFactory.getId();
            po.setContractId(contractId);
            Date now = new Date();
            po.setCreateTime(now);
            po.setUpdateTime(now);
            po.setCreateUser(jdhB2bEnterprise.getCreateUser());
            //插入
            return jdhB2bEnterpriseContractMapper.insert(po);
        }
    }

    /**
     * 保存
     *
     * @param jdhB2bEnterpriseContract
     * @return count
     */
    @Override
    public int delete(JdhB2bEnterpriseContract jdhB2bEnterpriseContract) {
        //更新
        LambdaUpdateWrapper<JdhB2bEnterpriseContractPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(JdhB2bEnterpriseContractPo::getUpdateUser, jdhB2bEnterpriseContract.getOperator());
        updateWrapper.set(JdhB2bEnterpriseContractPo::getYn, YnStatusEnum.NO.getCode());
        updateWrapper.eq(JdhB2bEnterpriseContractPo::getContractId, jdhB2bEnterpriseContract.getContractId());
        return jdhB2bEnterpriseContractMapper.update(null,updateWrapper);
    }

    /**
     * 更新合同
     *
     * @param jdhB2bEnterprise
     * @return
     */
    @Override
    public int updateContract(JdhB2bEnterpriseContract jdhB2bEnterprise) {
        //更新
        LambdaUpdateWrapper<JdhB2bEnterpriseContractPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(JdhB2bEnterpriseContractPo::getStatus, jdhB2bEnterprise.getStatus());
        updateWrapper.eq(JdhB2bEnterpriseContractPo::getEnterpriseId, jdhB2bEnterprise.getEnterpriseId());
        updateWrapper.eq(JdhB2bEnterpriseContractPo::getContractNumber, jdhB2bEnterprise.getContractNumber());
        return jdhB2bEnterpriseContractMapper.update(null,updateWrapper);
    }

    /**
     * 通过queryContext 查询
     *
     * @param queryContext
     * @return
     */
    @Override
    public JdhB2bEnterpriseContract findJdhB2bEnterpriseContract(JdhB2bEnterpriseContractQuery queryContext) {
        LambdaQueryWrapper<JdhB2bEnterpriseContractPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(queryContext.getContractId()),JdhB2bEnterpriseContractPo::getContractId,queryContext.getContractId())
                .eq(Objects.nonNull(queryContext.getEnterpriseId()),JdhB2bEnterpriseContractPo::getEnterpriseId,queryContext.getEnterpriseId())
                .eq(StringUtils.isNoneBlank(queryContext.getContractNumber()),JdhB2bEnterpriseContractPo::getContractNumber,queryContext.getContractNumber())
                .eq(Objects.nonNull(queryContext.getValid()),JdhB2bEnterpriseContractPo::getValid,queryContext.getValid())
                .eq(Objects.nonNull(queryContext.getStatus()),JdhB2bEnterpriseContractPo::getStatus,queryContext.getStatus())
                .eq(JdhB2bEnterpriseContractPo::getYn, YnStatusEnum.YES.getCode());
        List<JdhB2bEnterpriseContractPo> jdhB2bEnterpriseContractPos = jdhB2bEnterpriseContractMapper.selectList(queryWrapper);
        if(CollectionUtils.isEmpty(jdhB2bEnterpriseContractPos)){
            return null;
        }
        return JdhB2bEnterpriseContractConverter.INSTANCE.po2Entity(jdhB2bEnterpriseContractPos.get(0));
    }

    /**
     * 分页查询企业合同列表
     *
     * @param enterpriseContractQuery
     * @return
     */
    @Override
    public Page<JdhB2bEnterpriseContract> queryPageEnterpriseContract(JdhB2bEnterpriseContractQuery enterpriseContractQuery) {
        LambdaQueryWrapper<JdhB2bEnterpriseContractPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Objects.nonNull(enterpriseContractQuery.getEnterpriseId()), JdhB2bEnterpriseContractPo::getEnterpriseId, enterpriseContractQuery.getEnterpriseId())
                .eq(JdhB2bEnterpriseContractPo::getYn, YnStatusEnum.YES.getCode())
                .orderByDesc(JdhB2bEnterpriseContractPo::getCreateTime);
        Page<JdhB2bEnterpriseContractPo> param = new Page<>(enterpriseContractQuery.getPageNum(), enterpriseContractQuery.getPageSize());
        IPage<JdhB2bEnterpriseContractPo> jdhB2bEnterprisePoIPage = jdhB2bEnterpriseContractMapper.selectPage(param, queryWrapper);
        List<JdhB2bEnterpriseContract> jdhB2bEnterpriseList = JdhB2bEnterpriseContractConverter.INSTANCE.po2EntityList(jdhB2bEnterprisePoIPage.getRecords());
        return JdhBasicPoConverter.initPage(jdhB2bEnterprisePoIPage, jdhB2bEnterpriseList);
    }

    /**
     * 企业合同列表
     *
     * @param enterpriseContractQuery
     * @return
     */
    @Override
    public List<JdhB2bEnterpriseContract> findContractList(JdhB2bEnterpriseContractQuery enterpriseContractQuery) {
        return Collections.emptyList();
    }
}
