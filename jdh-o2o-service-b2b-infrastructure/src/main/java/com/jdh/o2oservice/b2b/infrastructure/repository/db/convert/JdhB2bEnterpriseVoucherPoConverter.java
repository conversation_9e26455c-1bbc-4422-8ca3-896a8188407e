package com.jdh.o2oservice.b2b.infrastructure.repository.db.convert;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.model.EnterpriseVoucher;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.po.JdhB2bEnterpriseVoucherPo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;


/**
 * JdhB2bEnterpriseVoucherPoConverter
 *
 * <AUTHOR>
 * @date 2025/03/03
 */
@Mapper
public interface JdhB2bEnterpriseVoucherPoConverter {

    /**
     * JdhB2bEnterpriseVoucherPoConverter
     */
    JdhB2bEnterpriseVoucherPoConverter INSTANCE = Mappers.getMapper(JdhB2bEnterpriseVoucherPoConverter.class);


    /**
     * po2 实体
     *
     * @param voucherPo 凭证 PO
     * @return {@link EnterpriseVoucher }
     */
    @Mappings({
            @Mapping(target = "extend",expression = "java(parseObject(voucherPo.getExtend(),com.jdh.o2oservice.b2b.domain.enterprisevoucher.model.EnterpriseVoucherExtend.class))")
    })
    EnterpriseVoucher po2Entity(JdhB2bEnterpriseVoucherPo voucherPo);

    /**
     * po2 实体集合
     *
     * @param voucherPo
     * @return
     */
    List<EnterpriseVoucher> po2EntityList(List<JdhB2bEnterpriseVoucherPo> voucherPo);

    /**
     * 实体2 po
     *
     * @param enterpriseVoucher 企业凭证
     * @return {@link JdhB2bEnterpriseVoucherPo }
     */
    @Mappings({
            @Mapping(target = "extend",expression = "java(com.alibaba.fastjson.JSON.toJSONString(enterpriseVoucher.getExtend()))"),
            //@Mapping(target = "appointmentPhoneIndex",source = "appointmentPhone"),
            //@Mapping(target = "appointmentNameIndex",source = "appointmentName")
    })
    JdhB2bEnterpriseVoucherPo entity2Po(EnterpriseVoucher enterpriseVoucher);

    /**
     * parseObject
     *
     * @param text  文本
     * @param clazz clazz
     * @return {@link T }
     */
    default <T> T parseObject(String text, Class<T> clazz) {
        if(StrUtil.isNotBlank(text)){
            return JSON.parseObject(text, clazz);
        }
        return null;
    }

}
