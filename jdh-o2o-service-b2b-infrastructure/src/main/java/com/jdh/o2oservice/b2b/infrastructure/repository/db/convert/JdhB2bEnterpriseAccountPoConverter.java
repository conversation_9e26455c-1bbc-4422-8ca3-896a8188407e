package com.jdh.o2oservice.b2b.infrastructure.repository.db.convert;
import com.jdh.o2oservice.b2b.domain.enterpriseaccount.model.JdhB2bEnterpriseAccountDetail;
import com.jdh.o2oservice.b2b.domain.enterpriseaccount.repository.model.JdhB2bEnterpriseAccount;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.po.JdhB2bEnterpriseAccountDetailPo;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.po.JdhB2bEnterpriseAccountPo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
 * @Description JdhB2bEnterpriseAccountPoConverter
 * @Date 2025/3/11 上午11:06
 * <AUTHOR>
 *
 **/
@Mapper
public interface JdhB2bEnterpriseAccountPoConverter {

    JdhB2bEnterpriseAccountPoConverter INSTANCE = Mappers.getMapper(JdhB2bEnterpriseAccountPoConverter.class);

    JdhB2bEnterpriseAccountDetail po2Entity(JdhB2bEnterpriseAccountDetailPo jdhB2bEnterpriseAccountDetailPo);

    List<JdhB2bEnterpriseAccountDetail> po2EntityList(List<JdhB2bEnterpriseAccountDetailPo> accountDetailPoList);
    JdhB2bEnterpriseAccount po2AccountEntity(JdhB2bEnterpriseAccountPo jdhB2bEnterpriseAccountPo);

    List<JdhB2bEnterpriseAccount> po2AccountEntityList(List<JdhB2bEnterpriseAccountPo> accountPoList);

}
