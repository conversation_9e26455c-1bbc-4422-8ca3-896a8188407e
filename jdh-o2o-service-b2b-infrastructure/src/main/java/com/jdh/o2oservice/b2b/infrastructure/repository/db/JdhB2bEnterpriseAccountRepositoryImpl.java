package com.jdh.o2oservice.b2b.infrastructure.repository.db;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jdh.o2oservice.b2b.base.enums.YnStatusEnum;
import com.jdh.o2oservice.b2b.domain.enterpriseaccount.repository.JdhB2bEnterpriseAccountRepository;
import com.jdh.o2oservice.b2b.domain.enterpriseaccount.repository.model.JdhB2bEnterpriseAccount;
import com.jdh.o2oservice.b2b.domain.enterpriseaccount.repository.model.JdhEnterpriseAccountIdentifier;
import com.jdh.o2oservice.b2b.domain.enterpriseaccount.repository.query.JdhB2bEnterpriseAccountQuery;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.convert.JdhB2bEnterpriseAccountPoConverter;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.convert.JdhBasicPoConverter;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.mapper.JdhB2bEnterpriseAccountMapper;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.po.JdhB2bEnterpriseAccountPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;
import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 企业账户
 */
@Repository
@Slf4j
public class JdhB2bEnterpriseAccountRepositoryImpl implements JdhB2bEnterpriseAccountRepository {

    @Resource
    private JdhB2bEnterpriseAccountMapper jdhB2bEnterpriseAccountMapper;

    /**
     * 保存
     * @param jdhB2bEnterpriseAccount
     * @return
     */
    @Override
    public int save(JdhB2bEnterpriseAccount jdhB2bEnterpriseAccount) {
        JdhB2bEnterpriseAccountPo po = JdhB2bEnterpriseAccountPo.builder()
                .enterpriseId(jdhB2bEnterpriseAccount.getEnterpriseId())
                .accountId(jdhB2bEnterpriseAccount.getAccountId())
                .creditAmount(jdhB2bEnterpriseAccount.getCreditAmount())
                .freezeAmount(jdhB2bEnterpriseAccount.getFreezeAmount())
                .extend(jdhB2bEnterpriseAccount.getExtend())
                .createUser(jdhB2bEnterpriseAccount.getOperator())
                .updateUser(jdhB2bEnterpriseAccount.getOperator())
                .build();
        JdhBasicPoConverter.initInsertBasicPo(po);
        return jdhB2bEnterpriseAccountMapper.insert(po);
    }

    /**
     * 更新
     * @param jdhB2bEnterpriseAccount
     * @return
     */
    @Override
    public int update(JdhB2bEnterpriseAccount jdhB2bEnterpriseAccount) {
        LambdaUpdateWrapper<JdhB2bEnterpriseAccountPo> updateWrapper = Wrappers.lambdaUpdate();
        JdhB2bEnterpriseAccountPo po = JdhB2bEnterpriseAccountPo.builder().build();
        updateWrapper.set(jdhB2bEnterpriseAccount.getCreditAmount() != null, JdhB2bEnterpriseAccountPo::getCreditAmount, jdhB2bEnterpriseAccount.getCreditAmount())
                .set(jdhB2bEnterpriseAccount.getFreezeAmount() != null, JdhB2bEnterpriseAccountPo::getFreezeAmount, jdhB2bEnterpriseAccount.getFreezeAmount())
                .set(jdhB2bEnterpriseAccount.getExtend() != null, JdhB2bEnterpriseAccountPo::getExtend, jdhB2bEnterpriseAccount.getExtend())
                .setSql("`version` = version+1")
                .eq(JdhB2bEnterpriseAccountPo::getAccountId, jdhB2bEnterpriseAccount.getAccountId())
                .eq(JdhB2bEnterpriseAccountPo::getVersion, jdhB2bEnterpriseAccount.getVersion());
        Date now = new Date();
        po.setUpdateUser(jdhB2bEnterpriseAccount.getOperator());
        po.setUpdateTime(now);
        return jdhB2bEnterpriseAccountMapper.update(po, updateWrapper);
    }

    /**
     * 通过Identify 查询
     * @param identifier
     * @return
     */
    @Override
    public JdhB2bEnterpriseAccount find(JdhEnterpriseAccountIdentifier identifier) {
        Long accountId = identifier.getAccountId();
        LambdaQueryWrapper<JdhB2bEnterpriseAccountPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhB2bEnterpriseAccountPo::getAccountId, accountId)
                .eq(JdhB2bEnterpriseAccountPo::getYn, YnStatusEnum.YES.getCode());
        JdhB2bEnterpriseAccountPo jdhB2bEnterpriseAccountPo = jdhB2bEnterpriseAccountMapper.selectOne(queryWrapper);
        return JdhB2bEnterpriseAccountPoConverter.INSTANCE.po2AccountEntity(jdhB2bEnterpriseAccountPo);
    }

    /**
     * 企业账户列表
     * @param query
     * @return
     */
    @Override
    public List<JdhB2bEnterpriseAccount> findList(JdhB2bEnterpriseAccountQuery query) {
        LambdaQueryWrapper<JdhB2bEnterpriseAccountPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(query.getEnterpriseId() != null, JdhB2bEnterpriseAccountPo::getEnterpriseId, query.getEnterpriseId())
                .eq(query.getAccountId() != null, JdhB2bEnterpriseAccountPo::getAccountId, query.getAccountId())
                .in(CollectionUtils.isNotEmpty(query.getEnterpriseIdList()), JdhB2bEnterpriseAccountPo::getEnterpriseId, query.getEnterpriseIdList())
                .eq(JdhB2bEnterpriseAccountPo::getYn, YnStatusEnum.YES.getCode());
        List<JdhB2bEnterpriseAccountPo> jdhB2bEnterpriseAccountPoList = jdhB2bEnterpriseAccountMapper.selectList(queryWrapper);
        return JdhB2bEnterpriseAccountPoConverter.INSTANCE.po2AccountEntityList(jdhB2bEnterpriseAccountPoList);
    }
}
