package com.jdh.o2oservice.b2b.infrastructure.repository.db.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * JdhB2bEnterpriseOrderPlatformPo
 *
 * <AUTHOR>
 * @date 2025/02/25
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "jdh_b2b_enterprise_order_platform",autoResultMap = true)
public class JdhB2bEnterpriseOrderPlatformPo extends JdhBasicPo{

    /**
    * 主键
    */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
    * 企业ID
    */
    private Long enterpriseId;

    /**
    * 源订单平台
    */
    private String sourceOrderPlatform;

    /**
    * 扩展信息
    */
    private String extend;

}