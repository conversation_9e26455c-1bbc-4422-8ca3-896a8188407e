package com.jdh.o2oservice.b2b.infrastructure.repository.db;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.b2b.base.enums.YnStatusEnum;
import com.jdh.o2oservice.b2b.domain.support.orderplatform.model.OrderPlatform;
import com.jdh.o2oservice.b2b.domain.support.orderplatform.repository.OrderPlatformRepository;
import com.jdh.o2oservice.b2b.domain.support.orderplatform.repository.query.OrderPlatformPageQuery;
import com.jdh.o2oservice.b2b.domain.support.orderplatform.repository.query.OrderPlatformQuery;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.convert.JdhB2bOrderPlatformPoConverter;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.convert.JdhBasicPoConverter;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.mapper.JdhB2bEnterpriseOrderPlatformMapper;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.po.JdhB2bEnterpriseOrderPlatformPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;

/**
 * OrderPlatformRepositoryImpl
 *
 * <AUTHOR>
 * @date 2025/02/25
 */
@Slf4j
@Repository
public class OrderPlatformRepositoryImpl implements OrderPlatformRepository {

    /**
     * jdhB2bEnterpriseOrderPlatformMapper
     */
    @Autowired
    private JdhB2bEnterpriseOrderPlatformMapper jdhB2bEnterpriseOrderPlatformMapper;

    /**
     * save
     *
     * @param orderPlatform orderPlatform
     * @return int
     */
    @Override
    public int save(OrderPlatform orderPlatform) {
        JdhB2bEnterpriseOrderPlatformPo po = JdhB2bOrderPlatformPoConverter.INS.entity2Po(orderPlatform);
        //insert
        if(Objects.isNull(po.getId())){
            JdhBasicPoConverter.initInsertBasicPo(po);
            return jdhB2bEnterpriseOrderPlatformMapper.insert(po);
        }else{
            //update

            return 0;
        }
    }

    /**
     * 查询
     *
     * @param query 查询
     * @return {@link OrderPlatform }
     */
    @Override
    public OrderPlatform query(OrderPlatformQuery query) {
        LambdaQueryWrapper<JdhB2bEnterpriseOrderPlatformPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(JdhB2bEnterpriseOrderPlatformPo::getEnterpriseId, query.getEnterpriseId());
        queryWrapper.eq(JdhB2bEnterpriseOrderPlatformPo::getSourceOrderPlatform, query.getSourceOrderPlatform());
        queryWrapper.eq(JdhB2bEnterpriseOrderPlatformPo::getYn, YnStatusEnum.YES.getCode());

        List<JdhB2bEnterpriseOrderPlatformPo> orderPlatformPos = jdhB2bEnterpriseOrderPlatformMapper.selectList(queryWrapper);
        if(CollUtil.isNotEmpty(orderPlatformPos)){
            return JdhB2bOrderPlatformPoConverter.INS.po2Entity(orderPlatformPos.get(0));
        }
        return null;
    }

    /**
     * 查询页
     *
     * @param query query
     * @return {@link Page }<{@link OrderPlatform }>
     */
    @Override
    public Page<OrderPlatform> queryPage(OrderPlatformPageQuery query) {
        Page<JdhB2bEnterpriseOrderPlatformPo> param = new Page<>(query.getPageNum(), query.getPageSize());

        LambdaQueryWrapper<JdhB2bEnterpriseOrderPlatformPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(query.getEnterpriseId()),JdhB2bEnterpriseOrderPlatformPo::getEnterpriseId, query.getEnterpriseId());
        queryWrapper.eq(StrUtil.isNotBlank(query.getSourceOrderPlatform()),JdhB2bEnterpriseOrderPlatformPo::getSourceOrderPlatform, query.getSourceOrderPlatform());
        queryWrapper.eq(JdhB2bEnterpriseOrderPlatformPo::getYn, YnStatusEnum.YES.getCode());

        Page<JdhB2bEnterpriseOrderPlatformPo> platformPoPage = jdhB2bEnterpriseOrderPlatformMapper.selectPage(param, queryWrapper);

        return JdhBasicPoConverter.initPage(platformPoPage,JdhB2bOrderPlatformPoConverter.INS.po2EntityList(platformPoPage.getRecords()));
    }
}
