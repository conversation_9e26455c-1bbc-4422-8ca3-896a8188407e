package com.jdh.o2oservice.b2b.infrastructure.rpc;
import com.alibaba.fastjson.JSON;
import com.jd.user.sdk.export.UserInfoExportService;
import com.jd.user.sdk.export.domain.UserBasicInfoVo;
import com.jdh.o2oservice.b2b.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.b2b.base.exception.BusinessErrorCode;
import com.jdh.o2oservice.b2b.base.exception.BusinessException;
import com.jdh.o2oservice.b2b.domain.support.common.bo.UserBaseInfoBo;
import com.jdh.o2oservice.b2b.domain.support.common.repository.UserInfoRpcService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.Objects;

@Component
@Slf4j
public class UserInfoRpcServiceImpl implements UserInfoRpcService {
    /** 用户rpc服务 */
    @Resource
    private UserInfoExportService userInfoExportService;

    /**  90为Bpin 非90 为Cpin */
    private static final Integer B_PIN_LEVEL = 90;

    /**
     * 90为Bpin 非90 为Cpin
     * https://cf.jd.com/pages/viewpage.action?pageId=100092999
     *
     * @param pin
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "UserInfoRpcServiceImpl.isBpin")
    public Boolean isBpin(String pin) {
        try {
            log.info("UserInfoRpcServiceImpl->isBpin request={}", pin);
            Integer level = userInfoExportService.getUserLevel(pin);
            log.info("UserInfoRpcServiceImpl->isBpin level={}", level);
            return Objects.equals(level, B_PIN_LEVEL);
        }catch (Exception e){
            log.error("[UserInfoRpcServiceImpl -> isBpin],查询账号信息异常!", e);
            throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
        }catch (Throwable e){
            throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR);
        }
    }

    /**
     * 根据pin获取用户基本信息
     *
     * @param pin      用户的pin码
     * @param loadType 加载类型，用于区分不同的加载场景
     * @return 用户基本信息对象
     */
    @Override
    @LogAndAlarm
    public UserBaseInfoBo getUserBaseInfoByPin(String pin, int loadType) {
        try {
            UserBasicInfoVo userBaseInfoByPin = userInfoExportService.getUserBasicInfoByPin(pin, loadType);
            log.info("UserInfoRpcServiceImpl->getUserBaseInfoByPin,userBaseInfoByPin={},", JSON.toJSONString(userBaseInfoByPin));
            if (Objects.nonNull(userBaseInfoByPin)){
                UserBaseInfoBo userBaseInfoBo = new UserBaseInfoBo();
                userBaseInfoBo.setPin(userBaseInfoByPin.getPin());
                userBaseInfoBo.setYunBigImageUrl(userBaseInfoByPin.getYunBigImageUrl());
                userBaseInfoBo.setYunMidImageUrl(userBaseInfoByPin.getYunMidImageUrl());
                userBaseInfoBo.setYunSmaImageUrl(userBaseInfoByPin.getYunSmaImageUrl());
                userBaseInfoBo.setMobile(userBaseInfoByPin.getMobile());
                log.info("UserInfoRpcServiceImpl->getUserBaseInfoByPin,userBaseInfoBo={},", JSON.toJSONString(userBaseInfoBo));
                return userBaseInfoBo;
            }
            log.info("UserInfoRpcServiceImpl->getUserBaseInfoByPin,userBaseInfoBo null");
            return null;
        }catch (Exception e){
            log.error("UserInfoRpcServiceImpl->getUserBaseInfoByPin error!", e);
            throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
        }catch (Throwable e){
            log.error("O2oUserInfoRpcServiceImpl->getUserBaseInfoByPin error!", e);
            throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR);
        }
    }
}
