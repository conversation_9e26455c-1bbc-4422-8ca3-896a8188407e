package com.jdh.o2oservice.b2b.infrastructure.rpc;

import com.alibaba.fastjson.JSON;
import com.jd.health.ares.open.platform.domain.common.JsfResult;
import com.jd.health.ares.open.platform.export.dto.VirtualUserInfoDTO;
import com.jd.health.ares.open.platform.export.service.PinExportService;
import com.jdh.o2oservice.b2b.base.exception.BusinessErrorCode;
import com.jdh.o2oservice.b2b.base.exception.BusinessException;
import com.jdh.o2oservice.b2b.domain.support.common.bo.VirtualPinBo;
import com.jdh.o2oservice.b2b.domain.support.common.repository.VirtualPinRpc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * VirtualPinRpcImpl
 */
@Slf4j
@Repository
public class VirtualPinRpcImpl implements VirtualPinRpc {

    /**
     * pinExportService
     */
    @Resource
    private PinExportService pinExportService;


    /**
     * 获取虚拟pin
     *
     * @param pinBo pinBo
     * @return {@link String }
     */
    @Override
    public String getVirtualPin(VirtualPinBo pinBo) {
        try {
            VirtualUserInfoDTO param = new VirtualUserInfoDTO();
            param.setUserId(pinBo.getUserId());
            param.setOrgId(pinBo.getOrgId());
            param.setUserIp(pinBo.getUserIp());
            log.info("VirtualPinRpcImpl -> getVirtualPin param:{}", JSON.toJSONString(param));
            JsfResult<String> virtualPin = pinExportService.getVirtualPin(param);
            log.info("VirtualPinRpcImpl -> getVirtualPin virtualPin:{}", JSON.toJSONString(virtualPin));
            if(Objects.nonNull(virtualPin) && virtualPin.isSuccess()){
                return virtualPin.getData();
            }else{
                throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
            }
        } catch (BusinessException be) {
            log.error("VirtualPinRpcImpl -> getVirtualPin business exception", be);
            throw new BusinessException(be.getErrorCode());
        } catch (Exception e) {
            log.error("VirtualPinRpcImpl -> getVirtualPin exception", e);
            throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
        }
    }
}
