package com.jdh.o2oservice.b2b.infrastructure.rpc.converter;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.O2oAngelWorkBo;
import com.jdh.o2oservice.export.angelpromise.dto.AngelWorkDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface O2oServiceAngelWorkRpcConvert {

    O2oServiceAngelWorkRpcConvert INS = Mappers.getMapper(O2oServiceAngelWorkRpcConvert.class);

    List<O2oAngelWorkBo> toO2oAngelWorkBo(List<AngelWorkDto> angelWorkDtoList);

}
