package com.jdh.o2oservice.b2b.infrastructure.repository.db;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.b2b.domain.support.operationlog.mode.JdhB2bOperationLog;
import com.jdh.o2oservice.b2b.domain.support.operationlog.repository.JdhB2bOperationLogRepository;
import com.jdh.o2oservice.b2b.domain.support.operationlog.repository.query.JdhB2bOperationLogQuery;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.convert.JdhB2bOperationLogPoConverter;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.convert.JdhBasicPoConverter;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.mapper.JdhB2bOperationLogMapper;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.po.JdhB2bOperationLogPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Repository
@Slf4j
public class JdhB2bOperationLogRepositoryImpl implements JdhB2bOperationLogRepository {

    @Resource
    private JdhB2bOperationLogMapper jdhB2bOperationLogMapper;

    /**
     * 保存
     * @param entity
     * @return
     */
    @Override
    public int save(JdhB2bOperationLog entity) {
        JdhB2bOperationLogPo po = JdhB2bOperationLogPo.builder()
                .operationLogId(entity.getOperationLogId())
                .enterpriseId(entity.getEnterpriseId())
                .bizSceneKey(entity.getBizSceneKey())
                .bizSceneDesc(entity.getBizSceneDesc())
                .operateType(entity.getOperateType())
                .bizUnionId(entity.getBizUnionId())
                .clientInfo(entity.getClientInfo())
                .param(entity.getParam())
                .result(entity.getResult())
                .resultType(entity.getResultType())
                .operator(entity.getOperator())
                .operateTime(entity.getOperateTime() == null ? new Date() : entity.getOperateTime())
                .extend(entity.getExtend())
                .build();
        return jdhB2bOperationLogMapper.insert(po);
    }

    /**
     * 更新
     * @param entity
     * @return
     */
    @Override
    public int update(JdhB2bOperationLog entity) {
        LambdaUpdateWrapper<JdhB2bOperationLogPo> updateWrapper = Wrappers.lambdaUpdate();
        JdhB2bOperationLogPo po = JdhB2bOperationLogPo.builder().build();
        updateWrapper.set(StringUtils.isNotBlank(entity.getResult()), JdhB2bOperationLogPo::getResult, entity.getResult())
                .eq(JdhB2bOperationLogPo::getOperationLogId, entity.getOperationLogId());
        Date now = new Date();
        po.setOperator(entity.getOperator());
        po.setOperateTime(now);
        return jdhB2bOperationLogMapper.update(po, updateWrapper);
    }

    /**
     * 查询
     * @param operationLogId
     * @return
     */
    @Override
    public JdhB2bOperationLog getOperationLog(Long operationLogId) {
        LambdaQueryWrapper<JdhB2bOperationLogPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhB2bOperationLogPo::getOperationLogId, operationLogId);
        JdhB2bOperationLogPo jdhB2bOperationLogPo = jdhB2bOperationLogMapper.selectOne(queryWrapper);
        return JdhB2bOperationLogPoConverter.INSTANCE.po2Entity(jdhB2bOperationLogPo);
    }

    /**
     * 分页查询操作日志
     * @param query
     * @return
     */
    @Override
    public Page<JdhB2bOperationLog> queryPageOperationLog(JdhB2bOperationLogQuery query) {
        LambdaQueryWrapper<JdhB2bOperationLogPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(query.getId() != null, JdhB2bOperationLogPo::getId, query.getId())
                .eq(query.getOperationLogId() != null, JdhB2bOperationLogPo::getOperationLogId, query.getOperationLogId())
                .eq(query.getEnterpriseId() != null, JdhB2bOperationLogPo::getEnterpriseId, query.getEnterpriseId())
                .eq(query.getOperateType() != null, JdhB2bOperationLogPo::getOperateType, query.getOperateType())
                .eq(query.getResultType() != null, JdhB2bOperationLogPo::getResultType, query.getResultType())
                .like(StringUtils.isNotBlank(query.getOperator()), JdhB2bOperationLogPo::getOperator, query.getOperator())
                .orderByDesc(JdhB2bOperationLogPo::getOperateTime);
        Page<JdhB2bOperationLogPo> param = new Page<>(query.getPageNum(), query.getPageSize());
        IPage<JdhB2bOperationLogPo> iPage = jdhB2bOperationLogMapper.selectPage(param, queryWrapper);
        List<JdhB2bOperationLog> dataList = JdhB2bOperationLogPoConverter.INSTANCE.po2Entity(iPage.getRecords());
        return JdhBasicPoConverter.initPage(iPage, dataList);
    }
}
