package com.jdh.o2oservice.b2b.infrastructure.rpc.converter;

import cn.hutool.core.date.DateUtil;
import com.jdh.o2oservice.b2b.base.constant.NumConstant;
import com.jdh.o2oservice.b2b.base.enums.JdhVoucherSourceTypeEnum;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.*;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.ctx.SubmitPromiseCtx;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.model.PromiseAppointmentTime;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.model.PromiseIntendedNurse;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.model.PromisePatient;
import com.jdh.o2oservice.export.angelpromise.dto.AngelWorkDetailDto;
import com.jdh.o2oservice.export.promise.cmd.*;
import com.jdh.o2oservice.export.promise.dto.*;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Mapper
public interface O2oServicePromiseRpcConvert {

    /**
     * 实例
     */
    O2oServicePromiseRpcConvert INS = Mappers.getMapper(O2oServicePromiseRpcConvert.class);

    /**
     * dto转 bo
     * @param promiseDtoList
     * @return
     */
    List<O2oPromiseBo> toO2oPromiseBo(List<PromiseDto> promiseDtoList);

    /**
     * dto 转 bo
     * @param dto
     * @return
     */
    O2oPromiseBo dto2Bo(PromiseDto dto);

    O2oPromiseAppointmentTimeBo dto2O2oPromiseAppointmentTimeBo(PromiseAppointmentTimeDto dto);
    @Mappings({
            @Mapping(target = "birthday", ignore = true),
            @Mapping(target = "userName", ignore = true),
            @Mapping(target = "phoneNumber", ignore = true),
            @Mapping(target = "credentialNum", ignore = true)
    })
    O2oPromisePatientBo dto2O2oPromisePatientBo(PromisePatientDto dto);
    List<O2oPromisePatientBo> dto2O2oPromisePatientBoList(List<PromisePatientDto> dtoList);
    O2oPromiseStationBo dto2O2oPromiseStationBo(PromiseStationDto dto);
    O2oPromiseServiceDetailBo dtoO2oPromiseServiceDetailBo(PromiseServiceDetailDto dto);
    List<O2oPromiseServiceDetailBo> dtoO2oPromiseServiceDetailBoList(List<PromiseServiceDetailDto> dtoList);
    O2oPromiseExtendBo dtoO2oPromiseExtendBo(PromiseExtendDto dto);
    List<O2oPromiseExtendBo> dtoO2oPromiseExtendBoList(List<PromiseExtendDto> dtoList);

    AngelWorkDetailBo dtoO2oPromiseExtendBoList(AngelWorkDetailDto dto);

    @Mappings({
        @Mapping(target = "promiseBo", source = "promiseDto"),
        @Mapping(target = "angelWorkDetailBo", source = "angelWorkDetailDto")
    })
    CompletePromiseBo convertCompletePromiseDto (CompletePromiseDto dto);

    List<CompletePromiseBo> convertCompletePromiseDtoList (List<CompletePromiseDto> dto);

    /**
     * ctx2CreateVoucherCmd
     *
     * @param ctx ctx
     * @return {@link CreateVoucherCmd }
     */
    default CreateVoucherCmd ctx2CreateVoucherCmd(SubmitPromiseCtx ctx){
        CreateVoucherCmd createVoucherCmd = new CreateVoucherCmd();
        //基本信息
        createVoucherCmd.setVerticalCode(ctx.getVerticalCode());
        createVoucherCmd.setSyncCreatePromise(Boolean.TRUE);
        createVoucherCmd.setServiceType("care");
        createVoucherCmd.setServiceId(ctx.getSkuId().toString());
        createVoucherCmd.setUserPin(ctx.getCustomerUserPin());
        createVoucherCmd.setSourceVoucherId(ctx.getEnterpriseVoucherId().toString());
        createVoucherCmd.setSourceType(JdhVoucherSourceTypeEnum.EXCHANGE.getType());
        createVoucherCmd.setPromiseNum(1);
        createVoucherCmd.setExpireDate(DateUtil.parse("2099-01-01"));
        createVoucherCmd.setCreateUser(ctx.getCustomerUserPin());

        //扩展信息
        VoucherExtend voucherExtend = new VoucherExtend();
        voucherExtend.setAutoPromise(Boolean.FALSE);
        voucherExtend.setSkuId(ctx.getSkuId().toString());
        voucherExtend.setSkuName(ctx.getSkuName());
        voucherExtend.setOrderPhone(ctx.getExtend().getAddress().getMobile());
        voucherExtend.setOrderRemark(ctx.getExtend().getRemark());
        voucherExtend.setPromisePatientNum(ctx.getExtend().getPatientList().size());
        voucherExtend.setHasAdded(NumConstant.NUM_0);
        voucherExtend.setMainSkuId(ctx.getSkuId());
        voucherExtend.setMainSkuName(ctx.getSkuName());

        if(Objects.nonNull(ctx.getExtend().getIntendedNurse())){
            IntendedNurse intendedNurse = new IntendedNurse();
            intendedNurse.setAngelId(ctx.getExtend().getIntendedNurse().getAngelId());
            intendedNurse.setInvitationCode(ctx.getExtend().getIntendedNurse().getInvitationCode());
            intendedNurse.setRecommendType(ctx.getExtend().getIntendedNurse().getRecommendType());
            voucherExtend.setIntendedNurse(intendedNurse);
        }

        createVoucherCmd.setExtend(voucherExtend);

        //voucherItemList
        List<VoucherItem> voucherItemList = new ArrayList<>();
        VoucherItem voucherItem = new VoucherItem();
        voucherItem.setVerticalCode(ctx.getVerticalCode());
        voucherItem.setServiceId(ctx.getSkuId());
        voucherItem.setServiceType("care");
        voucherItem.setTag(NumConstant.NUM_0);
        voucherItemList.add(voucherItem);
        createVoucherCmd.setVoucherItemList(voucherItemList);


        return createVoucherCmd;
    }

    /**
     * ctx2SubmitCmd
     *
     * @param ctx ctx
     * @return {@link SubmitPromiseCmd }
     */
    default SubmitPromiseCmd ctx2SubmitCmd(SubmitPromiseCtx ctx){
        SubmitPromiseCmd cmd = new SubmitPromiseCmd();

        cmd.setVerticalCode(ctx.getVerticalCode());
        cmd.setServiceType(ctx.getServiceType());
        cmd.setStoreId(ctx.getExtend().getAddress().getFullAddress());
        cmd.setUserPin(ctx.getCustomerUserPin());
        if(Objects.nonNull(ctx.getPromiseId())) {
            cmd.setPromiseId(String.valueOf(ctx.getPromiseId()));
        }

        List<SubmitUser> submitUsers = patientList2SubmitUserList(ctx.getExtend().getPatientList());
        cmd.setUsers(submitUsers);

        cmd.setAppointmentTime(convertAppointmentTime(ctx.getExtend().getAppointmentTime()));

        if(Objects.nonNull(ctx.getExtend().getIntendedNurse()) && Objects.nonNull(ctx.getExtend().getIntendedNurse().getAngelId())) {
            cmd.setIntendedNurse(convertIntendedNurse(ctx.getExtend().getIntendedNurse()));
            cmd.getIntendedNurse().setRecommendType(2);
        }

        cmd.setRemark(ctx.getExtend().getRemark());

        List<PromiseServiceItem> serviceItems = new ArrayList<>();
        PromiseServiceItem serviceItem = new PromiseServiceItem();
        serviceItem.setServiceId(ctx.getSkuId());
        serviceItems.add(serviceItem);
        cmd.setServices(serviceItems);

        cmd.setAppointmentPhone(ctx.getAppointmentPhone());
        cmd.setAppointmentUserName(ctx.getAppointmentName());

        return cmd;
    }

    SubmitUser patient2SubmitUser(PromisePatient patient);
    List<SubmitUser> patientList2SubmitUserList(List<PromisePatient> patientList);
    AppointmentTime convertAppointmentTime(PromiseAppointmentTime appointmentTime);
    IntendedNurse convertIntendedNurse(PromiseIntendedNurse intendedNurse);
}
