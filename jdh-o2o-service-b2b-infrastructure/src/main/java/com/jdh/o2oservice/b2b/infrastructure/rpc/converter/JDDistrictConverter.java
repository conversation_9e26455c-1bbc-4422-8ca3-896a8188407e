package com.jdh.o2oservice.b2b.infrastructure.rpc.converter;

import com.jd.addresstranslation.api.base.JDDistrictInfo;
import com.jdh.o2oservice.b2b.domain.support.address.bo.JDDistrictBo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @program: jdh-o2o-service
 * @description:
 * @author: luxingchen3
 * @create: 2024-04-18 11:05
 **/
@Mapper
public interface JDDistrictConverter {
    JDDistrictConverter INSTANCE = Mappers.getMapper(JDDistrictConverter.class);

    JDDistrictBo dtoToBo(JDDistrictInfo dto);

    JDDistrictInfo boToDto(JDDistrictBo bo);

    List<JDDistrictBo> dtoListToBoList(List<JDDistrictInfo> dtoList);

    List<JDDistrictInfo> boListToDtoList(List<JDDistrictBo> boList);
}
