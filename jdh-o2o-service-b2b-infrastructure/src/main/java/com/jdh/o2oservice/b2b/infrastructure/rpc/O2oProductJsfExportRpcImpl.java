package com.jdh.o2oservice.b2b.infrastructure.rpc;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jdh.o2oservice.b2b.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.b2b.base.enums.ThreadPoolConfigEnum;
import com.jdh.o2oservice.b2b.base.exception.BusinessErrorCode;
import com.jdh.o2oservice.b2b.base.exception.BusinessException;
import com.jdh.o2oservice.b2b.base.exception.DynamicErrorCode;
import com.jdh.o2oservice.b2b.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.b2b.domain.enterprisesku.rpc.O2oProductJsfExportRpc;
import com.jdh.o2oservice.b2b.domain.enterprisesku.rpc.bo.JdhSkuReqBo;
import com.jdh.o2oservice.b2b.domain.enterprisesku.rpc.bo.JdhSkuResBo;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.product.ProductJsfExport;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.query.JdhSkuRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class O2oProductJsfExportRpcImpl implements O2oProductJsfExportRpc {

    @Resource
    private ProductJsfExport productJsfExport;

    @Resource
    private ExecutorPoolFactory executorPoolFactory;

    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.infrastructure.rpc.ProductJsfExportRpcImpl.queryJdhSkuInfo")
    public JdhSkuResBo queryJdhSkuInfo(JdhSkuReqBo bo) {
        try{
            JdhSkuRequest param = new JdhSkuRequest();
            param.setSkuId(bo.getSkuId());
            param.setQuerySkuCoreData(bo.getQuerySkuCoreData());
            Response<JdhSkuDto> result = productJsfExport.queryJdhSkuInfo(param);
            log.info("ProductJsfExportRpcImpl queryJdhSkuInfo param={}, result={}", JSON.toJSONString(param), JSON.toJSONString(result));
            if(Objects.isNull(result)) {
                throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
            }
            if(Boolean.FALSE.equals(result.isSuccess())){
                throw new BusinessException(new DynamicErrorCode(result.getCode(), result.getMsg()));
            }
            return JSON.parseObject(JSON.toJSONString(result.getData()), JdhSkuResBo.class);
        } catch (BusinessException be) {
            log.error("ProductJsfExportRpcImpl -> queryJdhSkuInfo business exception", be);
            throw new BusinessException(be.getErrorCode());
        } catch (Exception e){
            log.error("ProductJsfExportRpcImpl queryJdhSkuInfo error e", e);
            throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
        }
    }

    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.infrastructure.rpc.ProductJsfExportRpcImpl.batchQueryJdhSkuInfo")
    public Map<Long,JdhSkuResBo> batchQueryJdhSkuInfo(List<Long> skuIds){
        try {
            List<CompletableFuture<JdhSkuResBo>> futureList = Collections.synchronizedList(Lists.newArrayList());
            skuIds.forEach(skuId->{
                CompletableFuture<JdhSkuResBo> future = CompletableFuture.supplyAsync(()->{
                    try {
                        JdhSkuReqBo bo = new JdhSkuReqBo();
                        bo.setSkuId(skuId);
                        bo.setQuerySkuCoreData(true);
                        return queryJdhSkuInfo(bo);
                    } catch (Exception e) {
                        log.error("ProductJsfExportRpcImpl batchQueryJdhSkuInfo supplyAsync error e:", e);
                    }
                    return null;
                }, executorPoolFactory.get(ThreadPoolConfigEnum.ENTERPRISE_SKU)).exceptionally(throwable -> {
                    log.error("ProductJsfExportRpcImpl batchQueryJdhSkuInfo completableFuture error:"+throwable.getMessage(),throwable);
                    return null;
                });
                futureList.add(future);
            });

            List<JdhSkuResBo> skuInfoList = new ArrayList<>();
            futureList.forEach(future->{
                JdhSkuResBo data = future.join();
                if (Objects.nonNull(data)){
                    skuInfoList.add(data);
                }
            });

            if (CollectionUtils.isNotEmpty(skuInfoList)){
                Map<Long, JdhSkuResBo> skuMap = skuInfoList.stream().collect(Collectors.toMap(JdhSkuResBo::getSkuId,
                        Function.identity(), (key1, key2) -> key2));
                log.info("ProductJsfExportRpcImpl batchQueryJdhSkuInfo skuMap={}", JSON.toJSONString(skuMap));
                return skuMap;
            }

        } catch (Exception e) {
            log.error("ProductJsfExportRpcImpl batchQueryJdhSkuInfo error e", e);
        }
        return Collections.emptyMap();
    }

}
