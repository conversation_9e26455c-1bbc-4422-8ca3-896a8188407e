package com.jdh.o2oservice.b2b.infrastructure.repository.db;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.b2b.base.enums.YnStatusEnum;
import com.jdh.o2oservice.b2b.domain.enterpriseuser.model.JdhB2bEnterpriseUser;
import com.jdh.o2oservice.b2b.domain.enterpriseuser.model.JdhEnterpriseUserIdentifier;
import com.jdh.o2oservice.b2b.domain.enterpriseuser.repository.JdhB2bEnterpriseUserRepository;
import com.jdh.o2oservice.b2b.domain.enterpriseuser.repository.query.JdhB2bEnterpriseUserQuery;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.convert.JdhB2bEnterpriseUserPoConverter;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.convert.JdhBasicPoConverter;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.mapper.JdhB2bEnterpriseUserMapper;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.po.JdhB2bEnterpriseUserPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 企业用户
 */
@Repository
@Slf4j
public class JdhB2bEnterpriseUserRepositoryImpl implements JdhB2bEnterpriseUserRepository {

    @Resource
    private JdhB2bEnterpriseUserMapper jdhB2bEnterpriseUserMapper;

    /**
     * 保存
     * @param entity
     * @return
     */
    @Override
    public int save(JdhB2bEnterpriseUser entity) {
        JdhB2bEnterpriseUserPo po = JdhB2bEnterpriseUserPo.builder().enterpriseId(entity.getEnterpriseId())
                .enterpriseUserId(entity.getEnterpriseUserId())
                .userPin(entity.getUserPin())
                .userName(entity.getUserName())
                .userRole(entity.getUserRole())
                .available(entity.getAvailable())
                .lastAvailableTime(entity.getLastAvailableTime())
                .extend(entity.getExtend())
                .createUser(entity.getOperator())
                .updateUser(entity.getOperator())
                .build();
        JdhBasicPoConverter.initInsertBasicPo(po);
        return jdhB2bEnterpriseUserMapper.insert(po);
    }

    /**
     * 更新
     * @param entity
     * @return
     */
    @Override
    public int update(JdhB2bEnterpriseUser entity) {
        LambdaUpdateWrapper<JdhB2bEnterpriseUserPo> updateWrapper = Wrappers.lambdaUpdate();
        JdhB2bEnterpriseUserPo po = JdhB2bEnterpriseUserPo.builder().build();
        updateWrapper.set(StringUtils.isNotBlank(entity.getUserName()), JdhB2bEnterpriseUserPo::getUserName, entity.getUserName())
                .set(StringUtils.isNotBlank(entity.getUserPin()), JdhB2bEnterpriseUserPo::getUserPin, entity.getUserPin())
                .set(entity.getAvailable() != null, JdhB2bEnterpriseUserPo::getAvailable, entity.getAvailable())
                .set(entity.getLastAvailableTime() != null, JdhB2bEnterpriseUserPo::getLastAvailableTime, entity.getLastAvailableTime())
                .setSql("`version` = version+1")
                .eq(JdhB2bEnterpriseUserPo::getEnterpriseUserId, entity.getEnterpriseUserId());
        Date now = new Date();
        po.setUpdateUser(entity.getOperator());
        po.setUpdateTime(now);
        return jdhB2bEnterpriseUserMapper.update(po, updateWrapper);
    }

    /**
     * 更新企业用户状态
     * @param entity
     * @return
     */
    @Override
    public int updateEnterpriseUserStatus(JdhB2bEnterpriseUser entity) {
        LambdaUpdateWrapper<JdhB2bEnterpriseUserPo> updateWrapper = Wrappers.lambdaUpdate();
        JdhB2bEnterpriseUserPo po = JdhB2bEnterpriseUserPo.builder().build();
        updateWrapper.set(JdhB2bEnterpriseUserPo::getAvailable, entity.getAvailable())
                .set(entity.getLastAvailableTime() != null, JdhB2bEnterpriseUserPo::getLastAvailableTime, entity.getLastAvailableTime())
                .setSql("`version` = version+1")
                .eq(JdhB2bEnterpriseUserPo::getEnterpriseUserId, entity.getEnterpriseUserId());
        Date now = new Date();
        po.setUpdateUser(entity.getUpdateUser());
        po.setUpdateTime(now);
        return jdhB2bEnterpriseUserMapper.update(po, updateWrapper);
    }

    /**
     * 删除
     * @param entity
     * @return
     */
    @Override
    public int delete(JdhB2bEnterpriseUser entity) {
        LambdaUpdateWrapper<JdhB2bEnterpriseUserPo> updateWrapper = Wrappers.lambdaUpdate();
        JdhB2bEnterpriseUserPo po = JdhB2bEnterpriseUserPo.builder().build();
        updateWrapper.set(JdhB2bEnterpriseUserPo::getYn, YnStatusEnum.NO.getCode())
                .eq(JdhB2bEnterpriseUserPo::getEnterpriseUserId, entity.getEnterpriseUserId());
        Date now = new Date();
        po.setUpdateUser(entity.getUpdateUser());
        po.setUpdateTime(now);
        return jdhB2bEnterpriseUserMapper.update(po, updateWrapper);
    }

    /**
     * 通过Identify 查询
     * @param identifier
     * @return
     */
    @Override
    public JdhB2bEnterpriseUser find(JdhEnterpriseUserIdentifier identifier) {
        Long enterpriseUserId = identifier.getEnterpriseUserId();
        LambdaQueryWrapper<JdhB2bEnterpriseUserPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhB2bEnterpriseUserPo::getEnterpriseUserId, enterpriseUserId)
                .eq(JdhB2bEnterpriseUserPo::getYn, YnStatusEnum.YES.getCode());
        JdhB2bEnterpriseUserPo jdhB2bEnterpriseUserPo = jdhB2bEnterpriseUserMapper.selectOne(queryWrapper);
        return JdhB2bEnterpriseUserPoConverter.INSTANCE.po2Entity(jdhB2bEnterpriseUserPo);
    }

    /**
     * 通过userPin 查询
     *
     * @param userPin
     * @return
     */
    @Override
    public JdhB2bEnterpriseUser findEnterpriseUserByPin(String userPin) {
        LambdaQueryWrapper<JdhB2bEnterpriseUserPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhB2bEnterpriseUserPo::getUserPin, userPin)
                .eq(JdhB2bEnterpriseUserPo::getYn, YnStatusEnum.YES.getCode());
        JdhB2bEnterpriseUserPo jdhB2bEnterpriseUserPo = jdhB2bEnterpriseUserMapper.selectOne(queryWrapper);
        return JdhB2bEnterpriseUserPoConverter.INSTANCE.po2Entity(jdhB2bEnterpriseUserPo);
    }

    /**
     * 分页查询企业用户列表
     * @param query
     * @return
     */
    @Override
    public Page<JdhB2bEnterpriseUser> queryPageEnterpriseUser(JdhB2bEnterpriseUserQuery query) {
        LambdaQueryWrapper<JdhB2bEnterpriseUserPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(query.getEnterpriseUserId() != null, JdhB2bEnterpriseUserPo::getEnterpriseUserId, query.getEnterpriseUserId())
                .eq(query.getEnterpriseId() != null, JdhB2bEnterpriseUserPo::getEnterpriseId, query.getEnterpriseId())
                .like(StringUtils.isNotBlank(query.getUserName()), JdhB2bEnterpriseUserPo::getUpdateTime, query.getUserName())
                .eq(StringUtils.isNotBlank(query.getUserPin()), JdhB2bEnterpriseUserPo::getUserPin, query.getUserPin())
                .eq(JdhB2bEnterpriseUserPo::getYn, YnStatusEnum.YES.getCode())
                .orderByDesc(JdhB2bEnterpriseUserPo::getCreateTime);
        Page<JdhB2bEnterpriseUserPo> param = new Page<>(query.getPageNum(), query.getPageSize());
        IPage<JdhB2bEnterpriseUserPo> iPage = jdhB2bEnterpriseUserMapper.selectPage(param, queryWrapper);
        List<JdhB2bEnterpriseUser> dataList = JdhB2bEnterpriseUserPoConverter.INSTANCE.po2Entity(iPage.getRecords());
        return JdhBasicPoConverter.initPage(iPage, dataList);
    }

    /**
     * 企业用户列表
     * @param query
     * @return
     */
    @Override
    public List<JdhB2bEnterpriseUser> findList(JdhB2bEnterpriseUserQuery query) {
        LambdaQueryWrapper<JdhB2bEnterpriseUserPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(query.getEnterpriseUserId() != null, JdhB2bEnterpriseUserPo::getEnterpriseUserId, query.getEnterpriseUserId())
                .eq(query.getEnterpriseId() != null, JdhB2bEnterpriseUserPo::getEnterpriseId, query.getEnterpriseId())
                .like(StringUtils.isNotBlank(query.getUserName()), JdhB2bEnterpriseUserPo::getUpdateTime, query.getUserName())
                .eq(StringUtils.isNotBlank(query.getUserPin()), JdhB2bEnterpriseUserPo::getUserPin, query.getUserPin())
                .eq(JdhB2bEnterpriseUserPo::getYn, YnStatusEnum.YES.getCode());
        List<JdhB2bEnterpriseUserPo> poList = jdhB2bEnterpriseUserMapper.selectList(queryWrapper);
        return JdhB2bEnterpriseUserPoConverter.INSTANCE.po2Entity(poList);
    }
}
