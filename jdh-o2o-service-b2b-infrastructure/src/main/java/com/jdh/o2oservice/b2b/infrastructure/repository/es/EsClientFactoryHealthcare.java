package com.jdh.o2oservice.b2b.infrastructure.repository.es;
import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.b2b.base.exception.BusinessErrorCode;
import com.jdh.o2oservice.b2b.base.exception.BusinessException;
import com.jdh.o2oservice.b2b.infrastructure.repository.es.convert.EsBeanUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.elasticsearch.action.bulk.BulkRequestBuilder;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.delete.DeleteResponse;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.action.update.UpdateResponse;
import org.elasticsearch.client.transport.TransportClient;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.common.transport.TransportAddress;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.transport.client.PreBuiltTransportClient;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.nio.ByteBuffer;
import java.nio.CharBuffer;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> limeng363
 * @Date : 2024-05-07 18:42
 * @Desc : elasticsearch公共类 -集群：jiesi-healthcare-consumer
 */
@Slf4j
@Data
@Component
@ConfigurationProperties(prefix = "elasticsearchfactory")
public class EsClientFactoryHealthcare implements InitializingBean, DisposableBean {
    /** ES 链接超时 **/
    public static final Long TIMEOUT_MILLIS = 2000L;
    /**
     * es 批量操作超时时长
     */
    private final long BACH_TIMEOUT_MILLIS = 10000L;
    /**
     * ES集群名称
     **/
    private String clusterName;

    /**
     * securityKey
     */
    private String securityKey = "request.headers.Authorization";
    /**
     * securityUser
     */
    private String securityUser;
    /**
     * securityPassword
     */
    private String securityPassword;
    /**
     * client
     */
     private volatile static TransportClient client = null;
    /**
     * Node IP:prot,IP:prot,IP:prot ES节点地址集合
     **/
    private String nodeConnectionAddress;

    private EsClientFactoryHealthcare() {}

    public TransportClient getClient() {
        return client;
    }

    @Override
    public void destroy() throws Exception {
        synchronized (EsClientFactoryHealthcare.class) {
            if (client != null) {
                client.close();
            }
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        try {
            log.info("EsClientFactoryHealthcare afterPropertiesSet clusterName={},securityUser={},securityPassword={},nodeConnectionAddress={}",clusterName, securityUser,securityPassword,nodeConnectionAddress);
            if (StringUtils.isBlank(clusterName) || StringUtils.isBlank(nodeConnectionAddress)) {
                log.error("EsClientFactorySupplychain.init.is.error:clusterName is null or nodeConnectionAddress is null");
                throw new RuntimeException("EsClientFactorySupplychain.init.is.error");
            }
            if (null == client) {
                synchronized (EsClientFactoryHealthcare.class) {
                    log.info("EsClientFactoryHealthcare afterPropertiesSet start");
                    if (null == client) {
                        // Settings settings = Settings.settingsBuilder()
                        Settings settings = Settings.builder().put("cluster.name", clusterName).put("client.transport.sniff", false).put("transport.netty.worker_count", 8)
                                .put(securityKey, basicAuthHeaderValue(securityUser, securityPassword))
                                // .put("transport.type","netty3")
                                // .put("http.type", "netty3")
                                .build();
                        System.setProperty("es.set.netty.runtime.available.processors", "false");
                        // client = TransportClient.builder().settings(settings).build();
                        log.info("EsClientFactoryHealthcare afterPropertiesSet settings finsh");
                        client = new PreBuiltTransportClient(settings);
                        String[] addressArr = nodeConnectionAddress.split(";");// 逗号分隔
                        log.info("EsClientFactoryHealthcare afterPropertiesSet addressArr={}", JSON.toJSONString(addressArr));
                        for (String ipPort : addressArr) {
                            String[] ipPortArr = ipPort.split(":");// 冒号分隔
                            log.info("EsClientFactoryHealthcare afterPropertiesSet ipPortArr={}", JSON.toJSONString(ipPortArr));
                            if (ipPortArr.length == 2) {
                                client.addTransportAddress(new TransportAddress(InetAddress.getByName(ipPortArr[0]), Integer.parseInt(ipPortArr[1])));
                            }
                        }
                        log.info("EsClientFactoryHealthcare afterPropertiesSet synchronized success end");
                    }
                }
            }
            log.info("EsClientFactoryHealthcare afterPropertiesSet success end");
        } catch (Exception e) {
            log.error("EsClientFactoryHealthcare afterPropertiesSet error e", e);
        }
    }
    /**
     * 基础的base64生成
     *
     * @param username
     *            用户名
     * @param passwd
     *            密码
     * @return
     */
    private static String basicAuthHeaderValue(String username, String passwd) {
        CharBuffer chars = CharBuffer.allocate(username.length() + passwd.length() + 1);
        byte[] charBytes = null;
        try {
            chars.put(username).put(':').put(passwd.toCharArray());
            charBytes = toUtf8Bytes(chars.array());

            String basicToken = Base64.getEncoder().encodeToString(charBytes);
            return "Basic " + basicToken;
        } finally {
            Arrays.fill(chars.array(), (char)0);
            if (charBytes != null) {
                Arrays.fill(charBytes, (byte)0);
            }
        }
    }

    /**
     * @param chars
     * @return
     */
    public static byte[] toUtf8Bytes(char[] chars) {
        CharBuffer charBuffer = CharBuffer.wrap(chars);
        ByteBuffer byteBuffer = StandardCharsets.UTF_8.encode(charBuffer);
        byte[] bytes = Arrays.copyOfRange(byteBuffer.array(), byteBuffer.position(), byteBuffer.limit());
        Arrays.fill(byteBuffer.array(), (byte)0);
        return bytes;
    }
    /**
     * 表里有就更新，没有就插入
     *
     * @param indexName
     * @param typeName
     * @param docId
     *            不能为空
     * @param parameter
     *            属性名严格和表字段名一一对应
     * @return 返回值为空，代表异常
     */
    public UpdateResponse updateOrInsertDocument(String indexName, String typeName, String docId, Object parameter) throws Exception {
        try {
            /** 公共参数检验 **/
            commonCheck(indexName, typeName);

            if (StringUtils.isBlank(docId)) {
                log.error("updateOrInsertDocument#docId.is.null");
                throw new Exception("docId.is.null");
            }
            Map<String, ?> mapObj = EsBeanUtils.makeObjectToMap(parameter);
            UpdateResponse response = getClient().prepareUpdate(indexName, typeName, docId).setDocAsUpsert(true).setDoc(mapObj).setRetryOnConflict(50).get();
            log.info("updateOrInsertDocument response:{}",JSON.toJSONString(response));
            if (null == response) {
                log.error("updateOrInsertDocument#updateDocument.is.error:");
                throw new Exception("updateDocument.response.is.null");
            }
            return response;
        } catch (Exception e) {
            log.error("updateOrInsertDocument#updateDocument() ES更新数据失败，参数：docId：{}，parameter：{}", docId, JSON.toJSONString(parameter), e);
            throw e;
        }
    }
    /**
     * 完全覆盖相同docId文档，如果没有相同docId就新增
     *
     * 注：和update不一样
     * @param indexName
     * @param typeName
     * @param docId
     *            不能为空
     * @param parameter
     *            属性名严格和表字段名一一对应
     * @return 返回值为空，代表异常
     */
    public boolean index(String indexName, String typeName, String docId, Object parameter) throws Exception {
//        CallerInfo callerInfo = ProfilerProxy.registerInfo(this.getClass().getName() + ".index", UmpKeyConst.APP_NAME, false, true);
        try {
            /** 公共参数检验 **/
            commonCheck(indexName, typeName);

            if (StringUtils.isBlank(docId)) {
                log.error("docId.is.null");
                throw new Exception("docId.is.null");
            }
            Map<String, ?> mapObj = EsBeanUtils.makeObjectToMap(parameter);
            IndexResponse response = client.prepareIndex(indexName, typeName, docId).setSource(mapObj).setTimeout(TimeValue.timeValueMillis(10000)).get();
            if (null == response) {
                log.error("updateDocument.is.error:");
                throw new Exception("updateDocument.response.is.null");
            }
            return true;
        } catch (Exception e) {
            log.error("#index() ES index数据失败，参数：docId：{}，parameter：{}", docId, JSON.toJSONString(parameter));
            log.error("", e);
            //Profiler.functionError(callerInfo);
            throw e;
        } finally {
            //Profiler.registerInfoEnd(callerInfo);
        }
    }
    /**
     * 根据文档ID 删除文档信息
     *
     * @param indexName
     * @param typeName
     * @param docId
     * @return
     */
    public DeleteResponse deleteDocument(String indexName, String typeName, String docId) throws Exception {
//        CallerInfo callerInfo = ProfilerProxy.registerInfo(this.getClass().getName() + ".deleteDocument", UmpKeyConst.APP_NAME, false, true);

        try {
            /** 公共参数检验 **/
            commonCheck(indexName, typeName);

            if (StringUtils.isBlank(docId)) {
                log.error("EsClientFactorySupplychain.deleteDocument.docId.is.null");
                throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR,"EsClientFactorySupplychain.deleteDocument.docId.is.null");
            }
            DeleteResponse dResponse = getClient().prepareDelete(indexName, typeName, docId).execute().actionGet(TIMEOUT_MILLIS);
            // response中返回索引名称，type名称，doc的Id和版本信息
            if (null == dResponse) {
                log.error("EsClientFactorySupplychain.deleteDocument.is.error:");
                throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR,"EsClientFactorySupplychain.deleteDocument.response.is.null");
            }
            return dResponse;
        } catch (Exception e) {
            log.error("EsClientFactorySupplychain#deleteDocument() deleteDocument失败", e);
            //Profiler.functionError(callerInfo);
            throw e;
        } finally {
            //Profiler.registerInfoEnd(callerInfo);
        }
    }
    /**
     * 根据文档ID 批量删除文档信息
     *
     * @param indexName
     * @param typeName
     * @param docIdList
     * @return Boolean
     */
    public Boolean deleteBatch(String indexName, String typeName, List<String> docIdList) throws Exception {
//        CallerInfo callerInfo = ProfilerProxy.registerInfo(this.getClass().getName() + ".deleteDocument", UmpKeyConst.APP_NAME, false, true);
        try {
            /** 公共参数检验 **/
            commonCheck(indexName, typeName);
            if (CollectionUtils.isEmpty(docIdList)) {
                log.error("EsClientFactorySupplychain.batchDelete.docIdList.is.null {}", indexName);
                throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR,"EsClientFactorySupplychain.batchDelete.docIdList.is.null");
            }
            BulkRequestBuilder bulkRequest = client.prepareBulk();
            for (String id : docIdList) {
                bulkRequest.add(client.prepareDelete(indexName, typeName, id));
            }
            BulkResponse bulkResponse = bulkRequest.execute().actionGet(BACH_TIMEOUT_MILLIS);
            if (null == bulkResponse) {
                log.error("EsClientFactorySupplychain.batchDelete.response.is.null {}", indexName);
                throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR,"EsClientFactorySupplychain.batchDelete.response.is.null");
            }
            if (bulkResponse.hasFailures()) {
                log.error("EsClientFactorySupplychain.batchDelete.is.failure:{}", bulkResponse.buildFailureMessage());
            }
            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("EsClientFactorySupplychain#{}#batchDelete() batchDelete失败，参数：{}", indexName, JSON.toJSONString(docIdList), e);
            //Profiler.functionError(callerInfo);
        } finally {
            //Profiler.registerInfoEnd(callerInfo);
        }
        return Boolean.FALSE;
    }
    /**
     *
     * @param indexName
     * @param typeName
     * @param docId
     * @param parameter
     * @return
     */
    public UpdateResponse updateDocument(String indexName, String typeName, String docId, Object parameter) {

//        CallerInfo callerInfo = ProfilerProxy.registerInfo(this.getClass().getName() + ".updateDocument" , UmpKeyConst.APP_NAME, false, true);
        try {
            /** 公共参数检验 **/
            commonCheck(indexName, typeName);

            if (StringUtils.isBlank(docId)) {
                log.error("{},EsClientFactorySupplychain.updateDocument.docId.is.null", indexName);
                throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR,"EsClientFactorySupplychain.updateDocument.docId.is.null");
            }
            UpdateRequest updateRequest = null;
            if (parameter instanceof Map) {
                updateRequest = new UpdateRequest().index(indexName).type(typeName).id(docId);
                updateRequest.doc((Map<String, ?>)parameter);
            } else if (parameter instanceof String) {
                updateRequest = new UpdateRequest().index(indexName).type(typeName).id(docId);
                updateRequest.doc(JSON.parseObject((String)parameter, Map.class));
            } else {
                updateRequest = new UpdateRequest().index(indexName).type(typeName).id(docId);
                updateRequest.doc(JSON.parseObject(EsBeanUtils.makeObjectToJson(parameter), Map.class));
            }
            UpdateResponse response = client.update(updateRequest).get(TIMEOUT_MILLIS, TimeUnit.MILLISECONDS);
            if (null == response) {
                log.error("{}, EsClientFactorySupplychain.updateDocument.is.error:", indexName);
                throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR,"EsClientFactorySupplychain.updateDocument.response.is.null");
            }
            return response;
        } catch (Exception e) {
            log.error("#updateDocument() ES更新数据失败，参数：{}, docId：{}，parameter：{}", indexName, docId, JSON.toJSONString(parameter), e);
            //Profiler.functionError(callerInfo);
            return null;
        } finally {
            //Profiler.registerInfoEnd(callerInfo);
        }
    }
    /**
     * 根据文档ID 获取文档内容
     *
     * @param indexName
     *            索引URL
     * @param typeName
     *            类型
     * @param docId
     * @return
     * @throws Exception
     */
    public GetResponse getDocument(String indexName, String typeName, String docId) throws Exception {
//        CallerInfo callerInfo = ProfilerProxy.registerInfo(this.getClass().getName() + ".getDocument", UmpKeyConst.APP_NAME, false, true);
        try {
            /** 公共参数检验 **/
            commonCheck(indexName, typeName);

            if (StringUtils.isBlank(docId)) {
                log.error("EsClientFactorySupplychain.getDocument.docId.is.null");
                throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR,"EsClientFactorySupplychain.getDocument.docId.is.null");
            }

            GetResponse gResponse = client.prepareGet(indexName, typeName, docId).execute().actionGet(TIMEOUT_MILLIS);

            // response中返回索引名称，type名称，doc的Id和版本信息
            if (null == gResponse) {
                log.error("EsClientFactorySupplychain.getDocument.is.error:");
                throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR,"EsClientFactorySupplychain.getDocument.response.is.null");
            }

            return gResponse;
        } catch (Exception e) {
            log.error("#getDocument() elasticsearch查询失败", e);
            //Profiler.functionError(callerInfo);
        } finally {
            //Profiler.registerInfoEnd(callerInfo);
        }
        return null;
    }

    /**
     * @param indexName
     * @param typeName
     */
    private void commonCheck(String indexName, String typeName) throws BusinessException {
        if (null == client) {
            log.error("EsClientFactorySupplychain.client.is.null");
            throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR,"EsClientFactorySupplychain.client.is.null");
        }
        if (StringUtils.isBlank(indexName)) {
            log.error("EsClientFactorySupplychain.indexName.is.null");
            throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR,"EsClientFactorySupplychain.indexName.is.null");
        }
        if (StringUtils.isBlank(typeName)) {
            log.error("EsClientFactorySupplychain.typeName.is.null");
            throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR,"EsClientFactorySupplychain.typeName.is.null");
        }
    }

}
