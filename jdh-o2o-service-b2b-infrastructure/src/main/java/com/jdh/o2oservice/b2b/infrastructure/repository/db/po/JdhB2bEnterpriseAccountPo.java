package com.jdh.o2oservice.b2b.infrastructure.repository.db.po;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;
import java.util.Date;
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "jdh_b2b_enterprise_account",autoResultMap = true)
public class JdhB2bEnterpriseAccountPo extends JdhBasicPo{

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 账户Id
     */
    private Long accountId;

    /**
     * 信用额度
     */
    private BigDecimal creditAmount;

    /**
     * 冻结金额
     */
    private BigDecimal freezeAmount;

    /**
     * 扩展信息
     */
    private String extend;

    /**
     * 是否有效 1有效 0无效
     */
    private Integer yn;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 数据来源分支
     */
    private String branch;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 更新时间
     */
    private Date updateTime;

}