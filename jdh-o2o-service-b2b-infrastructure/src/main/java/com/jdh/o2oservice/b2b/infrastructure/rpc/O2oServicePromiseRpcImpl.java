package com.jdh.o2oservice.b2b.infrastructure.rpc;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.b2b.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.b2b.base.exception.BusinessErrorCode;
import com.jdh.o2oservice.b2b.base.exception.BusinessException;
import com.jdh.o2oservice.b2b.base.exception.DynamicErrorCode;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.*;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.ctx.SubmitPromiseCtx;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.rpc.O2oServicePromiseRpc;
import com.jdh.o2oservice.b2b.infrastructure.rpc.converter.O2oServicePromiseRpcConvert;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.promise.PromiseJsfExport;
import com.jdh.o2oservice.export.promise.cmd.CreateVoucherCmd;
import com.jdh.o2oservice.export.promise.cmd.InvalidVoucherCmd;
import com.jdh.o2oservice.export.promise.cmd.SubmitPromiseCmd;
import com.jdh.o2oservice.export.promise.dto.CompletePromiseDto;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import com.jdh.o2oservice.export.promise.query.CompletePromiseRequest;
import com.jdh.o2oservice.export.promise.query.PromiseIdRequest;
import com.jdh.o2oservice.export.promise.query.PromiseListRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * O2oServicePromiseRpcImpl
 *
 * <AUTHOR>
 * @date 2025/02/27
 */
@Slf4j
@Service
public class O2oServicePromiseRpcImpl implements O2oServicePromiseRpc {

    /**
     * promiseJsfExport
     */
    @Resource
    private PromiseJsfExport promiseJsfExport;


    /**
     * 创建voucher promise  同步
     *
     * @param ctx ctx
     * @return {@link O2oPromiseBo }
     */
    @Override
    public List<O2oPromiseBo> syncCreateVoucher(SubmitPromiseCtx ctx) {
        try {
            CreateVoucherCmd createVoucherCmd = O2oServicePromiseRpcConvert.INS.ctx2CreateVoucherCmd(ctx);
            log.info("O2oServicePromiseRpcImpl -> syncCreateVoucher createVoucherCmd:{}", JSON.toJSONString(createVoucherCmd));
            Response<List<PromiseDto>> response = promiseJsfExport.createVoucher(createVoucherCmd);
            log.info("O2oServicePromiseRpcImpl -> syncCreateVoucher response:{}", JSON.toJSONString(response));
            if(Objects.isNull(response)) {
                throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
            }
            if(Boolean.FALSE.equals(response.isSuccess())){
                throw new BusinessException(new DynamicErrorCode(response.getCode(), response.getMsg()));
            }
            List<PromiseDto> data = response.getData();
            if(CollUtil.isEmpty(data)) {
                throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
            }
            return O2oServicePromiseRpcConvert.INS.toO2oPromiseBo(data);
        }catch (BusinessException be) {
            log.error("O2oServicePromiseRpcImpl -> syncCreateVoucher business exception", be);
            throw be;
        }catch (Exception e) {
            log.error("O2oServicePromiseRpcImpl -> syncCreateVoucher exception", e);
            throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
        }
    }


    /**
     * 提交预约
     *
     * @param ctx ctx
     * @return {@link Boolean }
     */
    @Override
    public Boolean submitAppointment(SubmitPromiseCtx ctx) {
        try {
            SubmitPromiseCmd cmd = O2oServicePromiseRpcConvert.INS.ctx2SubmitCmd(ctx);
            log.info("O2oServicePromiseRpcImpl -> submitAppointment cmd:{}", JSON.toJSONString(cmd));
            Response<Boolean> response = promiseJsfExport.submit(cmd);
            log.info("O2oServicePromiseRpcImpl -> submitAppointment response:{}", JSON.toJSONString(response));
            if(Objects.isNull(response)) {
                throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
            }
            if(Boolean.FALSE.equals(response.isSuccess())){
                throw new BusinessException(new DynamicErrorCode(response.getCode(), response.getMsg()));
            }
            return response.getData();
        } catch (BusinessException be) {
            log.error("O2oServicePromiseRpcImpl -> submitAppointment business exception", be);
            throw new BusinessException(be.getErrorCode());
        } catch (Exception e) {
            log.error("O2oServicePromiseRpcImpl -> submitAppointment exception", e);
            throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
        }
    }

    /**
     * getPromiseInfo
     *
     * @param promiseId promiseId
     * @return {@link O2oPromiseBo }
     */
    @Override
    public O2oPromiseBo getPromiseInfo(Long promiseId) {
        try {
            Response<PromiseDto> response = promiseJsfExport.queryPromise(PromiseIdRequest.builder()
                    .promiseId(promiseId).build());
            log.info("O2oServicePromiseRpcImpl -> getPromiseInfo response:{}", JSON.toJSONString(response));
            if(Objects.isNull(response)) {
                throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
            }
            if(Boolean.FALSE.equals(response.isSuccess())){
                throw new BusinessException(new DynamicErrorCode(response.getCode(), response.getMsg()));
            }
            return O2oServicePromiseRpcConvert.INS.dto2Bo(response.getData());
        } catch (BusinessException be) {
            log.error("O2oServicePromiseRpcImpl -> getPromiseInfo business exception", be);
            throw new BusinessException(be.getErrorCode());
        } catch (Exception e) {
            log.error("O2oServicePromiseRpcImpl -> getPromiseInfo exception", e);
            throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
        }
    }

    /**
     * invalidO2oVoucherBo
     *
     * @param invalidO2oVoucherBo invalidO2oVoucherBo
     * @return {@link Boolean }
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.infrastructure.rpc.O2oServicePromiseRpcImpl.invalidVoucher")
    public Boolean invalidVoucher(InvalidO2oVoucherBo invalidO2oVoucherBo) {
        try {
            log.info("O2oServicePromiseRpcImpl -> invalidVoucher invalidO2oVoucherBo:{}", JSON.toJSONString(invalidO2oVoucherBo));
            Response<Boolean> response = promiseJsfExport.invalidVoucher(InvalidVoucherCmd.builder()
                    .voucherId(invalidO2oVoucherBo.getVoucherId())
                    .invalidType(invalidO2oVoucherBo.getInvalidType())
                    .reason(invalidO2oVoucherBo.getReason())
                    .build());
            log.info("O2oServicePromiseRpcImpl -> invalidVoucher response:{}", JSON.toJSONString(response));
            if(Objects.isNull(response)) {
                throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
            }
            if(Boolean.FALSE.equals(response.isSuccess())){
                throw new BusinessException(new DynamicErrorCode(response.getCode(), response.getMsg()));
            }
            return response.getData();
        } catch (BusinessException be) {
            log.error("O2oServicePromiseRpcImpl -> invalidVoucher business exception", be);
            throw new BusinessException(be.getErrorCode());
        } catch (Exception e) {
            log.error("O2oServicePromiseRpcImpl -> invalidVoucher exception", e);
            throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
        }
    }

    /**
     * 查询履约单
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.infrastructure.rpc.O2oServicePromiseRpcImpl.findByPromiseList")
    public List<O2oPromiseBo> findByPromiseList(O2oPromiseListRequestBo request) {
        try {
            PromiseListRequest promiseListRequest = PromiseListRequest.builder()
                    .voucherIds(request.getVoucherIds())
                    .build();
            log.info("O2oServicePromiseRpcImpl -> findByPromiseList promiseListRequest:{}", JSON.toJSONString(promiseListRequest));
            Response<List<PromiseDto>> response = promiseJsfExport.findByPromiseList(promiseListRequest);
            log.info("O2oServicePromiseRpcImpl -> findByPromiseList response:{}", JSON.toJSONString(response));
            if(Objects.isNull(response)) {
                throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
            }
            if(Boolean.FALSE.equals(response.isSuccess())){
                throw new BusinessException(new DynamicErrorCode(response.getCode(), response.getMsg()));
            }
            return JSON.parseArray(JSON.toJSONString(response.getData()), O2oPromiseBo.class);
        } catch (BusinessException be) {
            log.error("O2oServicePromiseRpcImpl -> findByPromiseList business exception", be);
            throw new BusinessException(be.getErrorCode());
        } catch (Exception e) {
            log.error("O2oServicePromiseRpcImpl -> findByPromiseList exception", e);
            throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
        }
    }

    @Override
    @LogAndAlarm
    public List<CompletePromiseBo> queryCompletePromiseList(CompletePromiseRequestBo request) {
        try {
            CompletePromiseRequest completePromiseRequest = new CompletePromiseRequest();
            completePromiseRequest.setPromiseIds(request.getPromiseIds());
            log.info("O2oServicePromiseRpcImpl -> queryCompletePromiseList promiseListRequest:{}", JSON.toJSONString(completePromiseRequest));
            Response<List<CompletePromiseDto>> response = promiseJsfExport.queryCompletePromiseList(completePromiseRequest);
            log.info("O2oServicePromiseRpcImpl -> queryCompletePromiseList response:{}", JSON.toJSONString(response));
            if(Objects.isNull(response)) {
                throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
            }
            if(Boolean.FALSE.equals(response.isSuccess())){
                throw new BusinessException(new DynamicErrorCode(response.getCode(), response.getMsg()));
            }
            return O2oServicePromiseRpcConvert.INS.convertCompletePromiseDtoList(response.getData());
        } catch (BusinessException be) {
            log.error("O2oServicePromiseRpcImpl -> findByPromiseList business exception", be);
            throw new BusinessException(be.getErrorCode());
        } catch (Exception e) {
            log.error("O2oServicePromiseRpcImpl -> findByPromiseList exception", e);
            throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
        }
    }

}
