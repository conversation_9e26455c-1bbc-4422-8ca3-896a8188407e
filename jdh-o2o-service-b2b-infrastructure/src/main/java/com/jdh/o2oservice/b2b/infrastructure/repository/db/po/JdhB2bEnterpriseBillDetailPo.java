package com.jdh.o2oservice.b2b.infrastructure.repository.db.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName:JdhB2bEnterpriseBillDetailPo
 * @Description:B2b企业账单明细
 * @Author: liwenming
 * @Date: 2025/2/24 15:14
 * @Vserion: 1.0
 **/
@Data
@TableName(value = "jdh_b2b_enterprise_bill_detail",autoResultMap = true)
public class JdhB2bEnterpriseBillDetailPo {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 账单明细Id
     */
    private Long billDetailId;
    /**
     * 账单Id
     */
    private Long billId;
    /**
     * 企业Id
     */
    private Long enterpriseId;
    /**
     * 企业服务单Id
     */
    private Long enterpriseVoucherId;
    /**
     * 结算金额
     */
    private BigDecimal settlementAmount;

    /**
     * 状态 1-正常 2-已取消(无责) 3-已取消(有责) 4-作废
     */
    private Integer status;
    /**
     * 履约单id
     */
    private Long promiseId;
    /**
     * sku名称
     */
    private String skuName;
    /**
     * sku短名称
     */
    private String skuShortName;
    /**
     * 履约单创建时间
     */
    private Date orderTime;

    /**
     * 扩展信息
     */
    private String extend;

    /**
     * 版本号
     */
    private Integer version;
    /**
     * 数据来源分支
     */
    private String branch;

    /**
     * 是否有效 1有效 0 无效
     */
    private Integer yn;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改人
     */
    private String updateUser;
    /**
     * 修改时间
     */
    private Date updateTime;
}
