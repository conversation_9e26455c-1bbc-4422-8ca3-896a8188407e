package com.jdh.o2oservice.b2b.infrastructure.rpc.converter;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.FileUrlBo;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.PromiseTimelineBo;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.QueryPromiseTimelineBo;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.man.*;
import com.jdh.o2oservice.export.angel.dto.AngelStationDto;
import com.jdh.o2oservice.export.angel.dto.JdhAngelDetailDto;
import com.jdh.o2oservice.export.angel.dto.JdhStationDto;
import com.jdh.o2oservice.export.dispatch.dto.DispatchTargetStatusDto;
import com.jdh.o2oservice.export.dispatch.dto.JdhDispatchForManDTO;
import com.jdh.o2oservice.export.product.dto.AgencyAppointDateDto;
import com.jdh.o2oservice.export.promise.dto.PromiseTimelineDetailDto;
import com.jdh.o2oservice.export.promise.dto.PromiseTimelineDto;
import com.jdh.o2oservice.export.promise.query.QueryPromiseTimelineRequest;
import com.jdh.o2oservice.export.support.dto.CallRecordDto;
import com.jdh.o2oservice.export.support.dto.FileUrlDto;
import com.jdh.o2oservice.export.support.query.GetFileUrlRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Description ManServicePromiseRpcConvert
 * @Date 2025/3/4 下午6:31
 * <AUTHOR>
 **/
@Mapper
public interface ManServicePromiseRpcConvert {

    ManServicePromiseRpcConvert INS = Mappers.getMapper(ManServicePromiseRpcConvert.class);

    List<CallRecordBo> toCallRecordBOList(List<CallRecordDto> callRecordDtoList);

    List<AgencyAppointDateBo> toAgencyAppointDateBOList(List<AgencyAppointDateDto> appointDateDtoList);

    DispatchTargetStatusBo toDispatchTargetStatusBo(DispatchTargetStatusDto dispatchTargetStatusDto);

    JdhAngelDetailBo toJdhAngelDetailBo(JdhAngelDetailDto jdhAngelDetailDto);

    List<JdhDispatchForManBo> toJdhDispatchForManBoList(List<JdhDispatchForManDTO> dispatchForManDTO);

    List<JdhStationBo> toJdhStationDtoList(List<JdhStationDto> jdhStationDtos);

    List<AngelStationBo> toAngelStationBoList(List<AngelStationDto> angelStationDtos);

    GetFileUrlRequest toGetFileUrlRequest(GetFileUrlBo bo);

    List<FileUrlBo> toFileUrlBo(List<FileUrlDto> dtos);

    QueryPromiseTimelineRequest toQueryPromiseTimelineRequest(QueryPromiseTimelineBo bo);

    @Mappings({
            @Mapping(source = "promiseTimelineDetailDtoList", target = "promiseTimelineDetailBoList"),
    })
    PromiseTimelineBo toPromiseTimelineBo(PromiseTimelineDto dto);

    @Mappings({
            @Mapping(source = "actionName", target = "actionEnum.getDesc()"),
    })
    PromiseTimelineDetailBo toPromiseTimelineDetailBo(PromiseTimelineDetailDto dto);


}
