package com.jdh.o2oservice.b2b.infrastructure.rpc.converter;

import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.FileUrlBo;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.PromiseTimelineBo;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.QueryPromiseTimelineBo;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.man.*;
import com.jdh.o2oservice.export.angel.dto.AngelStationDto;
import com.jdh.o2oservice.export.angel.dto.JdhAngelDetailDto;
import com.jdh.o2oservice.export.angel.dto.JdhStationDto;
import com.jdh.o2oservice.export.dispatch.dto.DispatchTargetStatusDto;
import com.jdh.o2oservice.export.dispatch.dto.JdhDispatchForManDTO;
import com.jdh.o2oservice.export.product.dto.AgencyAppointDateDto;
import com.jdh.o2oservice.export.promise.dto.PromiseTimelineDetailDto;
import com.jdh.o2oservice.export.promise.dto.PromiseTimelineDto;
import com.jdh.o2oservice.export.promise.query.QueryPromiseTimelineRequest;
import com.jdh.o2oservice.export.support.dto.CallRecordDto;
import com.jdh.o2oservice.export.support.dto.FileUrlDto;
import com.jdh.o2oservice.export.support.query.GetFileUrlRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 运营服务履约RPC转换器
 *
 * <p>负责在DTO和BO之间进行数据转换，主要用于：</p>
 * <ul>
 *   <li>外呼记录数据转换</li>
 *   <li>预约时间数据转换</li>
 *   <li>派单相关数据转换</li>
 *   <li>服务者信息转换</li>
 *   <li>文件URL转换</li>
 *   <li>履约时间线转换</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 2025/3/4
 */
@Mapper
public interface ManServicePromiseRpcConvert {

    /**
     * 转换器实例
     */
    ManServicePromiseRpcConvert INS = Mappers.getMapper(ManServicePromiseRpcConvert.class);

    /**
     * 转换外呼记录列表
     *
     * @param callRecordDtoList 外呼记录DTO列表
     * @return 外呼记录BO列表
     */
    List<CallRecordBo> toCallRecordBOList(List<CallRecordDto> callRecordDtoList);

    /**
     * 转换预约时间列表
     *
     * @param appointDateDtoList 预约时间DTO列表
     * @return 预约时间BO列表
     */
    List<AgencyAppointDateBo> toAgencyAppointDateBOList(List<AgencyAppointDateDto> appointDateDtoList);

    /**
     * 转换派单目标状态
     *
     * @param dispatchTargetStatusDto 派单目标状态DTO
     * @return 派单目标状态BO
     */
    DispatchTargetStatusBo toDispatchTargetStatusBo(DispatchTargetStatusDto dispatchTargetStatusDto);

    /**
     * 转换服务者详情
     *
     * @param jdhAngelDetailDto 服务者详情DTO
     * @return 服务者详情BO
     */
    JdhAngelDetailBo toJdhAngelDetailBo(JdhAngelDetailDto jdhAngelDetailDto);

    /**
     * 转换运营端派单明细列表
     *
     * @param dispatchForManDTO 运营端派单明细DTO列表
     * @return 运营端派单明细BO列表
     */
    List<JdhDispatchForManBo> toJdhDispatchForManBoList(List<JdhDispatchForManDTO> dispatchForManDTO);

    /**
     * 转换服务站列表
     *
     * @param jdhStationDtos 服务站DTO列表
     * @return 服务站BO列表
     */
    List<JdhStationBo> toJdhStationBoList(List<JdhStationDto> jdhStationDtos);

    /**
     * 转换服务者站点列表
     *
     * @param angelStationDtos 服务者站点DTO列表
     * @return 服务者站点BO列表
     */
    List<AngelStationBo> toAngelStationBoList(List<AngelStationDto> angelStationDtos);

    /**
     * 转换文件URL请求
     *
     * @param bo 文件URL请求BO
     * @return 文件URL请求对象
     */
    GetFileUrlRequest toGetFileUrlRequest(GetFileUrlBo bo);

    /**
     * 转换文件URL列表
     *
     * @param dtos 文件URL DTO列表
     * @return 文件URL BO列表
     */
    List<FileUrlBo> toFileUrlBo(List<FileUrlDto> dtos);

    /**
     * 转换履约时间线查询请求
     *
     * @param bo 履约时间线查询BO
     * @return 履约时间线查询请求对象
     */
    QueryPromiseTimelineRequest toQueryPromiseTimelineRequest(QueryPromiseTimelineBo bo);

    /**
     * 转换履约时间线
     *
     * @param dto 履约时间线DTO
     * @return 履约时间线BO
     */
    @Mapping(source = "promiseTimelineDetailDtoList", target = "promiseTimelineDetailBoList")
    PromiseTimelineBo toPromiseTimelineBo(PromiseTimelineDto dto);

    /**
     * 转换履约时间线详情
     *
     * <p>直接映射所有字段，无需特殊处理</p>
     *
     * @param dto 履约时间线详情DTO
     * @return 履约时间线详情BO
     */
    @Mapping(source = "actionEnum.getDesc()", target = "actionName")
    PromiseTimelineDetailBo toPromiseTimelineDetailBo(PromiseTimelineDetailDto dto);

}
