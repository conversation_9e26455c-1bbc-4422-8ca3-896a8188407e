package com.jdh.o2oservice.b2b.infrastructure.repository.db.convert;

import com.jdh.o2oservice.b2b.domain.enterprise.model.JdhB2bEnterpriseContract;
import com.jdh.o2oservice.b2b.infrastructure.repository.db.po.JdhB2bEnterpriseContractPo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Description JdhB2bEnterpriseContractConverter
 * @Date 2025/2/25 上午11:06
 * <AUTHOR>
 **/
@Mapper
public interface JdhB2bEnterpriseContractConverter {

    JdhB2bEnterpriseContractConverter INSTANCE = Mappers.getMapper(JdhB2bEnterpriseContractConverter.class);

    JdhB2bEnterpriseContract po2Entity(JdhB2bEnterpriseContractPo jdhB2bEnterpriseContractPo);

    List<JdhB2bEnterpriseContract> po2EntityList(List<JdhB2bEnterpriseContractPo> list);

    JdhB2bEnterpriseContractPo entityToPo(JdhB2bEnterpriseContract jdhB2bEnterpriseContract);

}
