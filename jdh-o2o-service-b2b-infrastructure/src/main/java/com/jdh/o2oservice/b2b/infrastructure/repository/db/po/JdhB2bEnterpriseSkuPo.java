package com.jdh.o2oservice.b2b.infrastructure.repository.db.po;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "jdh_b2b_enterprise_sku",autoResultMap = true)
public class JdhB2bEnterpriseSkuPo extends JdhBasicPo{

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 企业服务skuId
     */
    private Long enterpriseSkuId;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 渠道价
     */
    private BigDecimal channelPrice;

    /**
     * 加人加价 1-正常累加 2-单独计价
     */
    private Integer priceType;

    /**
     * 单独计价
     */
    private BigDecimal singlePrice;

    /**
     * 护士出门取消扣款比例
     */
    private BigDecimal nurseOutCancelDeduction;

    /**
     * 护士开始服务取消扣款比例
     */
    private BigDecimal nurseServedCancelDeduction;

    /**
     * 合同编号
     */
    private String contractNumber;

    /**
     * 扩展信息
     */
    private String extend;

    /**
     * 是否有效 1有效 0无效
     */
    private Integer yn;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 数据来源分支
     */
    private String branch;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updateUser;

    /**
     * 修改时间
     */
    private Date updateTime;

}