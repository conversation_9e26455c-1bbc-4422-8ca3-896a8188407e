<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>jdh-o2o-service-b2b</artifactId>
        <groupId>com.jdh.o2oservice.b2b</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>jdh-o2o-service-b2b-infrastructure</artifactId>
    <version>${revision}</version>

    <dependencies>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jdh.o2oservice.b2b</groupId>
            <artifactId>jdh-o2o-service-b2b-base</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jdh.o2oservice.b2b</groupId>
            <artifactId>jdh-o2o-service-b2b-domain</artifactId>
        </dependency>

        <!-- 全局分布式唯一id生成 -->
        <dependency>
            <groupId>com.global</groupId>
            <artifactId>global-service-id-client</artifactId>
        </dependency>

        <!-- 京图路线接口 -->
        <dependency>
            <groupId>com.jd.lbs.jdlbsapi</groupId>
            <artifactId>jdlbsapi-api</artifactId>
        </dependency>

        <!-- 地址编码服务 全地址转换经纬度 -->
        <dependency>
            <groupId>com.jd.lbs.geocode</groupId>
            <artifactId>geocode-api</artifactId>
        </dependency>

        <!--京东级联地址-->
        <dependency>
            <groupId>com.jd.addresstranslation</groupId>
            <artifactId>addressTranslation-api</artifactId>
            <version>1.5.9-SNAPSHOT</version>
        </dependency>

        <!--o2o-->
        <dependency>
            <groupId>com.jdh.o2oservice</groupId>
            <artifactId>o2o-service-export</artifactId>
            <version>9.9.9-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>easyexcel</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>cn.afterturn</groupId>
                    <artifactId>easypoi</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>cn.afterturn</groupId>
                    <artifactId>easypoi-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>commons-beanutils</groupId>
            <artifactId>commons-beanutils</artifactId>
            <version>1.9.3</version>
        </dependency>

        <!--elasticsearch -->
        <dependency>
            <groupId>org.elasticsearch</groupId>
            <artifactId>elasticsearch</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>log4j-api</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>transport</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>elasticsearch</artifactId>
                    <groupId>org.elasticsearch</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch.plugin</groupId>
            <artifactId>transport-netty4-client</artifactId>
        </dependency>

        <!--   虚拟pin  -->
        <dependency>
            <groupId>com.jd.health.ares.open.platform</groupId>
            <artifactId>ares-open-platform-export</artifactId>
            <version>1.7.11-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.jd.contract</groupId>
            <artifactId>contract-sdk-api</artifactId>
            <version>1.2.0-interface</version>
        </dependency>

        <!-- uim2  -->
        <dependency>
            <groupId>com.jd.uim2</groupId>
            <artifactId>uim2-facade</artifactId>
        </dependency>

        <!--   人资     -->
        <dependency>
            <groupId>com.jd.enterprise</groupId>
            <artifactId>asia-hr-mdm-client</artifactId>
            <version>0.0.9-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.jd</groupId>
                    <artifactId>jsf</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.jd.user.sdk</groupId>
            <artifactId>user-sdk-export</artifactId>
        </dependency>

    </dependencies>

</project>