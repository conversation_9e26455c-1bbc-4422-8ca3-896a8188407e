package com.jdh.o2oservice.b2b.domain.enterpriseremark.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 企业备注分页查询对象
 *
 * <AUTHOR>
 * @date 2025/03/10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EnterpriseRemarkPageQuery {

    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 用户PIN
     */
    private String userPin;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 每页条数
     */
    private Integer pageSize;
}
