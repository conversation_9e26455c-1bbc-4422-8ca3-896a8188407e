package com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo;

import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.man.PromiseBo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @data 2024/9/23
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CompletePromiseBo {

    /**
     * 履约单信息
     */
    private PromiseBo promiseBo;

    /**
     * 工单详情
     */
    private AngelWorkDetailBo angelWorkDetailBo;

}
