package com.jdh.o2oservice.b2b.domain.enterprisevoucher.rpc;

import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.O2oPromiseBo;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.IntendedAngelBo;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.ctx.QueryIntendedAngelCtx;

public interface O2oServiceTradeRpc {


    /**
     * 创建voucher promise  同步
     *
     * @param ctx ctx
     * @return {@link O2oPromiseBo }
     */
    IntendedAngelBo queryIntendedAngel(QueryIntendedAngelCtx ctx);

}
