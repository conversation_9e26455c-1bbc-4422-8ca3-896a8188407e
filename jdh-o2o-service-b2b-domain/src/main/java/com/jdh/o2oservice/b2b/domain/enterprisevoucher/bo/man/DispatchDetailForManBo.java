package com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.man;

import com.jdh.o2oservice.b2b.common.request.AbstractPageQuery;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description
 * @Date 2025/3/5 下午3:53
 * <AUTHOR>
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DispatchDetailForManBo implements Serializable {

    /**
     * 履约单ID
     */
    private Long promiseId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 派单ID
     */
    private Long dispatchId;
    /**
     * 派单轮数
     */
    private Integer dispatchRound;

    private Integer pageNum = 1;

    private Integer pageSize = 10;
}
