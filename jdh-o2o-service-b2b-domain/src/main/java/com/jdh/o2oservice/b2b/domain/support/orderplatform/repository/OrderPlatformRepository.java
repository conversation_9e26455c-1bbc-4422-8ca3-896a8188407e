package com.jdh.o2oservice.b2b.domain.support.orderplatform.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.b2b.domain.support.orderplatform.model.OrderPlatform;
import com.jdh.o2oservice.b2b.domain.support.orderplatform.repository.query.OrderPlatformPageQuery;
import com.jdh.o2oservice.b2b.domain.support.orderplatform.repository.query.OrderPlatformQuery;

public interface OrderPlatformRepository {

    /**
     * save
     *
     * @param orderPlatform orderPlatform
     * @return int
     */
    int save(OrderPlatform orderPlatform);

    /**
     * 查询
     *
     * @param query 查询
     * @return {@link OrderPlatform }
     */
    OrderPlatform query(OrderPlatformQuery query);

    /**
     * 查询页
     *
     * @param build 构建
     * @return {@link Page }<{@link OrderPlatform }>
     */
    Page<OrderPlatform> queryPage(OrderPlatformPageQuery build);
}
