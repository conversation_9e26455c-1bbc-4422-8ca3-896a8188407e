package com.jdh.o2oservice.b2b.domain.enterprisevoucher.ctx;

import com.jdh.o2oservice.b2b.domain.enterprisevoucher.model.EnterpriseVoucherExtend;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QueryIntendedAngelCtx {

    /**
     * 服务id
     */
    private List<Long> serviceIds;

    /**
     * 地址id
     */
    private Long addressId;

    /**
     * 地址明细
     */
    private String fullAddress;

    /**
     * cps邀请码
     */
    private String cpsInviteCode;


}
