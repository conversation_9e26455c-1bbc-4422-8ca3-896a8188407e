package com.jdh.o2oservice.b2b.domain.enterpriseaccount.model;

import com.jdh.o2oservice.b2b.base.model.Aggregate;
import com.jdh.o2oservice.b2b.base.model.AggregateCode;
import com.jdh.o2oservice.b2b.base.model.DomainCode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class JdhB2bEnterpriseAccountDetail implements Aggregate<JdhEnterpriseAccountDetailIdentifier> {

    /**
     * id
     */
    private Long id;
    /**
     * 企业ID
     */
    private Long enterpriseId;
    /**
     * 账户Id
     */
    private Long accountId;
    /**
     * 账户明细Id
     */
    private Long accountDetailId;
    /**
     * 来源单据类型：1:voucher, 2:账单打款
     */
    private Integer sourceReceiptType;
    /**
     * 来源单据id
     */
    private Long sourceReceiptId;
    /**
     * 冻结金额
     */
    private BigDecimal freezeAmount;
    /**
     * 类型：1-冻结 2-释放
     */
    private Integer freezeType;

    /**
     * 扩展信息
     */
    private String extend;

    /**
     * 是否有效 1有效 0无效
     */
    private Integer yn;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 数据来源分支
     */
    private String branch;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updateUser;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 操作人
     */
    private String operator;

    @Override
    public DomainCode getDomainCode() {
        return null;
    }

    @Override
    public AggregateCode getAggregateCode() {
        return null;
    }

    /**
     * 版本
     *
     * @return {@link Integer}
     */
    @Override
    public Integer version() {
        return version;
    }

    /**
     * 版本增加
     */
    @Override
    public void versionIncrease() {
        version++;
    }

    /**
     * 获取标识符
     *
     * @return id
     */
    public JdhEnterpriseAccountDetailIdentifier getIdentifier() {
        return new JdhEnterpriseAccountDetailIdentifier(this.accountDetailId);
    }
}