package com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/9/19
 */
@Data
public class AngelServiceRecordQuestionGroupBo {
    /**
     * 节点名称
     */
    private String name;

    /**
     * 节点备注
     */
    private String desc;

    /**
     * 节点唯一code码
     */
    private String code;

    /**
     * 是否展示 1展示 0否
     */
    private Integer show;

    /**
     * 排序
     */
    private Integer sort;



    /**
     * 节点状态：0-未开始，1-进行中，2-已完成
     */
    private Integer status;

    /**
     * 题
     */
    private List<AngelServiceRecordQuestionBo> questionDTOS;

    /**
     * 按钮文案
     */
    private String buttonName;

    /**
     * 是否可以点击
     */
    private Boolean isClick = true;

    /**
     * angelTask
     */
    private AngelTaskBo angelServiceInfoDto;

}
