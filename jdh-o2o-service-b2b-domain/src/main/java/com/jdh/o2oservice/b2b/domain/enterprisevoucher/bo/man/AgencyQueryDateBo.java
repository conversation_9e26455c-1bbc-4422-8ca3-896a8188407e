package com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.man;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Date 2025/2/28 下午1:52
 * <AUTHOR>
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AgencyQueryDateBo implements Serializable {

    /**
     * 检测单ID
     */
    private Long medicalPromiseId;

    /**
     * 用户pin
     */
    private String userPin;

    /**
     * 地址id
     */
    private Long addressId;

    /**
     * fullAddress
     */
    private String fullAddress;

    /**
     * 父级日期id
     */
    private String parentDateId;

    /**
     * 履约单ID
     */
    private Long promiseId;

    /**
     * sku列表
     */
    private List<Long> skuIds;

    /**
     * 1 展示可约时间 2展示不可约时间 3或null展示全部
     */
    private Integer showTimeType;
}
