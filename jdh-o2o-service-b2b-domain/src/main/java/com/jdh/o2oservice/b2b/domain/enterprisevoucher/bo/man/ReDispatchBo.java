package com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.man;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Description 重新派单
 * @Date 2025/3/3 上午11:33
 * <AUTHOR>
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ReDispatchBo implements Serializable {

    /**
     * 履约单ID
     */
    private Long promiseId;

    /**
     * 派单任务ID
     */
    private Long dispatchId;

    /**
     * 操作人（一般为服务者）
     */
    @NotNull(message = "操作人不能为空")
    private String operator;

    /**
     * 1：AI 2：系统 3：医生Pin 4：运营 5：医生ID
     */
    @NotNull(message = "角色不能为空")
    private Integer roleType;

    /**
     * 原因
     */
    private String reason;
}
