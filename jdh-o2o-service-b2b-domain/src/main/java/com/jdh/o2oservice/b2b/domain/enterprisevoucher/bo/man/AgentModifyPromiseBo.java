package com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.man;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * @Date 2025/2/28 下午2:14
 * <AUTHOR>
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AgentModifyPromiseBo implements Serializable {

    /**
     * 起始时间
     */
    private String appointmentStartTime;

    /**
     * 结束时间
     */
    private String appointmentEndTime;

    /**
     * 是否立即预约
     */
    private Boolean isImmediately;

    /**
     * 备注
     */
    private String remark;

    /**
     * 日期类型
     */
    private Integer dateType = 2;

    /**
     * promiseId
     */
    private Long promiseId;

    /**
     * 操作人角色
     */
    private Integer operatorRoleType;

    /**
     * 操作人
     */
    private String operator;


}
