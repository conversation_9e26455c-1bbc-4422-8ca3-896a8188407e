package com.jdh.o2oservice.b2b.domain.support.orderplatform.context;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 创建订单平台ctx
 *
 * <AUTHOR>
 * @date 2025/02/25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateOrderPlatformCtx {

    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 源订单平台
     */
    private String sourceOrderPlatform;

    /**
     * 扩展信息
     */
    private String extend;

    /**
     * userPin
     */
    private String userPin;

}
