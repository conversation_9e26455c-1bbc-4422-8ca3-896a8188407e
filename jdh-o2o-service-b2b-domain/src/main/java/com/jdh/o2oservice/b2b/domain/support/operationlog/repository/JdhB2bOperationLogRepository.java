package com.jdh.o2oservice.b2b.domain.support.operationlog.repository;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.b2b.domain.support.operationlog.mode.JdhB2bOperationLog;
import com.jdh.o2oservice.b2b.domain.support.operationlog.repository.query.JdhB2bOperationLogQuery;

/**
 * 操作日志
 */
public interface JdhB2bOperationLogRepository{

    /**
     * 保存
     *
     * @param entity
     * @return count
     */
    int save(JdhB2bOperationLog entity);

    /**
     * 更新
     *
     * @param entity
     * @return count
     */
    int update(JdhB2bOperationLog entity);

    /**
     * 查询
     *
     * @param operationLogId
     * @return count
     */
    JdhB2bOperationLog getOperationLog(Long operationLogId);

    /**
     * 分页查询操作日志
     * @param query
     * @return
     */
    Page<JdhB2bOperationLog> queryPageOperationLog(JdhB2bOperationLogQuery query);
}
