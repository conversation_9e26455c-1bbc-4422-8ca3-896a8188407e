package com.jdh.o2oservice.b2b.domain.enterprisebill.model;

import com.jdh.o2oservice.b2b.base.model.Aggregate;
import com.jdh.o2oservice.b2b.base.model.AggregateCode;
import com.jdh.o2oservice.b2b.base.model.DomainCode;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName:JdhB2bEnterpriseBill
 * @Description: B2b企业账单
 * @Author: liwenming
 * @Date: 2025/2/24 15:14
 * @Vserion: 1.0
 **/
@Data
public class JdhB2bEnterpriseBill implements Aggregate<JdhB2bEnterpriseBillIdentifier> {

    /**
     * 主键
     */
    private Long id;
    /**
     * 账单Id
     */
    private Long billId;
    /**
     * 企业Id
     */
    private Long enterpriseId;

    /**
     * 账单金额
     */
    private BigDecimal billAmount;

    /**
     * 调账金额
     */
    private BigDecimal adjustAmount;

    /**
     * 调账说明
     */
    private String adjustDescribe;

    /**
     * 最终金额
     */
    private BigDecimal finalAmount;

    /**
     * 账单状态 1-待确认 2-已确认 3-已打款 4-已到账 5-已作废
     */
    private Integer billStatus;
    /**
     * 账期
     */
    private String billDate;
    /**
     * 扩展信息
     */
    private String extend;

    /**
     * 版本号
     */
    private Integer version;
    /**
     * 数据来源分支
     */
    private String branch;

    /**
     * 是否有效 1有效 0 无效
     */
    private Integer yn;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改人
     */
    private String updateUser;
    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 聚合所属领域编码
     *
     * @return
     */
    @Override
    public DomainCode getDomainCode() {
        return null;
    }

    /**
     * 获取聚合编码
     *
     * @return {@link AggregateCode}
     */
    @Override
    public AggregateCode getAggregateCode() {
        return null;
    }

    /**
     * 版本
     *
     * @return {@link Integer}
     */
    @Override
    public Integer version() {
        return 0;
    }

    /**
     * 版本增加
     */
    @Override
    public void versionIncrease() {

    }

    /**
     * 获取标识符
     *
     * @return {@link}
     */
    public JdhB2bEnterpriseBillIdentifier getIdentifier() {
        return new JdhB2bEnterpriseBillIdentifier(this.billId);
    }
}
