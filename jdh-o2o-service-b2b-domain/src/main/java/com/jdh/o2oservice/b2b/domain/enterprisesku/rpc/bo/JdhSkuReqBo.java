package com.jdh.o2oservice.b2b.domain.enterprisesku.rpc.bo;

import lombok.Data;

import java.io.Serializable;

@Data
public class JdhSkuReqBo implements Serializable {

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 是否查询服务项目
     */
    private Boolean queryServiceItem;

    /**
     * <pre>
     * 清单类型 1-项目（单项目） 2-服务（多项目套餐） 3-服务组（多服务套餐）
     * </pre>
     */
    private Integer skuItemType;

    /**
     * 是否查询商品中台主数据
     */
    private Boolean querySkuCoreData;
}
