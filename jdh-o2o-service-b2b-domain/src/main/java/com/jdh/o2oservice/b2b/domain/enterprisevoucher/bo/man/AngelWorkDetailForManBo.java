package com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.man;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description
 * @Date 2025/2/28 下午3:04
 * <AUTHOR>
 **/
@Data
public class AngelWorkDetailForManBo implements Serializable {

    /**
     * 垂直业务身份编码
     */
    private String verticalCode;

    /**
     * 服务类型
     */
    private String serviceType;

    /**
     * 来源Id，派单id
     */
    private Long sourceId;

    /**
     * 服务站Id
     */
    private String angelStationId;

    /**
     * 服务开始时间
     */
    private Date workStartTime;

    /**
     * 服务结束时间
     */
    private Date workEndTime;

    /**
     * 计划出门时间
     */
    private Date planOutTime;

    /**
     * 计划完成时间
     */
    private Date planFinishTime;

    /**
     * 工单id
     */
    private String workId;

    /**
     * 京东订单Id
     */
    private Long jdOrderId;

    /**
     * 服务者id
     */
    private String angelId;

    /**
     * 服务者pin
     */
    private String angelPin;

    /**
     * 服务者姓名
     */
    private String angelName;

    /**
     * 服务者手机号
     */
    private String angelPhone;

    /**
     * 履约单id
     */
    private String promiseId;

    /**
     * 工单类型：1=骑手,2=护士,3=护工
     */
    private Integer workType;

    /**
     * 预计服务费用
     */
    private BigDecimal angelCharge;

    /**
     * 工单状态
     */
    private Integer workStatus;

    /**
     * 工单暂停状态：0正常，1退款暂停, 2取消暂停
     */
    private Integer stopStatus;

    /**
     * 保单id
     */
    private String insureId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 着装照片地址
     */
    private List<String> clothingPicUrls;

    /**
     * 医疗废物照片地址
     */
    private List<String> medicalWastePicUrls;

    /**
     * 服务记录地址
     */
    private List<String> serviceRecordPicUrls;

    /**
     * 工单历史明细
     */
    private List<AngelWorkHistoryBo> workHistoryDtoList;

    /**
     * 任务单信息
     */
    private List<AngelTaskDetailBo> angelTaskDetailDtoList;

    /**
     * 运单列表
     */
    private List<AngelShipBo> shipDtoList;
}
