package com.jdh.o2oservice.b2b.domain.enterprisebill.enums;


import com.jdh.o2oservice.b2b.base.exception.AbstractErrorCode;

/**
 * @ClassName EnterpriseBillErrorCode
 * @Description
 * <AUTHOR>
 * @Date 2025/2/25 16:08
 **/
public enum EnterpriseBillErrorCode implements AbstractErrorCode {

    BILL_ADJUST_NULL("35021", "调账金额为空"),
    BILL_ID_NULL("35022", "账单Id为空"),
    BILL_NOT_EXIST("35023", "账单不存在"),
    CONFIRM_BILL_STATUS_FAIL("35024", "账单状态已变更,不可再操作"),
    ENTERPRISE_ID_NULL("35025", "企业Id为空"),
    BILL_DATE_NULL("35026", "账期为空"),
    ADJUST_DESC_OUT("35027", "调账说明内容超长"),
    ADJUST_AMOUNT_FAIL("35028", "调账金额非法"),
    ADJUST_AMOUNT_OUT("35029", "调账金额小数点后最多两位"),
    ADJUST_AMOUNT_OUT_BILL("35030", "调账金额超出"),

    ;

    /**
     * PromiseErrorCode
     *
     * @param code        代码
     * @param description 描述
     */
    EnterpriseBillErrorCode( String code, String description) {
        this.code = code;
        this.description = description;
    }

    /** */
    private String code;
    /**
     * 展示给客户的错误提示，因为有的场景把系统异常传递给客户展示，体验不好
     */
    private String description;

    /**
     * getCode
     *
     * @return {@link String}
     */
    @Override
    public String getCode() {
        return this.code;
    }

    /**
     * getDescription
     *
     * @return {@link String}
     */
    @Override
    public String getDescription() {
        return this.description;
    }
}