package com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo;

import com.jd.lbs.jdlbsapi.dto.c2c.PlaceSearchResult;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName TextParseAddressBO
 * @Description
 * <AUTHOR>
 * @Date 2025/1/7 13:14
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TextParseAddressBO {

    /**
     * 待解析文本
     */
    private String text;

    /**
     * 用户姓名
     */
    private String name;

    /**
     * 电话（座机/手机），优先手机，再是座机
     */
    private String phone;

    /**
     * 手机
     */
    private String mobilePhone;

    /**
     * 座机
     */
    private String telphone;

    /**
     * 地址相关的信息
     */
    private TextParseAddressDetailBo addressInfo;

    /**
     *
     */
    private List<PlaceSearchResult> searchPlaces;
}