package com.jdh.o2oservice.b2b.domain.enterprisevoucher.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * EnterpriseVoucherQuery
 *
 * <AUTHOR>
 * @date 2025/02/27
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EnterpriseVoucherQuery {

    /**
     * enterpriseVoucherId
     */
    private Long enterpriseVoucherId;

    /**
     * 履约单id
     */
    private Long promiseId;

    /**
     * 服务单id
     */
    private Long voucherId;

    /**
     * 企业ID，用于标识所属企业。
     */
    private Long enterpriseId;


}
