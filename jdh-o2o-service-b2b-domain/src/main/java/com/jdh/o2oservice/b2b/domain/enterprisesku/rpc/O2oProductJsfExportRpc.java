package com.jdh.o2oservice.b2b.domain.enterprisesku.rpc;

import com.jdh.o2oservice.b2b.domain.enterprisesku.rpc.bo.JdhSkuReqBo;
import com.jdh.o2oservice.b2b.domain.enterprisesku.rpc.bo.JdhSkuResBo;

import java.util.List;
import java.util.Map;

public interface O2oProductJsfExportRpc {

    /**
     * 查询商品信息
     * @param bo
     * @return
     */
    JdhSkuResBo queryJdhSkuInfo(JdhSkuReqBo bo);

    /**
     * 批量查询商品信息
     * @param skuIds
     * @return
     */
    Map<Long,JdhSkuResBo> batchQueryJdhSkuInfo(List<Long> skuIds);
}
