package com.jdh.o2oservice.b2b.domain.support.orderplatform.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderPlatform {

    /**
     * id
     */
    private Long id;

    /**
     * 企业ID
     */
    private Long enterpriseId;
    /**
     * 源订单平台
     */
    private String sourceOrderPlatform;
    /**
     * 扩展信息
     */
    private String extend;

    /**
     * 创建人pin
     */
    private String createUser;

}
