package com.jdh.o2oservice.b2b.domain.enterprise.repository.query;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Description 企业信息
 * @Date 2025/2/25 下午6:45
 * <AUTHOR>
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class JdhB2bEnterpriseQuery implements Serializable {

    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 企业名称
     */
    private String name;

    /**
     * 企业短名称
     */
    private String shortName;

    /**
     * 状态 1-启用 0-禁用
     */
    private Integer available;

    /**
     * 垂直业务身份编码
     */
    private String verticalCode;

    /**
     * 企业ID
     */
    private List<Long> enterpriseIdList;

    /**
     * 当前页
     */
    private Integer pageNum = 1;

    /**
     * 每页条数
     */
    private Integer pageSize = 10;

}
