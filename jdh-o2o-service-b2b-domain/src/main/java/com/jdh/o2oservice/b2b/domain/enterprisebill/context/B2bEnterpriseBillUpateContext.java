package com.jdh.o2oservice.b2b.domain.enterprisebill.context;

import com.jdh.o2oservice.b2b.common.request.AbstractPageQuery;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName:B2bEnterpriseBillQueryContext
 * @Description:B2b企业账单Context
 * @Author: liwenming
 * @Date: 2025/2/24 15:14
 * @Vserion: 1.0
 **/
@Data
public class B2bEnterpriseBillUpateContext extends AbstractPageQuery implements Serializable {
    /**
     *
     */
    private static final long serialVersionUID = -2406981332913100852L;
    /**
     * 企业Id
     */
    private Long enterpriseId;
    /**
     * 账单Id
     */
    private Long billId;
}
