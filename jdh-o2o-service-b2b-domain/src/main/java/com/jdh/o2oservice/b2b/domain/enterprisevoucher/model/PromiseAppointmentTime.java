package com.jdh.o2oservice.b2b.domain.enterprisevoucher.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 承诺预约时间
 *
 * <AUTHOR>
 * @date 2025/02/27
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PromiseAppointmentTime {

    /**
     * 预约时间类型1-按日 2-按时间段
     * 类型为1时，必传appointmentDate
     * 类型为2时，必传appointmentBeginTime、appointmentEndTime
     * 类型为4时，必传appointmentDate、timeRange
     */
    private Integer dateType;
    /**
     * 预约开始时间
     * 格式 yyyy-MM-dd HH:mm
     */
    private String appointmentStartTime;
    /**
     * 预约结束时间
     * 格式 yyyy-MM-dd HH:mm
     */
    private String appointmentEndTime;

    /**
     * 是否是立即预约
     */
    private Boolean isImmediately;
}
