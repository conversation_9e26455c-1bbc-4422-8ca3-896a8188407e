package com.jdh.o2oservice.b2b.domain.support.orderplatform.factory;

import com.jdh.o2oservice.b2b.base.exception.BusinessErrorCode;
import com.jdh.o2oservice.b2b.base.exception.BusinessException;
import com.jdh.o2oservice.b2b.base.exception.ParamErrorCode;
import com.jdh.o2oservice.b2b.base.util.SpringUtil;
import com.jdh.o2oservice.b2b.domain.support.orderplatform.context.CreateOrderPlatformCtx;
import com.jdh.o2oservice.b2b.domain.support.orderplatform.model.OrderPlatform;
import com.jdh.o2oservice.b2b.domain.support.orderplatform.repository.OrderPlatformRepository;
import com.jdh.o2oservice.b2b.domain.support.orderplatform.repository.query.OrderPlatformQuery;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;

/**
 * 订单平台工厂
 *
 * <AUTHOR>
 * @date 2025/02/25
 */
public class OrderPlatformFactory {

    /**
     * 创建订单平台
     *
     * @param ctx ctx
     * @return {@link OrderPlatform }
     */
    public static OrderPlatform createOrderPlatform(CreateOrderPlatformCtx ctx) {
        //1、校验基本参数
        if(Objects.isNull(ctx.getEnterpriseId()) || Objects.isNull(ctx.getSourceOrderPlatform())) {
            throw new BusinessException(ParamErrorCode.PARAM_IS_NULL);
        }

        //2、校验库里是否已经有同名的了
        OrderPlatform exitOrderPlatform = SpringUtil.getBean(OrderPlatformRepository.class).query(OrderPlatformQuery.builder()
                .enterpriseId(ctx.getEnterpriseId())
                .sourceOrderPlatform(ctx.getSourceOrderPlatform())
                .build());
        if(Objects.nonNull(exitOrderPlatform)) {
            return null;
        }

        //2、创建model
        return OrderPlatform.builder()
                .enterpriseId(ctx.getEnterpriseId())
                .sourceOrderPlatform(ctx.getSourceOrderPlatform())
                .createUser(ctx.getUserPin())
                .build();
    }

}
