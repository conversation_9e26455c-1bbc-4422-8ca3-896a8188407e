package com.jdh.o2oservice.b2b.domain.enterpriseremark.cmd;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 企业备注命令对象
 *
 * <AUTHOR>
 * @date 2025/03/10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EnterpriseRemarkCmd {

    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 企业备注ID
     */
    private Long enterpriseRemarkId;

    /**
     * 备注内容
     */
    private String content;

    /**
     * 操作人
     */
    private String operator;
}
