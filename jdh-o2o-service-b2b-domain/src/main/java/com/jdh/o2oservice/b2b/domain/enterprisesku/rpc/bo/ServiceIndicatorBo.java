package com.jdh.o2oservice.b2b.domain.enterprisesku.rpc.bo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class ServiceIndicatorBo implements Serializable {

    /**
     * 指标编码
     */
    private String indicatorId;

    /**
     * 指标名称
     */
    private String indicatorName;

    /**
     * 指标检查意义
     */
    private String indicatorMean;

    /**
     * 适用人群
     */
    private String indicatorSuitable;

    /**
     * 一级分类编码
     */
    private String firstIndicatorCategory;

    /**
     * 一级分类名称
     */
    private String firstIndicatorCategoryName;

    /**
     * 二级分类编码
     */
    private String secondIndicatorCategory;

    /**
     * 二级分类名称
     */
    private String secondIndicatorCategoryName;

    /**
     * 三级分类编码
     */
    private String thirdIndicatorCategory;

    /**
     * 三级分类编名称
     */
    private String thirdIndicatorCategoryName;

    /**
     * 一级商品类目信息
     */
    private Long firstSkuCategory;

    /**
     * 二级商品类目信息
     */
    private Long secondSkuCategory;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updateUser;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 京东健康指标百科ID
     */
    private String jdhWikiId;

    /**
     * 检测结果类型 1-定量 2-定性
     */
    private Integer testResultType;

    /**
     * 特殊标签 1-条件致病菌,多个逗号隔开
     */
    private String tags;

    /**
     * 站点指标信息
     */
    private List<ServiceIndicatorBo> stationIndicatorDtoList;
}
