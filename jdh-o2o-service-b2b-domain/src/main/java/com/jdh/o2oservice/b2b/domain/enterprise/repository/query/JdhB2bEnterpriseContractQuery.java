package com.jdh.o2oservice.b2b.domain.enterprise.repository.query;

import com.jdh.o2oservice.b2b.common.request.AbstractPageQuery;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description 企业合同信息
 * @Date 2025/3/11 下午6:45
 * <AUTHOR>
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class JdhB2bEnterpriseContractQuery extends AbstractPageQuery implements Serializable {

    /**
     * 企业ID
     */
    private Long enterpriseId;
    /**
     * 合同ID
     */
    private Long contractId;
    /**
     * 合同编号
     */
    private String contractNumber;
    /**
     * 有效标识：1/0
     */
    private Integer valid;
    /**
     * 合同状态：
     */
    private Integer status;
}
