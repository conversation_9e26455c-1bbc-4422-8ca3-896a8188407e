package com.jdh.o2oservice.b2b.domain.enterprisebill.model;

import com.jdh.o2oservice.b2b.base.model.Identifier;
import lombok.Builder;
import lombok.Data;

/**
 * @Author: liwenming
 * @Date: 2025/2/24 9:24 上午
 * @Description:
 */
@Data
@Builder
public class JdhB2bEnterpriseBillIdentifier implements Identifier {
    /**
     * 账单Id
     */
    private Long billId;

    @Override
    public String serialize() {
        return String.valueOf(billId);
    }

    public JdhB2bEnterpriseBillIdentifier() {
    }

    public JdhB2bEnterpriseBillIdentifier(Long billId) {
        this.billId = billId;
    }
}
