package com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.man;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description 查询外呼记录
 * @Date 2025/2/28 下午3:21
 * <AUTHOR>
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QueryCallRecordBo implements Serializable {

    /**
     * 履约单id
     */
    private Long promiseId;

    /**
     * 通话ID，唯一确定一次通话
     */
    private String callId;
}
