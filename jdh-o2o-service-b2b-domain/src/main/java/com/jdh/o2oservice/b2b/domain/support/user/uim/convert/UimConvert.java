package com.jdh.o2oservice.b2b.domain.support.user.uim.convert;

import com.jd.common.web.LoginContext;
import com.jdh.o2oservice.b2b.domain.support.user.uim.bo.Uim2RoleBO;
import com.jdh.o2oservice.b2b.domain.support.user.uim.context.UimLoginContext;
import com.jdh.o2oservice.b2b.domain.support.user.uim.context.UimRoleContext;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(builder = @org.mapstruct.Builder(disableBuilder = true))
public interface UimConvert {

    UimConvert INSTANCE = Mappers.getMapper(UimConvert.class);

    UimLoginContext toUimLoginContext(LoginContext loginContext);

    List<UimLoginContext> toUimLoginContextList(List<LoginContext> loginContext);

    UimRoleContext toUimRoleContext(Uim2RoleBO uim2RoleBO);

    List<UimRoleContext> toUimRoleContextList(List<Uim2RoleBO> loginuim2RoleBOContext);

}
