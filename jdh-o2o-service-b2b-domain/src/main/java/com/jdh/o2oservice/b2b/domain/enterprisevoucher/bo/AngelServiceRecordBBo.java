package com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/9/19
 */
@Data
public class AngelServiceRecordBBo {
    /**
     * 记录类型
     * 1。新模式返回
     */
    private Integer recordType;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 服务记录状态
     * 初始状态：0、完成:1、取消：2、评估结果高风险：-1
     *
     */
    private Integer serviceRecordStatus;

    /**
     * 外部患者ID
     */
    private String outPatientId;
    /**
     * 患者唯一ID
     */
    private Long promisePatientId;

    /**
     * patientName
     */
    private String patientName;

    /**
     * 节点集合
     */
    private List<AngelServiceRecordQuestionGroupBo> questionGroupDtoList;

}
