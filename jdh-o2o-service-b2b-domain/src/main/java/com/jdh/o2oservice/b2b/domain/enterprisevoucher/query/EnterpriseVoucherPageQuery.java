package com.jdh.o2oservice.b2b.domain.enterprisevoucher.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * EnterpriseVoucherPageQuery
 *
 * <AUTHOR>
 * @date 2025/02/27
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EnterpriseVoucherPageQuery {

    /**
     * 分页大小
     */
    private Integer pageSize = 10;

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * enterpriseVoucherId
     */
    private Long enterpriseVoucherId;

    /**
     * 企业id
     */
    private List<Long> enterpriseIdList;

    /**
     * 履约单id
     */
    private Long promiseId;
    /**
     * enterpriseVoucherIdList
     */
    private List<Long> enterpriseVoucherIdList;
    /**
     * sku名称集合
     */
    private Set<Long> enterpriseSkuIdSet;

    /**
     * 履约状态
     */
    private Set<Integer> promiseStatusSet;

    /**
     * 创建时间开始
     */
    private Date createTimeStart;

    /**
     * 创建时间结束
     */
    private Date createTimeEnd;
    /**
     * 是否统计 0-没统计 1-已统计
     */
    private Integer statistics;

    /**
     * 企业ID
     */
    private Long enterpriseId;
    /**
     * 履约状态
     */
    private List<Integer> promiseStatusList;
    /**
     * 账单结算时间开始
     */
    private String lastMonthFirstDay;

    /**
     * 账单结算时间结束
     */
    private String lastMonthLastDay;
}
