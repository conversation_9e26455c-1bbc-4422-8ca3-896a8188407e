package com.jdh.o2oservice.b2b.domain.enterprisebill.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.b2b.domain.enterprisebill.context.B2bEnterpriseBillQueryContext;
import com.jdh.o2oservice.b2b.domain.enterprisebill.model.JdhB2bEnterpriseBill;

/**
 * @ClassName:JdhB2bEnterpriseBillRepository
 * @Description: B2b企业账单
 * @Author: liwenming
 * @Date: 2025/2/24 15:14
 * @Vserion: 1.0
 **/
public interface JdhB2bEnterpriseBillRepository {
    /**
     * 查询企业账单列表
     * @param queryContext
     * @return
     */
    JdhB2bEnterpriseBill queryEnterpriseBill(B2bEnterpriseBillQueryContext queryContext);

    /**
     * 查询企业账单列表
     * @param queryContext
     * @return
     */
    Page<JdhB2bEnterpriseBill> queryEnterpriseBillPage(B2bEnterpriseBillQueryContext queryContext);

    /**
     * 查询企业某月账单
     * @param queryContext
     * @return
     */
    JdhB2bEnterpriseBill queryEnterpriseBillByDate(B2bEnterpriseBillQueryContext queryContext);

    /**
     * 更新企业账单
     * @param jdhB2bEnterpriseBill
     * @return
     */
    Integer updateEnterpriseBill(JdhB2bEnterpriseBill jdhB2bEnterpriseBill);

    /**
     * 保存企业账单
     * @param jdhB2bEnterpriseBill
     * @return
     */
    Integer saveEnterpriseBill(JdhB2bEnterpriseBill jdhB2bEnterpriseBill);

}
