package com.jdh.o2oservice.b2b.domain.enterprisevoucher.rpc;
import com.jdh.o2oservice.b2b.common.response.PageDto;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.FileUrlBo;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.PromiseTimelineBo;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.QueryPromiseTimelineBo;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.man.*;
import java.util.List;

/**
 * @Description 运营服务履约
 * @Date 2025/3/4 下午6:28
 * <AUTHOR>
 **/
public interface O2oManServicePromiseRpc {

    /**
     * 外呼记录列表
     * @param bo
     * @return
     */
    List<CallRecordBo> queryCallRecordList(QueryCallRecordBo bo);

    /**
     * 查询外呼url
     * @param bo
     * @return
     */
    String queryCallRecordUrl(QueryCallRecordBo bo);

    /**
     * 重新派单
     * @param bo
     * @return
     */
    Boolean reDispatch(ReDispatchBo bo);

    /**
     * 指定派单
     * @param bo
     * @return
     */
    DispatchTargetStatusBo targetDispatch(TargetDispatchBo bo);

    /**
     * 查询服务者详情
     * @param bo
     * @return
     */
    JdhAngelDetailBo queryAngelDetail(AngelDetailBo bo);

    /**
     * 查询预约时间
     * @param bo
     * @return
     */
    List<AgencyAppointDateBo> queryAvailableTime(AgencyQueryDateBo bo);

    /**
     * 查询服务站列表
     * @param bo
     * @return
     */
    List<JdhStationBo> queryStationDtoList(AgencyQueryDateBo bo);

    /**
     * 修改预约时间
     * @param bo
     * @return
     */
    Boolean modifyAppointmentTime(AgentModifyPromiseBo bo);

    /**
     * 查询完整B2b promise信息
     * @param bo
     * @return
     */
    ViaCompletePromiseResBo queryCompleteB2bPromise(ViaCompletePromiseReqBo bo);

    /**
     * 查询派单明细
     * @param bo
     * @return
     */
    PageDto<JdhDispatchForManBo> queryDispatchDetailList(DispatchDetailForManBo bo);

    /**
     * 通过fileId置换文件URL
     * @param bo
     * @return
     */
    List<FileUrlBo> queryMultiFileUrl(GetFileUrlBo bo);

    /**
     * 查询履约单操作日志
     * @param bo
     * @return
     */
    PromiseTimelineBo queryPromiseTimeline(QueryPromiseTimelineBo bo);

}
