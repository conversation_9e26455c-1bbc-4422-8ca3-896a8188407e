package com.jdh.o2oservice.b2b.domain.enterprisevoucher.rpc;

import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.AngelServiceRecordBBo;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.O2oAngelServiceRecordBo;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/9/19
 */
public interface O2oAngelServiceRecordRpc {

    /**
     * B端查询服务记录
     * @param bo
     * @return
     */
    List<AngelServiceRecordBBo> queryAngelServiceBRecordFlow(O2oAngelServiceRecordBo bo);

}
