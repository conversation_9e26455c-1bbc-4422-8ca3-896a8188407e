package com.jdh.o2oservice.b2b.domain.enterprise.repository;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.b2b.domain.enterprise.model.JdhB2bEnterprise;
import com.jdh.o2oservice.b2b.domain.enterprise.model.JdhEnterpriseIdentifier;
import com.jdh.o2oservice.b2b.domain.enterprise.repository.query.JdhB2bEnterpriseQuery;

import java.util.List;

/**
 * @Description 企业信息
 * @Date 2025/2/24 下午6:27
 * <AUTHOR>
 **/
public interface JdhB2bEnterpriseRepository {

    /**
     * 保存
     *
     * @param entity
     * @return count
     */
    int save(JdhB2bEnterprise entity);

    /**
     * 更新
     *
     * @param entity
     * @return count
     */
    int update(JdhB2bEnterprise entity);

    /**
     * 更新企业状态
     *
     * @param entity
     * @return count
     */
    int updateEnterpriseStatus(JdhB2bEnterprise entity);

    /**
     * 通过Identify 查询
     * @param identifier
     * @return
     */
    JdhB2bEnterprise find(JdhEnterpriseIdentifier identifier);

    /**
     * 分页查询企业列表
     * @param query
     * @return
     */
    Page<JdhB2bEnterprise> queryPageEnterprise(JdhB2bEnterpriseQuery query);

    /**
     * 企业列表
     * @param query
     * @return
     */
    List<JdhB2bEnterprise> findList(JdhB2bEnterpriseQuery query);
}
