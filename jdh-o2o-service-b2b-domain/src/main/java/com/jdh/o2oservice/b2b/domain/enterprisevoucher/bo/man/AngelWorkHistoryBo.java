package com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.man;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description 服务工单历史
 * @Date 2025/2/28 下午3:05
 * <AUTHOR>
 **/
@Data
public class AngelWorkHistoryBo implements Serializable {

    /**
     * 工单id
     */
    private String angelWorkId;

    /**
     * 前一个状态
     * AngelWorkStatusEnum
     */
    private Integer beforeStatus;

    /**
     * 当前状态
     */
    private Integer afterStatus;

    /**
     * 操作时间
     */
    private Date operateTime;

    /**
     * 创建时间
     */
    private Date createTime;
}
