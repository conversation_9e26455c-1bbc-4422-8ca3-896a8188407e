package com.jdh.o2oservice.b2b.domain.enterpriseaccount.repository;
import com.jdh.o2oservice.b2b.domain.enterpriseaccount.repository.model.JdhB2bEnterpriseAccount;
import com.jdh.o2oservice.b2b.domain.enterpriseaccount.repository.model.JdhEnterpriseAccountIdentifier;
import com.jdh.o2oservice.b2b.domain.enterpriseaccount.repository.query.JdhB2bEnterpriseAccountQuery;

import java.util.List;

/**
 * 企业账户
 */
public interface JdhB2bEnterpriseAccountRepository {

    /**
     * 保存
     *
     * @param jdhB2bEnterpriseAccount
     * @return count
     */
    int save(JdhB2bEnterpriseAccount jdhB2bEnterpriseAccount);

    /**
     * 更新
     *
     * @param jdhB2bEnterpriseAccount
     * @return count
     */
    int update(JdhB2bEnterpriseAccount jdhB2bEnterpriseAccount);

    /**
     * 通过Identify 查询
     * @param identifier
     * @return
     */
    JdhB2bEnterpriseAccount find(JdhEnterpriseAccountIdentifier identifier);

    /**
     * 企业账户列表
     * @param jdhB2bEnterpriseAccountQuery
     * @return
     */
    List<JdhB2bEnterpriseAccount> findList(JdhB2bEnterpriseAccountQuery jdhB2bEnterpriseAccountQuery);
}
