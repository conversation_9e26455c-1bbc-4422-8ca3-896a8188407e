package com.jdh.o2oservice.b2b.domain.support.orderplatform.repository.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * OrderPlatformQuery
 *
 * <AUTHOR>
 * @date 2025/02/25
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderPlatformQuery {

    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 源订单平台
     */
    private String sourceOrderPlatform;


}
