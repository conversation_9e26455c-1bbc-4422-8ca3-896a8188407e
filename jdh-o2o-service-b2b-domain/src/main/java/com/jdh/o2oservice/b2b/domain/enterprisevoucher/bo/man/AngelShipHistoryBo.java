package com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.man;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description 服务运单历史记录
 * @Date 2025/2/28 下午3:09
 * <AUTHOR>
 **/
@Data
public class AngelShipHistoryBo implements Serializable {

    /**
     * 运单Id
     */
    private Long shipId;

    /**
     * 服务工单Id
     */
    private Long workId;

    /**
     * 变更前任务单状态
     * AngelShipStatusEnum
     */
    private Integer beforeStatus;

    /**
     * 变更后任务单状态：1
     */
    private Integer afterStatus;

    /**
     * 操作时间
     */
    private Date operateTime;

    /**
     * 创建时间
     */
    private Date createTime;
}
