package com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.man;

import com.google.common.base.Joiner;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;

/**
 * @Date 2025/2/28 下午2:58
 * <AUTHOR>
 **/
@Data
public class JdhAngelBo implements Serializable {


    /**
     * 主键
     */
    private Long id;

    /**
     * 互医医生id
     */
    private Long nethpDocId;

    /**
     * 服务者id
     */
    private Long angelId;

    /**
     * 服务者pin
     */
    private String angelPin;

    /**
     * 服务者姓名
     */
    private String angelName;

    /**
     * 服务者头图
     */
    private String headImg;

    /**
     * 性别 1-男 2-女
     */
    private Integer gender;

    /**
     * 证件号
     */
    private String idCard;

    /**
     * 证件类型 1-身份证 2-护照
     */
    private Integer idCardType;

    /**
     * 证件号索引
     */
    private String idCardIndex;

    /**
     * 身份证国徽图url
     */
    private String idCardImgFrontUrl;

    /**
     * 身份证人像图url
     */
    private String idCardImgOppositeUrl;

    /**
     * 电话
     */
    private String phone;

    /**
     * 电话加密索引
     */
    private String phoneIndex;

    /**
     * 紧急联系人姓名
     */
    private String emergencyContactName;

    /**
     * 紧急联系人电话
     */
    private String emergencyContactPhone;

    /**
     * 人员标签 0兼职 1全职
     */
    private Integer jobNature;

    /**
     * 接单状态 0关闭 1开启
     */
    private Integer takeOrderStatus;

    /**
     * 服务站点id
     */
    private Long stationId;

    /**
     * 站长erp
     */
    private String stationMaster;

    /**
     * 审核状态
     */
    private Integer auditProcessStatus;

    /**
     * 是否有效 0-无效 1-有效
     */
    private Integer yn;

    /**
     * 创建日期
     */
    private Date createTime;

    /**
     * 修改日期
     */
    private Date updateTime;

    /**
     * 服务者职业名称列表(多个职业用逗号分隔)
     */
    private String angelProfessionNames;
    /**
     * 服务者职业职级名称(多个职业用逗号分隔)
     */
    private String angelProfessionTitleNames;

    /**
     * 服务者职业机构名称(多个职业用逗号分隔)
     */
    private String angelProfessionInstitutionNames;

    /**
     * 服务者绑定的服务站信息
     */
    private AngelStationBo jdhStationDto;

    /**
     * 服务者已点亮技能信息(多个技能用逗号分隔)
     */
    private String skillNames;

    /**
     * 绑定机构id（结算机构）
     */
    private Long jdhProviderId;

    /**
     * 服务者姓名加密
     */
    private String angelNameEncrypt;

    /**
     * 服务者证件号加密
     */
    private String idCardEncrypt;

    /**
     * 服务者电话加密
     */
    private String phoneEncrypt;

    /**
     * 全地址
     */
    private String fullAddress;

    /**
     * 全科室
     */
    private String fullDepartmentName;

    /**
     * 省地区code
     */
    private String provinceName;

    /**
     * 市地区code
     */
    private String cityName;

    /**
     * 县地区code
     */
    private String countyName;

    /**
     * 一级科室code
     */
    private String oneDepartmentName;

    /**
     * 二级科室code
     */
    private String twoDepartmentName;

    /**
     * 返回全地址
     * @return
     */
    public String getFullAddress(){
        if(StringUtils.isBlank(this.getCountyName())){
            return Joiner.on("-").skipNulls().join(StringUtils.defaultIfBlank(this.getProvinceName(),null),StringUtils.defaultIfBlank(this.getCityName(),null));
        }
        return Joiner.on("-").skipNulls().join(StringUtils.defaultIfBlank(this.getProvinceName(),null),StringUtils.defaultIfBlank(this.getCityName(),null),this.getCountyName());
    }

    /**
     * 返回全科室
     * @return
     */
    public String getFullDepartmentName(){
        return Joiner.on("-").skipNulls().join(StringUtils.defaultIfBlank(this.getOneDepartmentName(),null),StringUtils.defaultIfBlank(this.getTwoDepartmentName(),null));
    }
}
