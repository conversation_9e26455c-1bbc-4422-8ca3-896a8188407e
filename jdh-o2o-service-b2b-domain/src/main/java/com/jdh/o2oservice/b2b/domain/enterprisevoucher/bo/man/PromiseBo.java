package com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.man;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description 履约单
 * @Date 2025/2/28 下午2:32
 * <AUTHOR>
 **/
@Data
public class PromiseBo implements Serializable {

    /**
     * id
     */
    private Long id;

    /**
     * 用户pin
     */
    private String userPin;

    /**
     * 垂直业务身份编码
     */
    private String verticalCode;

    /**
     * 服务类型
     */
    private String serviceType;

    /**
     * 履约单ID
     */
    private Long promiseId;

    /**
     * 服务单ID
     */
    private Long voucherId;

    /**
     * 服务兑换来源id
     */
    private String sourceVoucherId;

    /**
     * 冻结状态 0 正常 1 冻结
     */
    private Integer freeze;

    /**
     * 履约单序号，对于一个服务单需要多次履约的场景
     */
    private Integer serialNum;

    /**
     * 履约单状态 详见 JdhPromiseStatusEnum
     */
    private Integer promiseStatus;

    /**
     * 免预约、在线预约、商家预约
     */
    private Integer promiseType;

    /**
     * 过期时间
     */
    private Date expireDate;

    /**
     * 卡号ID
     */
    private String codeId;

    /**
     * 卡号
     */
    private String code;

    /**
     * 卡密
     */
    private String codePwd;

    /**
     * 履约渠道编号
     */
    private Long channelNo;

    /**
     * 预约单号
     */
    private Long appointmentId;

    /**
     * 渠道方的履约单号
     */
    private String channelAppointmentId;

    /**
     * 预约时间
     */
    private PromiseAppointmentTimeBo appointmentTime;

    /**
     * 受检人（使用服务的患者信息）
     */
    private List<PromisePatientBo> patients;

    /**
     * 预约地点
     */
    private PromiseStationBo store;

    /**
     * jdhPromiseServiceDetailList
     */
    private List<PromiseServiceDetailBo> services;

    /**
     * 扩展信息
     */
    private List<PromiseExtendBo> promiseExtends;

    /**
     * 数据来源分支
     */
    private String branch;

    /**
     * 版本
     */
    private Integer version;

    /**
     * 创建用户
     */
    private String createUser;

    /**
     * 更新用户
     */
    private String updateUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 预约人手机号，预约人是指当前操作预约的用户，被服务人和预约人可能不是同一个人
     */
    private String appointmentPhone;
}
