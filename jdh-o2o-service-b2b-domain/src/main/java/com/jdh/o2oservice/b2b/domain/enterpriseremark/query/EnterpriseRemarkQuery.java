package com.jdh.o2oservice.b2b.domain.enterpriseremark.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 企业备注查询对象
 *
 * <AUTHOR>
 * @date 2025/03/10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EnterpriseRemarkQuery {

    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 企业备注ID
     */
    private Long enterpriseRemarkId;
}
