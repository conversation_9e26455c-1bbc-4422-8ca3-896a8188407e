package com.jdh.o2oservice.b2b.domain.enterprisevoucher.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * IntendedNurse
 *
 * <AUTHOR>
 * @date 2025/02/27
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PromiseIntendedNurse {

    /**
     * 推荐类型 1-平台推荐 2-意向护士
     */
    private Integer recommendType = 1;

    /**
     * 护士id
     */
    private Long angelId;

    /**
     * 邀请码
     */
    private String invitationCode;
}