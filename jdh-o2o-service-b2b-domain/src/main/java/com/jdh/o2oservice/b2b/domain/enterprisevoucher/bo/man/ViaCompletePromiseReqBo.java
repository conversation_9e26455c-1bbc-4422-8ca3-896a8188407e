package com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.man;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Date 2025/2/28 下午2:20
 * <AUTHOR>
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ViaCompletePromiseReqBo implements Serializable {

    /**
     * 实验室检测单id
     */
    private Long medPromiseId;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 页面标识
     */
    private String pageCode;

    /**
     * promiseId
     */
    private Long promiseId;
}
