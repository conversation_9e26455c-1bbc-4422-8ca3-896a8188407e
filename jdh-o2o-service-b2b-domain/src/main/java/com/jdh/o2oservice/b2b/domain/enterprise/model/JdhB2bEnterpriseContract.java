package com.jdh.o2oservice.b2b.domain.enterprise.model;

import com.jdh.o2oservice.b2b.base.model.Aggregate;
import com.jdh.o2oservice.b2b.base.model.AggregateCode;
import com.jdh.o2oservice.b2b.base.model.DomainCode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class JdhB2bEnterpriseContract implements Aggregate<JdhEnterpriseContractIdentifier> {

    /**
     * id
     */
    private Long id;
    /**
     * 合同ID
     */
    private Long contractId;
    /**
     * 企业ID
     */
    private Long enterpriseId;
    /**
     * 合同编号
     */
    private String contractNumber;
    /**
     * 合同名称
     */
    private String name;
    /**
     * 合同主体
     */
    private Integer ou;
    /**
     * 有效标识：1/0
     */
    private Integer valid;
    /**
     * 合同开始时间
     */
    private Date startTime;
    /**
     * 合同结束时间
     */
    private Date endTime;
    /**
     * 合同状态
     */
    private Integer status;
    /**
     * 结算周期:1-日 2-月 3-季度 4-年 5-周 6-半月 7-其他时间
     */
    private Integer settlementPeriod;
    /**
     * 结算天数
     */
    private Integer settlementDays;
    /**
     * 合同扩展字段
     */
    private String extMap;
    /**
     * 扩展信息
     */
    private String extend;

    /**
     * 是否有效 1有效 0无效
     */
    private Integer yn;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 数据来源分支
     */
    private String branch;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updateUser;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 操作人
     */
    private String operator;

    @Override
    public DomainCode getDomainCode() {
        return null;
    }

    @Override
    public AggregateCode getAggregateCode() {
        return null;
    }

    /**
     * 版本
     *
     * @return {@link Integer}
     */
    @Override
    public Integer version() {
        return version;
    }

    /**
     * 版本增加
     */
    @Override
    public void versionIncrease() {
        version++;
    }

    /**
     * 获取标识符
     *
     * @return id
     */
    public JdhEnterpriseIdentifier getIdentifier() {
        return new JdhEnterpriseIdentifier(this.contractId);
    }
}