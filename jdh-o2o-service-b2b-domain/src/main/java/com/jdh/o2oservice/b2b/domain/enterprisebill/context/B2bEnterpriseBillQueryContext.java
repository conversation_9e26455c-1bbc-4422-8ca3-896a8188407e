package com.jdh.o2oservice.b2b.domain.enterprisebill.context;

import com.jdh.o2oservice.b2b.common.request.AbstractPageQuery;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;

/**
 * @ClassName:B2bEnterpriseBillQueryContext
 * @Description:B2b企业账单Context
 * @Author: liwenming
 * @Date: 2025/2/24 15:14
 * @Vserion: 1.0
 **/
@Data
public class B2bEnterpriseBillQueryContext extends AbstractPageQuery implements Serializable {
    /**
     *
     */
    private static final long serialVersionUID = -2406981332913100852L;
    /**
     * 企业Id
     */
    private Long enterpriseId;
    /**
     * 账单Id
     */
    private Long billId;
    /**
     * 账期
     */
    private String billDate;

    /**
     * 状态 1-正常 2-已取消(无责) 3-已取消(有责) 4-作废
     */
    private Integer status;
    /**
     * 履约单id
     */
    private Long promiseId;
    /**
     * sku名称集合
     */
    private Set<Long> enterpriseSkuIdSet;
    /**
     * 履约状态
     */
    private Set<Integer> promiseStatusSet;
    /**
     * 下单开始时间
     */
    private Date orderTimeStart;
    /**
     * 下单结束时间
     */
    private Date orderTimeEnd;

    /**
     * 企业服务单id
     */
    private Long enterpriseVoucherId;
}
