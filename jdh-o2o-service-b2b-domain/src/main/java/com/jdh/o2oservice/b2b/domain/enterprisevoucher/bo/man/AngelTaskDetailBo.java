package com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.man;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description 服务任务明细
 * @Date 2025/2/28 下午3:06
 * <AUTHOR>
 **/
@Data
public class AngelTaskDetailBo implements Serializable {

    /**
     * 任务单Id
     */
    private Long taskId;

    /**
     * 服务工单Id
     */
    private Long workId;

    /**
     * 任务单状态：1=待服务，2=服务中，3=用户确认，4=服务完成，5=服务取消
     */
    private Integer	status;

    /**
     * 业务任务状态：0初始状态 21验证通过 22采样 23绑码 24送检中 25服务记录上传
     */
    private Integer bizExtStatus;

    /**
     * 扩展信息：被服务人姓名、出生年月日、性别、服务时间段（开始时间、结束时间）、服务地址、联系方式、就医记录图片、点子签名图片、
     */
    private String extend;

    /**
     * 被服务人Id
     */
    private String patientId;

    /**
     * 被服务人姓名
     */
    private String patientName;

    /**
     * 被服务人性别
     */
    private String patientGender;

    /**
     * 被服务人年龄
     */
    private String patientAge;

    /**
     * 被服务人地址
     */
    private String patientFullAddress;

    /**
     * 被服务人纬度
     */
    private String patientAddressLat;

    /**
     * 被服务人经度
     */
    private String patientAddressLng;

    /**
     * 服务开始时间
     */
    private Date serviceStartTime;

    /**
     * 服务结束时间
     */
    private Date serviceEndTime;

    /**
     * 计划出门时间
     */
    private Date planOutTime;

    /**
     * 就医证明图片地址
     */
    private List<String> medicalCertificateUrls;

    /**
     * 知情同意书签字地址
     */
    private String signatureLetterOfConsentUrl;
    /**
     * 知情同意书模版文件
     */
    private String letterOfConsentUrl;

    /**
     * 任务单历史信息
     */
    private List<AngelTaskHistoryBo> taskHistotryDtoList;
}
