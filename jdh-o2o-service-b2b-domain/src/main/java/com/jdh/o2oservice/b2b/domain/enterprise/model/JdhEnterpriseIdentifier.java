package com.jdh.o2oservice.b2b.domain.enterprise.model;
import com.jdh.o2oservice.b2b.base.model.Identifier;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

/**
 * 企业id
 *
 * <AUTHOR>
 * @date 2024/04/17
 */
@Data
@Builder
@AllArgsConstructor
public class JdhEnterpriseIdentifier implements Identifier {

    /**
     * 企业ID
     */
    private Long enterpriseId;

    @Override
    public String serialize() {
        return String.valueOf(enterpriseId);
    }
}
