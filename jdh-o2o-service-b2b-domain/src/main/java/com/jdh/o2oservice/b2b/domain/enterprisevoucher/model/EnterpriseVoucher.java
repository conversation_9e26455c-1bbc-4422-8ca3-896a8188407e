package com.jdh.o2oservice.b2b.domain.enterprisevoucher.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * EnterpriseVoucher
 *
 * <AUTHOR>
 * @date 2025/02/27
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EnterpriseVoucher {

    /**
     * 主键
     */
    private Long id;

    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 企业服务单id
     */
    private Long enterpriseVoucherId;

    /**
     * 服务单id
     */
    private Long voucherId;

    /**
     * 履约单id
     */
    private Long promiseId;

    /**
     * 企业服务skuId
     */
    private Long enterpriseSkuId;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * sku名称
     */
    private String skuName;

    /**
     * sku短名称
     */
    private String skuShortName;

    /**
     * 履约单状态：待接单/已接单/护士已出发/开始服务/服务完成/已取消
     */
    private Integer promiseStatus;

    /**
     * 用户pin
     */
    private String customerUserPin;

    /**
     * 企业用户id
     */
    private Long enterpriseUserId;

    /**
     * 用户名称
     */
    private String enterpriseUserName;

    /**
     * erp_pin
     */
    private String erpPin;

    /**
     * 预约人电话
     */
    private String appointmentPhone;

    /**
     * 预约人姓名
     */
    private String appointmentName;

    /**
     * 卡号
     */
    private String code;

    /**
     * 源订单号
     */
    private String sourceOrderId;

    /**
     * 源订单平台
     */
    private String sourceOrderPlatform;

    /**
     * 扩展信息
     */
    private EnterpriseVoucherExtend extend;
    /**
     * 是否统计 1有效 0无效
     */
    private Integer statistics;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 金额
     */
    private BigDecimal totalAmount;
}
