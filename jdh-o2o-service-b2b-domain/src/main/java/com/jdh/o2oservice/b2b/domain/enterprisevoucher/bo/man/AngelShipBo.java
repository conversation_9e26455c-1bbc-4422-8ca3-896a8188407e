package com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.man;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description 服务运单
 * @Date 2025/2/28 下午3:08
 * <AUTHOR>
 **/
@Data
public class AngelShipBo implements Serializable {

    /**
     * 运单Id
     */
    private Long shipId;

    /**
     * 服务工单Id
     */
    private Long angelWorkId;

    /**
     * 外部运单Id
     */
    private String outShipId;

    /**
     * 运单类型：1=自行寄送，2=达达
     */
    private Integer type;

    /**
     * 骑手Id
     */
    private String transferId;

    /**
     * 骑手姓名
     */
    private String transferName;

    /**
     * 骑手联系方式
     */
    private String transferPhone;

    /**
     * 发件人姓名
     */
    private String senderName;

    /**
     * 发件全地址
     */
    private String senderFullAddress;

    /**
     * 运单状态：
     */
    private Integer shipStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 预计呼叫时间
     */
    private Date planCallTime;

    /**
     * 预计接单时间
     */
    private Date estimateGrabTime;

    /**
     * 预计取件时间
     */
    private Date estimatePickUpTime;

    /**
     * 预计完单时间
     */
    private Date estimateReceiveTime;

    /**
     * 运单历史明细
     */
    private List<AngelShipHistoryBo> shipHistoryDtoList;

    /**
     * 骑手经度
     */
    private String transporterLng;

    /**
     * 骑手纬度
     */
    private String transporterLat;
}
