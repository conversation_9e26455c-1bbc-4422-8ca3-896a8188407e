package com.jdh.o2oservice.b2b.domain.enterpriseaccount.repository.model;

import com.jdh.o2oservice.b2b.base.model.Identifier;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

/**
 * 企业账户id
 */
@Data
@Builder
@AllArgsConstructor
public class JdhEnterpriseAccountIdentifier implements Identifier {

    /**
     * 账户Id
     */
    private Long accountId;

    @Override
    public String serialize() {
        return String.valueOf(accountId);}
}
