package com.jdh.o2oservice.b2b.domain.enterprisevoucher.cmd;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * EnterpriseVoucherQuery
 *
 * <AUTHOR>
 * @date 2025/02/27
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EnterpriseVoucherCmd {

    /**
     * enterpriseVoucherId
     */
    private Long enterpriseVoucherId;

    /**
     * 履约单id
     */
    private Long promiseId;

    /**
     * enterpriseVoucherIdList
     */
    private List<Long> enterpriseVoucherIdList;

}
