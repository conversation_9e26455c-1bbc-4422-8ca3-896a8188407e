package com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * PromiseTimelineRequest
 *
 * <AUTHOR>
 * @date 2024/05/07
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryPromiseTimelineBo implements Serializable {

    /**
     * 履约单id
     */
    private Long promiseId;
}
