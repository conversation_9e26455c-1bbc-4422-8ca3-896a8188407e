package com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 导入履约单Excel模版行数据实体
 */
@Data
public class ImportVoucherExcelRowRecord {
    /**
     * 预约项目
     */
    @ExcelProperty(value = "预约项目")
    @NotNull(message = "预约项目不能为空")
    private String enterpriseSkuId;

    /**
     * 上门地址
     */
    @ExcelProperty(value = "上门地址")
    @NotNull(message = "上门地址不能为空")
    private String fullAddress;

    /**
     * 联系人
     */
    @ExcelProperty(value = "联系人")
    @NotNull(message = "联系人不能为空")
    private String appointmentName;

    /**
     * 联系人
     */
    @ExcelProperty(value = "手机号")
    @NotNull(message = "手机号不能为空")
    private String appointmentPhone;

    /**
     * 上门时间
     */
    @ExcelProperty(value = "上门时间")
    @NotNull(message = "上门时间不能为空")
    private String appointmentStartDateTime;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 源单号
     */
    @ExcelProperty(value = "源订单号")
    private String sourceOrderId;

    /**
     * 源单平台
     */
    @ExcelProperty(value = "源订单平台")
    private String sourceOrderPlatform;

    /**
     * 被服务人1
     */
    @ExcelProperty(value = "被服务人1")
    @NotNull(message = "被服务人1不能为空")
    private String patientName1;

    /**
     * 性别1
     */
    @ExcelProperty(value = "性别1")
    @NotNull(message = "性别1不能为空")
    private String patientGender1;

    /**
     * 年龄1
     */
    @ExcelProperty(value = "年龄1")
    @NotNull(message = "年龄1不能为空")
    private String patientAge1;

    /**
     * 手机号1
     */
    @ExcelProperty(value = "手机号1")
    @NotNull(message = "手机号1不能为空")
    private String patientPhone1;

    /**
     * 被服务人2
     */
    @ExcelProperty(value = "被服务人2")
    private String patientName2;

    /**
     * 性别2
     */
    @ExcelProperty(value = "性别2")
    private String patientGender2;

    /**
     * 年龄2
     */
    @ExcelProperty(value = "年龄2")
    private String patientAge2;

    /**
     * 手机号2
     */
    @ExcelProperty(value = "手机号2")
    private String patientPhone2;

    /**
     * 被服务人3
     */
    @ExcelProperty(value = "被服务人3")
    private String patientName3;

    /**
     * 性别3
     */
    @ExcelProperty(value = "性别3")
    private String patientGender3;

    /**
     * 年龄3
     */
    @ExcelProperty(value = "年龄3")
    private String patientAge3;

    /**
     * 手机号3
     */
    @ExcelProperty(value = "手机号3")
    private String patientPhone3;

    /**
     * 被服务人4
     */
    @ExcelProperty(value = "被服务人4")
    private String patientName4;

    /**
     * 性别4
     */
    @ExcelProperty(value = "性别4")
    private String patientGender4;

    /**
     * 年龄4
     */
    @ExcelProperty(value = "年龄4")
    private String patientAge4;

    /**
     * 手机号4
     */
    @ExcelProperty(value = "手机号4")
    private String patientPhone4;

    /**
     * 被服务人5
     */
    @ExcelProperty(value = "被服务人5")
    private String patientName5;

    /**
     * 性别5
     */
    @ExcelProperty(value = "性别5")
    private String patientGender5;

    /**
     * 年龄5
     */
    @ExcelProperty(value = "年龄5")
    private String patientAge5;

    /**
     * 手机号5
     */
    @ExcelProperty(value = "手机号5")
    private String patientPhone5;

    /**
     * 上传状态标记
     */
    @ExcelProperty(value = "上传状态标记")
    private String isPassed;
    /**
     * 错误信息
     */
    @ExcelProperty(value = "错误信息")
    private String errMsg;

    /**
     * sheetName
     */
    private String sheetName;
    /**
     * key描述
     */
    private String keyDesc;
    /**
     * key的值
     */
    private String keyValue;

}