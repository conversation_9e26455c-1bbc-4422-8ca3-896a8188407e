package com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo;

import lombok.Data;

import java.util.Date;

/**
 * O2oPromiseAppointmentTimeBo
 *
 * <AUTHOR>
 * @date 2025/02/27
 */
@Data
public class O2oPromiseAppointmentTimeBo {

    /**
     * 预约时间类型1-按日 2-按时间段
     * 类型为1时，必传appointmentDate
     * 类型为2时，必传appointmentBeginTime、appointmentEndTime
     * 类型为4时，必传appointmentDate、timeRange
     */
    protected Integer dateType;

    /**
     * 预约开始时间
     * 格式 yyyy-MM-dd HH:mm
     */
    protected Date appointmentStartTime;

    /**
     * 预约结束时间
     * 格式 yyyy-MM-dd HH:mm
     */
    protected Date appointmentEndTime;

    /**
     * 是否是立即预约
     */
    private Boolean isImmediately;
}
