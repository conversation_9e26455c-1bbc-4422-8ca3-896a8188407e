package com.jdh.o2oservice.b2b.domain.enterprisebill.model;

import com.jdh.o2oservice.b2b.base.model.Identifier;
import lombok.Builder;
import lombok.Data;

/**
 *
 * @Author: liwenming
 * @Date: 2025/2/24 9:24 上午
 * @Description:
 */
@Data
@Builder
public class JdhB2bEnterpriseBillDetailIdentifier implements Identifier {
    /**
     * 账单Id
     */
    private Long billDetailId;

    @Override
    public String serialize() {
        return String.valueOf(billDetailId);
    }

    public JdhB2bEnterpriseBillDetailIdentifier() {
    }

    public JdhB2bEnterpriseBillDetailIdentifier(Long billDetailId) {
        this.billDetailId = billDetailId;
    }
}
