package com.jdh.o2oservice.b2b.domain.enterprise.model;
import com.jdh.o2oservice.b2b.base.model.Aggregate;
import com.jdh.o2oservice.b2b.base.model.AggregateCode;
import com.jdh.o2oservice.b2b.base.model.DomainCode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class JdhB2bEnterprise implements Aggregate<JdhEnterpriseIdentifier> {

    /**
     * id
     */
    private Long id;

    /**
     * 垂直业务身份编码
     */
    private String verticalCode;

    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 状态 1-启用 0-禁用
     */
    private Integer available;

    /**
     * 企业名称
     */
    private String name;

    /**
     * 企业短名称
     */
    private String shortName;

    /**
     * 允许指定意向护士 0-否 1-是
     */
    private Integer needIntendedNurse;

    /**
     * 负责人
     */
    private String director;

    /**
     * 内部备注
     */
    private String internalRemark;

    /**
     * 项目资料
     */
    private String projectInformation;

    /**
     * 扩展信息
     */
    private String extend;

    /**
     * 是否有效 1有效 0无效
     */
    private Integer yn;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 数据来源分支
     */
    private String branch;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updateUser;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 操作人
     */
    private String operator;


    /**
     * 展示电子护理单 0-否 1-是
     */
    private Integer needAngelServiceRecord;


    /**
     * 启用API 0-否 1-是
     */
    private Integer needApi;


    /**
     * 是否展示履约时间轴 0-否 1-是
     */
    private Integer needPromiseTimeline;

    @Override
    public DomainCode getDomainCode() {
        return null;
    }

    @Override
    public AggregateCode getAggregateCode() {
        return null;
    }

    /**
     * 版本
     *
     * @return {@link Integer}
     */
    @Override
    public Integer version() {
        return version;
    }

    /**
     * 版本增加
     */
    @Override
    public void versionIncrease() {
        version++;
    }

    /**
     * 获取标识符
     *
     * @return id
     */
    public JdhEnterpriseIdentifier getIdentifier() {
        return new JdhEnterpriseIdentifier(this.enterpriseId);
    }
}