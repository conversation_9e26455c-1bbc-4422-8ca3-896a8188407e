package com.jdh.o2oservice.b2b.domain.enterprisesku.rpc.bo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class AngelSkillDictBo implements Serializable {

    /**
     * 主键
     */
    private Long id;

    /**
     * 服务者技能id
     */
    private Long angelSkillId;

    /**
     * 服务者技能code（全局唯一）
     */
    private String angelSkillCode;

    /**
     * 服务者技能名称
     */
    private String angelSkillName;

    /**
     * 服务类型 1:供应商体检项目 2:检测类 3:护理类
     */
    private Integer itemType;

    /**
     * 创建人pin
     */
    private String createUser;

    /**
     * 创建日期
     */
    private Date createTime;

    /**
     * 修改人pin
     */
    private String updateUser;

    /**
     * 修改日期
     */
    private Date updateTime;

    /**
     * 有效标志 0-无效 1-有效
     */
    private Integer yn;
}
