package com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.man;

import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * @Description 作废履约单命令
 * @Date 2025/2/28 下午1:38
 * <AUTHOR>
 **/
@Data
public class InvalidVoucherBo implements Serializable {

    /**
     * voucherId
     */
    private Long voucherId;
    /**
     *
     */
    private String reason;


    /**
     * 作废的信息
     */
    private List<PromisePatientBo> promisePatient;

    /**
     *
     */
    private List<PromiseServiceDetailBo> invalidService;

    /**
     * 1：以人维度作废；
     * 2：以服务维度作废；
     * 3：整单（履约单）作废
     */
    private Integer invalidType;
}
