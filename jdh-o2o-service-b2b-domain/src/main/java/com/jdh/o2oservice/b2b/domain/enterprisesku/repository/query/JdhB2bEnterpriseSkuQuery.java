package com.jdh.o2oservice.b2b.domain.enterprisesku.repository.query;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description 企业服务项目
 * @Date 2025/2/25 下午6:43
 * <AUTHOR>
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class JdhB2bEnterpriseSkuQuery {

    /**
     * id
     */
    private Long id;

    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 企业服务skuId
     */
    private Long enterpriseSkuId;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 企业ids
     */
    private List<Long> enterpriseIdList;

    /**
     * 合同编号
     */
    private String contractNumber;

    /**
     * 企业服务skuId
     */
    private List<Long> enterpriseSkuIdList;

    /**
     * <pre>
     * 分页
     * </pre>
     */
    private int pageNum = 1;

    /**
     * <pre>
     * 分页
     * </pre>
     */
    private int pageSize = 10;

}
