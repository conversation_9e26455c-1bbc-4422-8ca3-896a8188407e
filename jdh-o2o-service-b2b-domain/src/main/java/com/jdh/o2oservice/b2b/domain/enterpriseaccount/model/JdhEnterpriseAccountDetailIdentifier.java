package com.jdh.o2oservice.b2b.domain.enterpriseaccount.model;
import com.jdh.o2oservice.b2b.base.model.Identifier;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

/**
 * 企业id
 *
 * <AUTHOR>
 * @date 2024/04/17
 */
@Data
@Builder
@AllArgsConstructor
public class JdhEnterpriseAccountDetailIdentifier implements Identifier {

    /**
     * 账户明细Id
     */
    private Long accountDetailId;

    @Override
    public String serialize() {
        return String.valueOf(accountDetailId);
    }
}
