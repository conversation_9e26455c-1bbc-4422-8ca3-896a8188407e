package com.jdh.o2oservice.b2b.domain.enterprisevoucher.factory;

import com.jdh.o2oservice.b2b.domain.enterprisevoucher.ctx.SubmitPromiseCtx;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.model.EnterpriseVoucher;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.model.EnterpriseVoucherExtend;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.model.PromiseAddress;

/**
 * EnterpriseVoucherFactory
 *
 * <AUTHOR>
 * @date 2025/02/27
 */
public class EnterpriseVoucherFactory {

    /**
     * createEnterpriseVoucher
     *
     * @param ctx ctx
     * @return {@link EnterpriseVoucher }
     */
    public static EnterpriseVoucher createEnterpriseVoucher(SubmitPromiseCtx ctx) {
        EnterpriseVoucher enterpriseVoucher = EnterpriseVoucher.builder().build();
        enterpriseVoucher.setEnterpriseId(ctx.getEnterpriseId());
        enterpriseVoucher.setEnterpriseVoucherId(ctx.getEnterpriseVoucherId());
        enterpriseVoucher.setVoucherId(ctx.getVoucherId());
        enterpriseVoucher.setPromiseId(ctx.getPromiseId());
        enterpriseVoucher.setEnterpriseSkuId(ctx.getEnterpriseSkuId());
        enterpriseVoucher.setSkuId(ctx.getSkuId());
        enterpriseVoucher.setSkuName(ctx.getSkuName());
        enterpriseVoucher.setSkuShortName(ctx.getSkuShortName());
        enterpriseVoucher.setPromiseStatus(ctx.getPromiseStatus());
        enterpriseVoucher.setCustomerUserPin(ctx.getCustomerUserPin());
        enterpriseVoucher.setEnterpriseUserId(ctx.getEnterpriseUserId());
        enterpriseVoucher.setEnterpriseUserName(ctx.getEnterpriseUserName());
        enterpriseVoucher.setErpPin(ctx.getErpPin());
        enterpriseVoucher.setSourceOrderId(ctx.getSourceOrderId());
        enterpriseVoucher.setAppointmentName(ctx.getAppointmentName());
        enterpriseVoucher.setAppointmentPhone(ctx.getAppointmentPhone());
        enterpriseVoucher.setSourceOrderPlatform(ctx.getSourceOrderPlatform());

        EnterpriseVoucherExtend extend = EnterpriseVoucherExtend.builder().build();
        PromiseAddress address = ctx.getExtend().getAddress();
        address.setName(ctx.getAppointmentName());
        address.setMobile(ctx.getAppointmentPhone());
        extend.setAddress(address);
        extend.setPatientList(ctx.getExtend().getPatientList());
        extend.setService(ctx.getExtend().getService());
        extend.setAppointmentTime(ctx.getExtend().getAppointmentTime());
        extend.setIntendedNurse(ctx.getExtend().getIntendedNurse());
        enterpriseVoucher.setExtend(extend);

        return enterpriseVoucher;
    }


}
