package com.jdh.o2oservice.b2b.domain.enterpriseremark.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.b2b.domain.enterpriseremark.model.EnterpriseRemark;
import com.jdh.o2oservice.b2b.domain.enterpriseremark.query.EnterpriseRemarkPageQuery;
import com.jdh.o2oservice.b2b.domain.enterpriseremark.query.EnterpriseRemarkQuery;

import java.util.List;

/**
 * 企业备注仓储接口
 *
 * <AUTHOR>
 * @date 2025/03/10
 */
public interface EnterpriseRemarkRepository {

    /**
     * 保存企业备注
     *
     * @param enterpriseRemark 企业备注
     * @return 影响行数
     */
    Integer save(EnterpriseRemark enterpriseRemark);

    /**
     * 查询企业备注
     *
     * @param query 查询条件
     * @return 企业备注
     */
    EnterpriseRemark queryEnterpriseRemark(EnterpriseRemarkQuery query);

    /**
     * 分页查询企业备注
     *
     * @param query 查询条件
     * @return 分页结果
     */
    Page<EnterpriseRemark> queryEnterpriseRemarkPage(EnterpriseRemarkPageQuery query);

    /**
     * 查询企业备注列表
     *
     * @param query 查询条件
     * @return 企业备注列表
     */
    List<EnterpriseRemark> queryEnterpriseRemarkList(EnterpriseRemarkQuery query);

    /**
     * 删除企业备注
     *
     * @param enterpriseRemark 企业备注
     * @return 影响行数
     */
    Integer delete(EnterpriseRemark enterpriseRemark);
}
