package com.jdh.o2oservice.b2b.domain.enterprisevoucher.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * EnterpriseVoucherExtend
 *
 * <AUTHOR>
 * @date 2025/02/27
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EnterpriseVoucherExtend {

    /**
     * address
     */
    private PromiseAddress address;

    /**
     * patientList
     */
    private List<PromisePatient> patientList;

    /**
     * service
     */
    private PromiseSku service;

    /**
     * appointmentTime
     */
    private PromiseAppointmentTime appointmentTime;

    /**
     * 意向护士
     */
    private PromiseIntendedNurse intendedNurse;
    /**
     * 备注
     */
    private String remark;

    /**
     * 合同编号
     */
    private String contractNumber;

    // 取消完成时间
    private Date finishTime;

    // 取消责任类型；0无责取消，1出门有责取消，2服务中有责取消, 3服务完成有责取消
    private Integer cancelResponsibleType;
}
