package com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.man;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description
 * @Date 2025/2/28 下午1:39
 * <AUTHOR>
 **/
@Data
public class PromisePatientBo implements Serializable {

    /**
     * promiseId
     */
    private Long promiseId;

    /**
     * promisePatientId
     */
    private Long promisePatientId;


    /**
     * version
     */
    private Integer version;

    /**
     * userPin
     */
    private String userPin;

    /**
     * 患者id
     */
    private Long patientId;

    /**
     * 生日
     */
    private BirthdayBo birthday;

    /**
     * 性别
     */
    private Integer gender;

    /**
     * 婚否
     */
    private Integer marriage;

    /**
     * 预约人姓名
     */
    private UserNameBo userName;

    /**
     * 预约人手机号
     */
    private PhoneNumberBo phoneNumber;

    /**
     * 证件号码（加密）
     */
    private CredentialNumberBo credentialNum;

    /**
     * 亲属类型:1本人 21-父母 22-配偶 23-子女 34-其他
     */
    private Integer relativesType;
    /**
     *
     */
    private List<PromiseServiceDetailBo> serviceDetails;
    /**
     * 样本信息
     */
    private List<MedicalPromiseBo> medicalPromiseDetails;
    /**
     * 头像
     */
    private String patientHeaderImage;
}
