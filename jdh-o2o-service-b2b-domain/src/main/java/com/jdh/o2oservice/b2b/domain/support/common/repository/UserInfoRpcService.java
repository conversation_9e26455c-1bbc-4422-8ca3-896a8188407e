package com.jdh.o2oservice.b2b.domain.support.common.repository;

import com.jdh.o2oservice.b2b.domain.support.common.bo.UserBaseInfoBo;

public interface UserInfoRpcService {

    Integer LOAD_TYPE_ONE = 1;
    Integer LOAD_TYPE_TWO = 2;
    Integer LOAD_TYPE_THREE = 3;
    /**
     *  90为Bpin 非90 为Cpin
     *  https://cf.jd.com/pages/viewpage.action?pageId=100092999
     * @param pin
     * @return
     */
    Boolean isBpin(String pin);

    /**
     * 根据pin获取用户基本信息
     * @param pin 用户的pin码
     * @param loadType 加载类型，用于区分不同的加载场景
     * @return 用户基本信息对象
     */
    UserBaseInfoBo getUserBaseInfoByPin(String pin, int loadType);
}
