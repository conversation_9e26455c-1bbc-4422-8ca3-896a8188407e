package com.jdh.o2oservice.b2b.domain.enterpriseuser.model;

import com.jdh.o2oservice.b2b.base.model.Identifier;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

/**
 * 企业用户id
 */
@Data
@Builder
@AllArgsConstructor
public class JdhEnterpriseUserIdentifier implements Identifier {
    /**
     * 企业用户id
     */
    private Long enterpriseUserId;

    @Override
    public String serialize() {
        return String.valueOf(enterpriseUserId);}
}

