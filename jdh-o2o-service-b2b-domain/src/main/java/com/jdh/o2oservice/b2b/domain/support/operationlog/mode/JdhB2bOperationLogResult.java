package com.jdh.o2oservice.b2b.domain.support.operationlog.mode;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class JdhB2bOperationLogResult implements Serializable {

    /**
     * 附件链接
     */
    private String attachment;
    /**
     * 附件名称
     */
    private String attachmentName;
    /**
     * 结果附件链接
     */
    private String resultAttachment;
    /**
     * 结果附件名称
     */
    private String resultAttachmentName;
}
