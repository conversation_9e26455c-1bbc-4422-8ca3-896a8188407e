package com.jdh.o2oservice.b2b.domain.support.operationlog.repository.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class JdhB2bOperationLogQuery implements Serializable {

    /**
     * id
     */
    private Long id;

    /**
     * 日志id
     */
    private Long operationLogId;

    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 操作类型 1-新增 2-删除 3-修改 4-查询 5-上传 6-下载
     */
    private Integer operateType;

    /**
     * 结果类型 0-未知 1-处理中 2-成功 3-失败 4-部分失败
     */
    private Integer resultType;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 当前页
     */
    private Integer pageNum = 1;

    /**
     * 每页条数
     */
    private Integer pageSize = 10;
}
