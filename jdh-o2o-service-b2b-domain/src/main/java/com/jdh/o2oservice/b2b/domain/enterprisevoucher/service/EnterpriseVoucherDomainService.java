package com.jdh.o2oservice.b2b.domain.enterprisevoucher.service;

import com.jdh.o2oservice.b2b.domain.enterprisevoucher.ctx.SubmitPromiseCtx;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.model.EnterpriseVoucher;

/**
 * EnterpriseVoucherDomainService
 *
 * <AUTHOR>
 * @date 2025/02/27
 */
public interface EnterpriseVoucherDomainService {

    /**
     * createAndSubmitPromise
     *
     * @param ctx ctx
     * @return {@link EnterpriseVoucher }
     */
    EnterpriseVoucher createAndSubmitPromise(SubmitPromiseCtx ctx);
}
