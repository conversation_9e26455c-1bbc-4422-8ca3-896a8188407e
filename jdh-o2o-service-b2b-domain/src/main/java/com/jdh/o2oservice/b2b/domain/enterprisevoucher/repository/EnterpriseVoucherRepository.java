package com.jdh.o2oservice.b2b.domain.enterprisevoucher.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.model.EnterpriseVoucher;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.query.EnterpriseVoucherPageQuery;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.query.EnterpriseVoucherQuery;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.cmd.EnterpriseVoucherCmd;

import java.util.List;

/**
 * EnterpriseVoucherRepository
 *
 * <AUTHOR>
 * @date 2025/02/27
 */
public interface EnterpriseVoucherRepository {

    /**
     * saveEnterpriseVoucher
     *
     * @param enterpriseVoucher enterpriseVoucher
     * @return {@link Integer }
     */
    Integer save(EnterpriseVoucher enterpriseVoucher);

    /**
     * queryEnterpriseVoucher
     *
     * @param enterpriseVoucherQuery enterpriseVoucherQuery
     * @return {@link EnterpriseVoucher }
     */
    EnterpriseVoucher queryEnterpriseVoucher(EnterpriseVoucherQuery enterpriseVoucherQuery);

    /**
     * queryEnterpriseVoucherPage
     *
     * @param query 查询
     * @return {@link Page }<{@link EnterpriseVoucher }>
     */
    Page<EnterpriseVoucher> queryEnterpriseVoucherPage(EnterpriseVoucherPageQuery query);

    /**
     * queryEnterpriseVoucherPage
     *
     * @param query 查询
     * @return {@link Page }<{@link EnterpriseVoucher }>
     */
    List<EnterpriseVoucher> queryEnterpriseVoucherList(EnterpriseVoucherPageQuery query);

    /**
     * updateVoucherStatistics
     *
     * @param enterpriseVoucherCmd
     * @return {@link Integer }
     */
    Integer updateVoucherStatistics(EnterpriseVoucherCmd enterpriseVoucherCmd);
}
