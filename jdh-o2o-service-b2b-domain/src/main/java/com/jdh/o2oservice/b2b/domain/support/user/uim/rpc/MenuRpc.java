package com.jdh.o2oservice.b2b.domain.support.user.uim.rpc;

import com.jdh.o2oservice.b2b.domain.support.user.uim.bo.Uim2MenuBO;
import com.jdh.o2oservice.b2b.domain.support.user.uim.bo.UserBO;
import com.jdh.o2oservice.b2b.domain.support.user.uim.param.Uim2UserContext;

import java.util.List;

/**
 * @Author: duanqi<PERSON>na1
 * @Date: 2024/4/25 9:33 上午
 * @Description:
 */
public interface MenuRpc {


    /**
     * 查询菜单
     * @param userContext
     * @return
     */
    List<Uim2MenuBO> getMenuTree(Uim2UserContext userContext);


    /**
     *
     * @param erpCode
     * @return
     */
    UserBO getUserInfo(String erpCode);
}
