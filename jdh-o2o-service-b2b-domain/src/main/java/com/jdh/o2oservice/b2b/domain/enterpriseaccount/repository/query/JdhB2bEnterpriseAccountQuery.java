package com.jdh.o2oservice.b2b.domain.enterpriseaccount.repository.query;
import com.jdh.o2oservice.b2b.common.request.AbstractPageQuery;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class JdhB2bEnterpriseAccountQuery extends AbstractPageQuery implements Serializable {

    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 账户Id
     */
    private Long accountId;

    /**
     * 企业id
     */
    private List<Long> enterpriseIdList;
}
