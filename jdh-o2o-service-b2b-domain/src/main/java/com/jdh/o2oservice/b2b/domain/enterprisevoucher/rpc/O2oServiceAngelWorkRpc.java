package com.jdh.o2oservice.b2b.domain.enterprisevoucher.rpc;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.O2oAngelWorkBo;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.O2oAngelWorkQueryBo;

import java.util.List;

public interface O2oServiceAngelWorkRpc {

    /**
     * 查询工单列表
     * @param bo
     * @return
     */
    List<O2oAngelWorkBo> getAngelWorkList(O2oAngelWorkQueryBo bo);
}
