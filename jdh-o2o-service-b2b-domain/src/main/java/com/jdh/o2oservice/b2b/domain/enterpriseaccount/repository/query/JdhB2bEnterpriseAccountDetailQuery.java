package com.jdh.o2oservice.b2b.domain.enterpriseaccount.repository.query;

import com.jdh.o2oservice.b2b.common.request.AbstractPageQuery;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Description 企业账户
 * @Date 2025/2/25 下午6:45
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class JdhB2bEnterpriseAccountDetailQuery extends AbstractPageQuery implements Serializable {

    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 账户Id
     */
    private Long accountId;

    /**
     * 账户明细Id
     */
    private Long accountDetailId;

    /**
     * 履约单号
     */
    private Long promiseId;

    /**
     * 来源单据类型：1:voucher, 2:账单打款
     */
    private Integer sourceReceiptType;
    /**
     * 类型：1-冻结 2-释放
     */
    private Integer freezeType;

    /**
     * 来源单据id
     */
    private Long sourceReceiptId;
    /**
     * 来源单据id
     */
    private List<Long> sourceReceiptIdList;
}
