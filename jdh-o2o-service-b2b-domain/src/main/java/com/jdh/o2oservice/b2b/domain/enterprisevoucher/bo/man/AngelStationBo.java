package com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.man;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Date 2025/2/28 下午3:01
 * <AUTHOR>
 **/
@Data
public class AngelStationBo implements Serializable {

    /**
     * 服务站id
     */
    private Long angelStationId;

    /**
     * 围栏id
     */
    private String fenceId;

    /**
     * 服务站名称
     */
    private String angelStationName;

    /**
     * 地图id
     */
    private Long mapId;

    /**
     * 图层id
     */
    private Long layerId;

    /**
     * 范围的衡量类型（1：公里数，2：分钟）
     */
    private Integer fenceRangeType;

    /**
     * 范围的衡量值（如xx公里或xx分钟）
     */
    private Integer fenceRangeRadius;

    /**
     * 圆中心点纬度
     */
    private String fenceRangeCenterLat;

    /**
     * 圆中心点经度
     */
    private String fenceRangeCenterLng;

    /**
     * 站长erp
     */
    private String stationMaster;

    /**
     * 省编码
     */
    private String provinceCode;

    /**
     * 省名称
     */
    private String provinceName;

    /**
     * 市编码
     */
    private String cityCode;

    /**
     * 市名称
     */
    private String cityName;

    /**
     * 区编码
     */
    private String districtCode;

    /**
     * 区名称
     */
    private String districtName;

    /**
     * 县编码
     */
    private String countyCode;

    /**
     * 县名称
     */
    private String countyName;

    /**
     * 详细地址
     */
    private String addressDetail;

    /**
     * 全地址
     */
    private String fullAddress;

    /**
     * 站点状态：1启用 2停用
     */
    private Integer stationStatus;

    /**
     * 开始营业时间(24小时)
     */
    private String openHour;

    /**
     * 停止营业时间(24小时)
     */
    private String closeHour;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updateUser;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 围栏形状 空+1:圆形 2多边形
     */
    private Integer fenceShapeType;

    /**
     * 坐标集合 数据格式: [ [116.562913321,39.79562447840648],[116.562913321,39.79562447840648] ]
     */
    private List<List<Double>> fenceBoundaryList;

    /**
     * 服务资源类型 二进制编码 右向左 1位骑手 2位护士
     */
    private List<Integer> angelTypes;

    /**
     * 骑手供应商 服务资源类型 二进制编码 右向左 1位达达 2位顺丰
     */
    private List<Integer> deliverySuppliers;


    /**
     * 实验室id
     */
    private String stationId;

    /**
     * 实验室名称
     */
    private String stationName;

    /**
     * 服务资源数量
     */
    private Integer angelNum;

    /**
     * 库存异常调仓记录列表
     */
    private List<InventoryReadjustBo> inventoryReadjustDtoList;

    /**
     * 三方店铺id
     */
    private String outShopId;

}
