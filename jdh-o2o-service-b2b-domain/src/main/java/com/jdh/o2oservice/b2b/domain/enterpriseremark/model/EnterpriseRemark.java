package com.jdh.o2oservice.b2b.domain.enterpriseremark.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 企业备注领域模型
 *
 * <AUTHOR>
 * @date 2025/03/10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EnterpriseRemark {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 企业备注ID
     */
    private Long enterpriseRemarkId;

    /**
     * 备注内容
     */
    private String content;

    /**
     * 数据来源分支
     */
    private String branch;
    
    /**
     * 版本号
     */
    private Integer version;

    /**
     * 是否有效 0：无效；1：有效
     */
    private Integer yn;

    /**
     * 创建人pin
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人pin
     */
    private String updateUser;

    /**
     * 更新时间
     */
    private Date updateTime;
}
