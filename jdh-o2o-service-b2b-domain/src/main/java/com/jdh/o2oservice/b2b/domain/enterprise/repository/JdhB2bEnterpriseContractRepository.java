package com.jdh.o2oservice.b2b.domain.enterprise.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.b2b.domain.enterprise.model.JdhB2bEnterpriseContract;
import com.jdh.o2oservice.b2b.domain.enterprise.repository.query.JdhB2bEnterpriseContractQuery;

import java.util.List;

/**
 * @Description 企业合同
 * @Date 2025/3/11 下午6:27
 * <AUTHOR>
public interface JdhB2bEnterpriseContractRepository {

    /**
     * 保存
     *
     * @param jdhB2bEnterprise
     * @return count
     */
    int save(JdhB2bEnterpriseContract jdhB2bEnterprise);
    /**
     * 保存
     *
     * @param jdhB2bEnterpriseContract
     * @return count
     */
    int delete(JdhB2bEnterpriseContract jdhB2bEnterpriseContract);

    /**
     * 更新合同
     * @param jdhB2bEnterprise
     * @return
     */
    int updateContract(JdhB2bEnterpriseContract jdhB2bEnterprise);
    /**
     * 通过enterpriseContractQuery 查询
     * @param enterpriseContractQuery
     * @return
     */
    JdhB2bEnterpriseContract findJdhB2bEnterpriseContract(JdhB2bEnterpriseContractQuery enterpriseContractQuery);

    /**
     * 分页查询企业合同列表
     * @param enterpriseContractQuery
     * @return
     */
    Page<JdhB2bEnterpriseContract> queryPageEnterpriseContract(JdhB2bEnterpriseContractQuery enterpriseContractQuery);

    /**
     * 企业合同列表
     * @param enterpriseContractQuery
     * @return
     */
    List<JdhB2bEnterpriseContract> findContractList(JdhB2bEnterpriseContractQuery enterpriseContractQuery);
}
