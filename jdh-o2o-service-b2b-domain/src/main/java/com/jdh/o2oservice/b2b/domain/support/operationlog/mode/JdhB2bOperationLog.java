package com.jdh.o2oservice.b2b.domain.support.operationlog.mode;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
@Data
public class JdhB2bOperationLog implements Serializable {

    /**
     * id
     */
    private Long id;

    /**
     * 日志id
     */
    private Long operationLogId;

    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 业务场景key,默认:类全路径#方法
     */
    private String bizSceneKey;

    /**
     * 业务场景中文描述
     */
    private String bizSceneDesc;

    /**
     * 操作类型 1-新增 2-删除 3-修改 4-查询 5-上传 6-下载
     */
    private Integer operateType;

    /**
     * 业务对象唯一id,如skuId、angelId等
     */
    private String bizUnionId;

    /**
     * 请求端信息
     */
    private String clientInfo;

    /**
     * 入参信息
     */
    private String param;

    /**
     * 出参信息
     */
    private String result;

    /**
     * 结果类型 0-未知 1-成功 2-失败 3-处理中 4-部分失败
     */
    private Integer resultType;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作时间
     */
    private Date operateTime;

    /**
     * 扩展信息
     */
    private String extend;
}
