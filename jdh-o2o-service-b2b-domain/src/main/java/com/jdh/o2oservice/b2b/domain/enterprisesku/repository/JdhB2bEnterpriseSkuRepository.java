package com.jdh.o2oservice.b2b.domain.enterprisesku.repository;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.b2b.domain.enterprisesku.model.JdhB2bEnterpriseSku;
import com.jdh.o2oservice.b2b.domain.enterprisesku.model.JdhEnterpriseSkuIdentifier;
import com.jdh.o2oservice.b2b.domain.enterprisesku.repository.query.JdhB2bEnterpriseSkuQuery;

import java.util.List;

/**
 * @Description 企业sku
 * @Date 2025/2/24 下午6:28
 * <AUTHOR>
 **/
public interface JdhB2bEnterpriseSkuRepository {

    /**
     * 保存
     *
     * @param entity
     * @return count
     */
    int save(JdhB2bEnterpriseSku entity);

    /**
     * 更新
     *
     * @param entity
     * @return count
     */
    int update(JdhB2bEnterpriseSku entity);

    /**
     * 删除
     *
     * @param entity
     * @return count
     */
    int delete(JdhB2bEnterpriseSku entity);

    /**
     * 通过Identify 查询
     * @param identifier
     * @return
     */
    JdhB2bEnterpriseSku find(JdhEnterpriseSkuIdentifier identifier);

    /**
     * 分页查询企业sku列表
     * @param query
     * @return
     */
    Page<JdhB2bEnterpriseSku> queryPageEnterpriseSku(JdhB2bEnterpriseSkuQuery query);

    /**
     * 企业sku列表
     * @param query
     * @return
     */
    List<JdhB2bEnterpriseSku> findList(JdhB2bEnterpriseSkuQuery query);
}
