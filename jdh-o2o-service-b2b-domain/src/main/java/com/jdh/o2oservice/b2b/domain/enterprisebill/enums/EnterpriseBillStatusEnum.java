package com.jdh.o2oservice.b2b.domain.enterprisebill.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * @Author: 账单状态
 * @Date: 2025/2/25 15:38 上午
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum EnterpriseBillStatusEnum {

    // 账单状态 0-初始化 1-待确认 2-已确认 3-已打款 4-已到账 5-已作废
    STANDBY(0, "初始化"),
    CONFIRMING(1, "待确认"),
    CONFIRMED(2, "账单已确认"),
    PAID(3, "账单已打款"),
    ACCOUNT(4, "账单已结清"),
    INVALID(5, "账单已作废"),
    ;

    /**
     *
     */
    private Integer status;

    /**
     *
     */
    private String desc;

    /**
     * @param status
     * @return
     */
    public static String getBillStatusDescByStatus(Integer status) {
        if (Objects.isNull(status)) {
            return "";
        }
        for (EnterpriseBillStatusEnum billStatusEnum : EnterpriseBillStatusEnum.values()) {
            if (Objects.equals(billStatusEnum.getStatus(), status)) {
                return billStatusEnum.getDesc();
            }
        }
        return "";
    }
}
