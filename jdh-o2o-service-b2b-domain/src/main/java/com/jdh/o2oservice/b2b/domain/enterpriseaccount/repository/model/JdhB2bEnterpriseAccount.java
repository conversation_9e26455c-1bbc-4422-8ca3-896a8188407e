package com.jdh.o2oservice.b2b.domain.enterpriseaccount.repository.model;
import com.jdh.o2oservice.b2b.base.model.Aggregate;
import com.jdh.o2oservice.b2b.base.model.AggregateCode;
import com.jdh.o2oservice.b2b.base.model.DomainCode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;
import java.util.Date;
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class JdhB2bEnterpriseAccount implements Aggregate<JdhEnterpriseAccountIdentifier> {

    private Long id;

    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 账户Id
     */
    private Long accountId;

    /**
     * 信用额度
     */
    private BigDecimal creditAmount;

    /**
     * 冻结金额
     */
    private BigDecimal freezeAmount;

    /**
     * 扩展信息
     */
    private String extend;

    /**
     * 是否有效 1有效 0无效
     */
    private Integer yn;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 数据来源分支
     */
    private String branch;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 操作人
     */
    private String operator;

    @Override
    public DomainCode getDomainCode() {
        return null;
    }

    @Override
    public AggregateCode getAggregateCode() {
        return null;
    }

    /**
     * 版本
     *
     * @return {@link Integer}
     */
    @Override
    public Integer version() {
        return version;
    }

    /**
     * 版本增加
     */
    @Override
    public void versionIncrease() {
        version++;
    }

    /**
     * 获取标识符
     *
     * @return id
     */
    public JdhEnterpriseAccountIdentifier getIdentifier() {
        return new JdhEnterpriseAccountIdentifier(this.accountId);
    }
}
