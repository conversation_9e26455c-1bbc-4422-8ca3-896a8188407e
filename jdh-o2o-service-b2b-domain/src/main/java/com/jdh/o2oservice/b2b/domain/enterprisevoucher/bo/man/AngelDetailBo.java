package com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.man;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description 服务者详情
 * @Date 2025/3/3 下午1:21
 * <AUTHOR>
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AngelDetailBo implements Serializable {

    /**
     * 服务者id
     */
    private Long angelId;

    /**
     * 是否查询未开启技能
     */
    private Boolean isQueryNotOpenSkill;


}
