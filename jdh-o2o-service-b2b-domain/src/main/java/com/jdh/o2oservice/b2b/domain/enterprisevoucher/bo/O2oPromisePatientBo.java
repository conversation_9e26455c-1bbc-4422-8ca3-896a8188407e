package com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo;

import lombok.Data;

/**
 * O2oPromisePatientBo
 *
 * <AUTHOR>
 * @date 2025/02/27
 */
@Data
public class O2oPromisePatientBo {


    /**
     * promiseId
     */
    private Long promiseId;

    /**
     * promisePatientId
     */
    private Long promisePatientId;


    /**
     * version
     */
    private Integer version;

    /**
     * userPin
     */
    private String userPin;

    /**
     * 患者id
     */
    private Long patientId;

    /**
     * 生日
     */
    private String birthday;

    /**
     * 性别
     */
    private Integer gender;

    /**
     * 婚否
     */
    private Integer marriage;

    /**
     * 预约人姓名
     */
    private String userName;

    /**
     * 预约人手机号
     */
    private String phoneNumber;

    /**
     * 证件号码（加密）
     */
    private String credentialNum;

    /**
     * 亲属类型:1本人 21-父母 22-配偶 23-子女 34-其他
     */
    private Integer relativesType;

    /**
     * 头像
     */
    private String patientHeaderImage;
}
