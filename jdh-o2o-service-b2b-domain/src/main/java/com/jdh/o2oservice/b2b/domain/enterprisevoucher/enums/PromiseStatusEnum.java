package com.jdh.o2oservice.b2b.domain.enterprisevoucher.enums;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Getter;

import java.util.Map;
import java.util.Set;

/**
 * PromiseStatusEnum
 *
 * <AUTHOR>
 * @date 2023/12/14
 */
@Getter
public enum PromiseStatusEnum {

    /**
     * 初始状态
     */
    INIT(0, "待服务"),


    /**
     * 待接单
     */
    WAIT_RECEIVE(1, "待接单"),

    /**
     * 已接单
     */
    RECEIVED(2, "已接单"),

    /**
     * 待服务
     */
    WAIT_SERVICE(3, "已出门"),

    /**
     * 服务中
     */
    SERVICING(4, "服务中"),

    /**
     * 服务结束
     */
    SERVICED(5, "上门结束"),

    /**
     * 送检中
     */
    DELIVERING(6, "送检中"),

    /**
     * 服务完成
     */
    COMPLETED(7, "服务完成"),


    /**
     * 已取消
     */
    CANCEL(9, "已取消"),

    /**
     * 已过期
     */
    EXPIRED(10, "已过期"),

    ;

    /**
     * @param status
     * @param desc
     */
    PromiseStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    /**
     * status
     */
    private final Integer status;

    /**
     * desc
     */
    private final String desc;

    /**
     * STATUS_MAP
     */
    private static final Map<Integer, PromiseStatusEnum> STATUS_MAP = Maps.newHashMap();


    static {
        for (PromiseStatusEnum value : values()) {
            STATUS_MAP.put(value.getStatus(), value);
        }
    }

    /**
     * 终态
     */
    public static final Set<Integer> FINISH_STATUS = Sets.newHashSet(EXPIRED.status, COMPLETED.status, CANCEL.status);

    /**
     * 非终态
     */
    public static final Set<Integer> NON_FINISH_STATUS = Sets.newHashSet(INIT.status, WAIT_RECEIVE.status, RECEIVED.status, WAIT_SERVICE.status, SERVICING.status, SERVICED.status, DELIVERING.status);

    /**
     * 是否完成
     *
     * @return {@link Boolean}
     */
    public static Boolean isFinish(Integer status) {
        return FINISH_STATUS.contains(status);
    }

    public static PromiseStatusEnum getEnumByStatus(Integer status) {
            if (STATUS_MAP.containsKey(status)) {
                return STATUS_MAP.get(status);
            }
        return null;
    }
}
