package com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.man;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 视图 - 完整的promise dto
 * 包含：订单，C端履约，实验室履约，服务者履约，派单，运单相关信息
 * @Date 2025/2/28 下午2:27
 * <AUTHOR>
 **/
@Data
public class ViaCompletePromiseResBo implements Serializable {

    /**
     * 履约单
     */
    private PromiseBo promiseDto;

    /**
     * 服务者工单
     */
    private AngelWorkDetailForManBo angelWorkDto;

    /**
     * angel信息
     */
    private JdhAngelBo angelDto;

    /**
     * 履约时间轴
     */
    private List<ViaTimeLineItemBo> promiseTimeLine;

    /**
     * tagDto
     */
    private ManViaCompletePromiseTagBo tagInfo;

    /**
     * 显示按钮配置
     */
    private List<ManViaFloorBo> floorConfigList;

}
