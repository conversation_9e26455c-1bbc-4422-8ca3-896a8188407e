package com.jdh.o2oservice.b2b.domain.support.user.uim.context;
import com.jdh.o2oservice.b2b.domain.support.user.RequestContext;
import lombok.Data;

import java.util.List;

/**
 * 用户信息上下文
 *
 * <AUTHOR>
 * @date 2023-10-07 11:01
 */
@Data
public class UimUserContext {
    /**
     * 登录信息上下文
     */
    private UimLoginContext loginContext;

    /**
     * 权限信息上下文
     */
    private List<UimRoleContext> roleListContext;

    /**
     * 请求信息上线文
     */
    private RequestContext requestContext;

    /**
     * 用户信息
     */
    private static final ThreadLocal<UimUserContext> USER_HOLDER = new ThreadLocal<>();

    /**
     * 需要方法体加上UserPinCheck注解
     * 将userPin放入ThreadLocal
     *
     * @param userContext userContext
     */
    public static void put(UimUserContext userContext) {
        USER_HOLDER.set(userContext);
    }

    /**
     * 从ThreadLocal获取
     *
     * @return UserContext
     */
    public static UimUserContext get() {
        return USER_HOLDER.get();
    }

    /**
     * 将userPin从ThreadLocal移除
     */
    public static void remove() {
        USER_HOLDER.remove();
    }

    /**
     * 从ThreadLocal获取uerPin
     *
     * @return UserContext
     */
    public static String getPin() {
        UimUserContext userContext = USER_HOLDER.get();
        if (userContext == null || userContext.getLoginContext() == null) {
            return null;
        }
        return userContext.getLoginContext().getPin();
    }
}
