package com.jdh.o2oservice.b2b.domain.enterprisevoucher.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName AddressTextParseRpcParam
 * @Description
 * <AUTHOR>
 * @Date 2025/1/7 13:11
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AddressTextParseRpcParam {

    /**
     * 用户输入文本（必填）
     */
    private String text;

    /**
     * 请求坐标的经度（非必填）
     */
    private Double lat;

    /**
     * 请求坐标的纬度（非必填）
     */
    private Double lng;

    /**
     * 0:京标   1:国标  （非必填）
     */
    private int addressType;
}