package com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName TextParseAddressDetailBo
 * @Description
 * <AUTHOR>
 * @Date 2025/1/7 14:55
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TextParseAddressDetailBo {
    /**
     * 省编码
     */
    private String provCode;

    /**
     * 省名称
     */
    private String provName;

    /**
     * 市编码
     */
    private String cityCode;

    /**
     * 市名称
     */
    private String cityName;

    /**
     * 区编码
     */
    private String districtCode;

    /**
     * 区名称
     */
    private String districtName;

    /**
     * 末级行政区编码
     */
    private String townCode;

    /**
     * 末级行政区名称
     */
    private String townName;

    /**
     *
     */
    private Long adminCode;

    /**
     * 全地址
     */
    private String fullAddress;

    /**
     * 解析的详细地址
     */
    private String detailAddress;
}