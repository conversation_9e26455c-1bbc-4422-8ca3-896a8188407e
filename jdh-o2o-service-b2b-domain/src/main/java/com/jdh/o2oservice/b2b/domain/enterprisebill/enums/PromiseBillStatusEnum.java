package com.jdh.o2oservice.b2b.domain.enterprisebill.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * @Author: 账单状态
 * @Date: 2025/2/25 15:38 上午
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum PromiseBillStatusEnum {

    // 账单状态 1-正常 2-已取消(无责) 3-已取消(有责) 4-作废
    NORMAL(1, "正常"),
    CANCELLED_NO_RESPONSIBILITY(2, "已取消(无责)"),
    CANCELLED_RESPONSIBILITY(3, "已取消(有责)"),
    CANCEL(4, "作废"),
    ;

    /**
     *
     */
    private Integer status;

    /**
     *
     */
    private String desc;

    /**
     * @param status
     * @return
     */
    public static String getPromiseBillStatusDescByStatus(Integer status) {
        if (Objects.isNull(status)) {
            return "";
        }
        for (PromiseBillStatusEnum promiseBillStatusEnum : PromiseBillStatusEnum.values()) {
            if (Objects.equals(promiseBillStatusEnum.getStatus(), status)) {
                return promiseBillStatusEnum.getDesc();
            }
        }
        return "";
    }
}
