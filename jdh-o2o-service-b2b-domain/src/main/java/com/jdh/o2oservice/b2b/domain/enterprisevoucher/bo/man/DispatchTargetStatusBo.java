package com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.man;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description O2oDispatchTargetStatusBo
 * @Date 2025/3/3 下午3:16
 * <AUTHOR>
 **/
@Data
public class DispatchTargetStatusBo implements Serializable {


    /**
     * 派单任务ID
     */
    private Long dispatchId;

    /**
     * 错误编码
     */
    private String errorCode;

    /**
     * 展示给客户的错误提示，因为有的场景把系统异常传递给客户展示，体验不好
     */
    private String description;

    /**
     * 指定派单操作结果
     */
    private Boolean targetResult;

}
