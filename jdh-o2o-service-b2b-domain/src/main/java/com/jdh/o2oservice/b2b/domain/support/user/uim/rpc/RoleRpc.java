package com.jdh.o2oservice.b2b.domain.support.user.uim.rpc;

import com.jdh.o2oservice.b2b.domain.support.user.uim.bo.Uim2DimResourceBO;
import com.jdh.o2oservice.b2b.domain.support.user.uim.bo.Uim2RoleBO;

import java.util.List;

/**
 * uim系统角色接口
 *
 * <AUTHOR>
 * @date 2023-09-21 21:24
 */
public interface RoleRpc {

    /**
     * 查询用户拥有的全部角色
     *
     * @param erp erp
     * @return list
     */
    List<Uim2RoleBO> getRole(String erp);

    /**
     * getDimResource
     *
     * @param erp ERP
     * @return {@link List}<{@link Uim2DimResourceBO}>
     */
    List<Uim2DimResourceBO> getDimResource(String erp);
}