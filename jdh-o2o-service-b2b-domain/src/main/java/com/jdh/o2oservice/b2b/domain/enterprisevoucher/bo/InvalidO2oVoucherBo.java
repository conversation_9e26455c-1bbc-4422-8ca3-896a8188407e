package com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * InvalidO2oVoucherBo
 *
 * <AUTHOR>
 * @date 2025/02/27
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InvalidO2oVoucherBo {

    /**
     * 1：以人维度作废；
     * 2：以服务维度作废；
     * 3：整单（履约单）作废
     */
    private Integer invalidType;

    /**
     * voucherId
     */
    private Long voucherId;
    /**
     *
     */
    private String reason;

}
