package com.jdh.o2oservice.b2b.domain.enterprisebill.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.b2b.domain.enterprisebill.context.B2bEnterpriseBillQueryContext;
import com.jdh.o2oservice.b2b.domain.enterprisebill.model.JdhB2bEnterpriseBillDetail;

import java.util.List;

/**
 * @ClassName:JdhB2bEnterpriseBillDetailRepository
 * @Description:B2b企业账单明细
 * @Author: liwenming
 * @Date: 2025/2/24 15:14
 * @Vserion: 1.0
 **/
public interface JdhB2bEnterpriseBillDetailRepository {

    /**
     * 查询企业账单列表
     * @param queryContext
     * @return
     */
    Page<JdhB2bEnterpriseBillDetail> queryEnterpriseBillDetailPage(B2bEnterpriseBillQueryContext queryContext);

    /**
     *
     * @param detailList
     * @return
     */
    Boolean batchSaveEnterpriseBillDetail(List<JdhB2bEnterpriseBillDetail> detailList);
}
