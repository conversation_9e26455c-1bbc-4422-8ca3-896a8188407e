package com.jdh.o2oservice.b2b.domain.enterprisesku.model;
import com.jdh.o2oservice.b2b.base.model.Identifier;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

/**
 * 企业skuId
 *
 * <AUTHOR>
 * @date 2024/04/17
 */
@Data
@Builder
@AllArgsConstructor
public class JdhEnterpriseSkuIdentifier implements Identifier {

    /**
     * 企业服务项目skuId
     */
    private Long enterpriseSkuId;

    @Override
    public String serialize() {
        return String.valueOf(enterpriseSkuId);
    }
}
