package com.jdh.o2oservice.b2b.domain.enterpriseaccount.repository;
import com.jdh.o2oservice.b2b.domain.enterpriseaccount.model.JdhB2bEnterpriseAccountDetail;
import com.jdh.o2oservice.b2b.domain.enterpriseaccount.model.JdhEnterpriseAccountDetailIdentifier;
import com.jdh.o2oservice.b2b.domain.enterpriseaccount.repository.query.JdhB2bEnterpriseAccountDetailQuery;

import java.util.List;

/**
 * @Description 企业信息
 * @Date 2025/2/24 下午6:27
 * <AUTHOR>
 **/
public interface JdhB2bEnterpriseAccountDetailRepository {


    /**
     * 通过Identify 查询
     * @param identifier
     * @return
     */
    JdhB2bEnterpriseAccountDetail find(JdhEnterpriseAccountDetailIdentifier identifier);
    /**
     * 查询
     * @param queryContext
     * @return
     */
    JdhB2bEnterpriseAccountDetail queryJdhB2bEnterpriseAccountDetail(JdhB2bEnterpriseAccountDetailQuery queryContext);

    /**
     * 查询DetailList
     * @param queryContext
     * @return
     */
    List<JdhB2bEnterpriseAccountDetail> queryEnterpriseAccountDetailList(JdhB2bEnterpriseAccountDetailQuery queryContext);

    /**
     * 保存
     *
     * @param jdhB2bEnterpriseAccountDetail
     * @return count
     */
    int save(JdhB2bEnterpriseAccountDetail jdhB2bEnterpriseAccountDetail);
}
