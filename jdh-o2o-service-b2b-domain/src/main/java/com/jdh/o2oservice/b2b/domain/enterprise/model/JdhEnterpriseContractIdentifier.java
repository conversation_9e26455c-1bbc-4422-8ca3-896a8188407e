package com.jdh.o2oservice.b2b.domain.enterprise.model;
import com.jdh.o2oservice.b2b.base.model.Identifier;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

/**
 * 企业合同id
 *
 * <AUTHOR>
 * @date 2024/04/17
 */
@Data
@Builder
@AllArgsConstructor
public class JdhEnterpriseContractIdentifier implements Identifier {

    /**
     * 合同id
     */
    private Long contractId;

    @Override
    public String serialize() {
        return String.valueOf(contractId);
    }
}
