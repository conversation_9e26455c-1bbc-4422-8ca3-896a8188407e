package com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.man;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description
 * @Date 2025/2/28 下午1:42
 * <AUTHOR>
 **/
@Data
public class PromiseServiceDetailBo implements Serializable {

    /**   */
    public static final Integer OTHER_STATUS = 0;
    /** 服务完成状态 */
    public static final Integer COMPLETE_STATUS = 6;
    /** 作废状态 */
    public static final Integer INVALID_STATUS = 8;

    /**
     * 履约单ID
     */
    private Long promiseId;

    /**
     * 服务ID
     */
    private Long serviceId;

    /**
     * 服务名称
     */
    private String serviceName;

    /**
     * 服务项id
     */
    private Long serviceItemId;

    /**
     * 服务项名称
     */
    private String serviceItemName;

    /**
     * 服务标签待定，扩展
     */
    private Integer tags;

    /**
     * 外部服务名称
     */
    private String outServiceId;

    /**
     * 外部服务编号
     */
    private String outServiceName;

    /**
     * 聚合的服务结算状态，当前只有作废、完成、其它三种状态；其它：0；作废：8；完成：6
     * 因为SKU可能存在多个检查项目，检查项目都会有自己的状态，最终SKU的状态应该由项目聚合出来。对于结算来说，关注的是作废、完成。
     */
    private Integer settleStatus;
    /**
     * 0-正常 1-冻结
     */
    private Integer freeze;
}
