package com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.man;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Description TargetDispatchBo
 * @Date 2025/3/3 下午1:42
 * <AUTHOR>
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TargetDispatchBo implements Serializable {

    /**
     * 履约单ID
     */
    private Long promiseId;

    /**
     * 派单任务ID
     */
    private Long dispatchId;

    /**
     * 操作人（一般为客服、运营）
     */
    private String operator;

    /**
     * 指定服务者
     */
    private Long targetAngelId;

    /**
     * 1：AI 2：系统 3：医生Pin 4：运营 5：医生ID
     */
    private Integer roleType;

    /**
     * 原因
     */
    private String reason;
}
