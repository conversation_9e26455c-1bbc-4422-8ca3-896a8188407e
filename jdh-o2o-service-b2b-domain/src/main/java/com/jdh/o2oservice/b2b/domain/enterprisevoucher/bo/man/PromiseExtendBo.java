package com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.man;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description 扩展信息
 * @Date 2025/2/28 下午2:35
 * <AUTHOR>
 **/
@Data
public class PromiseExtendBo implements Serializable {

    /**
     * 下单电话
     */
    public static final String ORDER_PHONE = "orderPhone";

    /**
     * 下单备注
     */
    public static final String ORDER_REMARK = "orderRemark";


    /**
     * 履约单id
     */
    private Long promiseId;

    /**
     * 属性名
     */
    private String attribute;


    /**
     * 属性值
     */
    private String value;


    /**
     * 下单电话
     *
     * @param phone 电话
     */
    public void setOrderPhone(String phone){
        this.setAttribute(ORDER_PHONE);
        this.setValue(phone);
    }

    /**
     * 获取下单电话
     *
     * @return {@link String}
     */
    public String getOrderPhone(){
        return ORDER_PHONE.equals(this.getAttribute()) ? this.getValue() : "";
    }

    /**
     * 下单备注
     *
     * @param remark remark
     */
    public void setOrderRemark(String remark){
        this.setAttribute(ORDER_REMARK);
        this.setValue(remark);
    }

    /**
     * 下单备注
     *
     * @return {@link String}
     */
    public String getOrderRemark(){
        return ORDER_REMARK.equals(this.getAttribute()) ? this.getValue() : "";
    }
}
