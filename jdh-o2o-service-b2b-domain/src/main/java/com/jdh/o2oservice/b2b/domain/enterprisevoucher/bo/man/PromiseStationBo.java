package com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.man;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description 预约地点
 * @Date 2025/2/28 下午2:34
 * <AUTHOR>
 **/
@Data
public class PromiseStationBo implements Serializable {

    /**
     * 供应商编码
     */
    private Long channelNo;
    /**
     * 京东门店的ID
     */
    private String storeId;
    /**
     * 商家门店名称
     */
    private String storeName;
    /**
     * 商家门店地址
     */
    private String storeAddr;

    /** 商家门店电话 */
    private String storePhone;

    /**
     * 级联地址 一级
     */
    private Integer provinceCode;

    /**
     * 级联地址 一级
     */
    private String provinceName;

    /**
     * 级联地址 二级
     */
    private Integer cityCode;

    /**
     * 级联地址 二级
     */
    private String cityName;

    /**
     * 级联地址 三级
     */
    private Integer districtCode;

    /**
     * 级联地址 三级
     */
    private String districtName;

    /**
     * 级联地址 四级
     */
    private Integer townCode;

    /**
     * 级联地址 四级
     */
    private String townName;
}
