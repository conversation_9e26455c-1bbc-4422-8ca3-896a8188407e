package com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.man;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description 服务者详情
 * @Date 2025/3/3 下午1:39
 * <AUTHOR>
 **/
@Data
public class JdhAngelDetailBo implements Serializable {

    private Long id;

    /**
     * 服务者id
     */
    private Long angelId;

    /**
     * 互医医生id
     */
    private Long nethpDocId;

    /**
     * 服务者pin
     */
    private String angelPin;

    /**
     * 服务者姓名
     */
    private String angelName;

    /**
     * 服务者头图
     */
    private String headImg;

    /**
     * 性别 1-男 2-女
     */
    private Integer gender;

    /**
     * 证件号
     */
    private String idCard;

    /**
     * 证件类型 1-身份证 2-护照
     */
    private Integer idCardType;

    /**
     * 身份证国徽图url
     */
    private String idCardImgFrontUrl;

    /**
     * 身份证人像图url
     */
    private String idCardImgOppositeUrl;

    /**
     * 电话
     */
    private String phone;

    /**
     * 紧急联系人姓名
     */
    private String emergencyContactName;

    /**
     * 紧急联系人电话
     */
    private String emergencyContactPhone;

    /**
     * 人员标签 0兼职 1全职
     */
    private Integer jobNature;

    /**
     * 个人简介
     */
    private String introduction;

    /**
     * 接单状态 0关闭 1开启
     */
    private Integer takeOrderStatus;

    /**
     * 服务站点id
     */
    private Long stationId;

    /**
     * 站长erp
     */
    private String stationMaster;

    /**
     * 审核状态
     */
    private Integer auditProcessStatus;

    /**
     * 审核时间
     */
    private String auditProcessDate;

    /**
     * 审核备注
     */
    private String auditProcessRemark;

    /**
     * 是否有效 0-无效 1-有效
     */
    private Integer yn;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 数据来源分支
     */
    private String branch;

    /**
     * 创建日期
     */
    private Date createTime;

    /**
     * 修改日期
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String updateUser;

    /**
     * 全地址
     */
    private String fullAddress;

    /**
     * 全科室
     */
    private String fullDepartmentName;

    /**
     * 省地区code
     */
    private String provinceName;

    /**
     * 市地区code
     */
    private String cityName;

    /**
     * 县地区code
     */
    private String countyName;

    /**
     * 一级科室code
     */
    private String oneDepartmentName;

    /**
     * 二级科室code
     */
    private String twoDepartmentName;
}
