package com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
@Data
public class O2oAngelWorkBo implements Serializable {

    /**
     * 自增主键
     */
    private Long id;

    /**
     * 垂直业务身份编码
     */
    private String verticalCode;

    /**
     * 服务类型
     */
    private String serviceType;

    /**
     * 服务工单Id
     */
    private Long workId;

    /**
     * 京东订单Id
     */
    private Long jdOrderId;

    /**
     * 履约单Id
     */
    private Long promiseId;

    /**
     * 服务工单类型：1=骑手,2=护士,3=护工,4=康复师
     */
    private Integer	workType;

    /**
     * 服务工单类型-页面展示用
     */
    private String workTypeDesc;

    /**
     * 服务者Id
     */
    private String angelId;

    /**
     * 服务者Cpin
     */
    private String angelPin;

    /**
     * 服务者姓名
     */
    private String angelName;

    /**
     * 服务者联系方式
     */
    private String angelPhone;

    /**
     * 预计服务费用
     */
    private BigDecimal angelCharge;

    /**
     * 服务者工单状态：
     */
    private Integer	status;

    /**
     * 服务者工单状态：
     */
    private String statusDesc;

    /**
     * 任务暂停状态：0正常，1退款暂停, 2取消暂停
     */
    private Integer stopStatus;

    /**
     * 失效的，如果退款了，则disabled为true
     */
    private Boolean disabled;

    /**
     * 服务站Id
     */
    private String angelStationId;

    /**
     * 着装照片地址
     */
    private List<String> clothingPicUrls;

    /**
     * 医疗废物照片地址
     */
    private List<String> medicalWastePicUrls;

    /**
     * 服务记录地址
     */
    private List<String> serviceRecordPicUrls;

    /**
     * 扩展信息：订单信息{订单Id,订单remark,预约人姓名、联系方式}，医生着装图片，医疗废物处理图片
     */
    private String extend;

    /**
     * 保单Id
     */
    private String insureId;

    /**
     * 投保状态：1=审核中，2=成功，3=失败，4=失效
     */
    private Integer	insureStatus;

    /**
     * 所需全部耗材：（试管*2；血常规检测包*2）
     */
    private String materialNames;

    /**
     * 是否需要上传服务记录，true需要弹出上传服务记录的弹层
     */
    private boolean needServiceRecord;

    /**
     * 是否需要上传服务记录，true要展示服务记录上传楼层
     */
    private boolean needServiceRecordFloor;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 剩余接单时长，单位秒
     */
    private Integer remainingDuration;

    /**
     * 预计服务费用
     */
    private String angelChargeDesc;

    /**
     * 服务开始时间
     */
    private Date workStartTime;

    /**
     * 服务结束时间
     */
    private Date workEndTime;

    /**
     * 是否需要展示实验室指引楼层
     */
    private boolean needAcceptGuideFloor;
}
