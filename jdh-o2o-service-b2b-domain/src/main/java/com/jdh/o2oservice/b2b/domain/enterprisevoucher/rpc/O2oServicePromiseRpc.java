package com.jdh.o2oservice.b2b.domain.enterprisevoucher.rpc;

import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.*;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.ctx.SubmitPromiseCtx;

import java.util.List;

public interface O2oServicePromiseRpc {


    /**
     * 创建voucher promise  同步
     *
     * @param ctx ctx
     * @return {@link O2oPromiseBo }
     */
    List<O2oPromiseBo> syncCreateVoucher(SubmitPromiseCtx ctx);

    /**
     * 预约
     *
     * @return {@link Boolean }
     */
    Boolean submitAppointment(SubmitPromiseCtx ctx);

    /**
     * 查询promise信息
     *
     * @param promiseId promiseId
     * @return {@link O2oPromiseBo }
     */
    O2oPromiseBo getPromiseInfo(Long promiseId);


    /**
     * invalidO2oVoucherBo
     *
     * @param invalidO2oVoucherBo invalidO2oVoucherBo
     * @return {@link Boolean }
     */
    Boolean invalidVoucher(InvalidO2oVoucherBo invalidO2oVoucherBo);

    /**
     * 查询履约单
     * @param request
     * @return
     */
    List<O2oPromiseBo> findByPromiseList(O2oPromiseListRequestBo request);


    List<CompletePromiseBo> queryCompletePromiseList(CompletePromiseRequestBo request);

}
