package com.jdh.o2oservice.b2b.domain.enterpriseuser.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.b2b.domain.enterpriseuser.model.JdhB2bEnterpriseUser;
import com.jdh.o2oservice.b2b.domain.enterpriseuser.model.JdhEnterpriseUserIdentifier;
import com.jdh.o2oservice.b2b.domain.enterpriseuser.repository.query.JdhB2bEnterpriseUserQuery;

import java.util.List;

/**
 * 企业用户
 */
public interface JdhB2bEnterpriseUserRepository {

    /**
     * 保存
     *
     * @param entity
     * @return count
     */
    int save(JdhB2bEnterpriseUser entity);

    /**
     * 更新
     *
     * @param entity
     * @return count
     */
    int update(JdhB2bEnterpriseUser entity);

    /**
     * 更新企业用户状态
     *
     * @param entity
     * @return count
     */
    int updateEnterpriseUserStatus(JdhB2bEnterpriseUser entity);

    /**
     * 删除
     *
     * @param entity
     * @return count
     */
    int delete(JdhB2bEnterpriseUser entity);

    /**
     * 通过Identify 查询
     * @param identifier
     * @return
     */
    JdhB2bEnterpriseUser find(JdhEnterpriseUserIdentifier identifier);

    /**
     * 通过userPin 查询
     * @param userPin
     * @return
     */
    JdhB2bEnterpriseUser findEnterpriseUserByPin(String userPin);
    /**
     * 分页查询企业用户列表
     * @param query
     * @return
     */
    Page<JdhB2bEnterpriseUser> queryPageEnterpriseUser(JdhB2bEnterpriseUserQuery query);

    /**
     * 企业用户列表
     * @param query
     * @return
     */
    List<JdhB2bEnterpriseUser> findList(JdhB2bEnterpriseUserQuery query);
}
