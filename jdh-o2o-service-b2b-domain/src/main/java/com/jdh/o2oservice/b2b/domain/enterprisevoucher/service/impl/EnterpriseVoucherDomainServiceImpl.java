package com.jdh.o2oservice.b2b.domain.enterprisevoucher.service.impl;

import cn.hutool.core.net.Ipv4Util;
import com.jdh.o2oservice.b2b.base.constant.NumConstant;
import com.jdh.o2oservice.b2b.base.enums.ThreadPoolConfigEnum;
import com.jdh.o2oservice.b2b.base.exception.BusinessErrorCode;
import com.jdh.o2oservice.b2b.base.exception.BusinessException;
import com.jdh.o2oservice.b2b.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.b2b.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.b2b.base.util.IpUtil;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.InvalidO2oVoucherBo;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.O2oPromiseBo;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.ctx.SubmitPromiseCtx;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.enums.PromiseStatusEnum;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.factory.EnterpriseVoucherFactory;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.model.EnterpriseVoucher;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.repository.EnterpriseVoucherRepository;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.rpc.O2oServicePromiseRpc;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.service.EnterpriseVoucherDomainService;
import com.jdh.o2oservice.b2b.domain.support.common.bo.VirtualPinBo;
import com.jdh.o2oservice.b2b.domain.support.common.repository.VirtualPinRpc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * EnterpriseVoucherDomainServiceImpl
 *
 * <AUTHOR>
 * @date 2025/02/27
 */
@Slf4j
@Service
public class EnterpriseVoucherDomainServiceImpl implements EnterpriseVoucherDomainService {

    /**
     * o2oServicePromiseRpc
     */
    @Autowired
    private O2oServicePromiseRpc o2oServicePromiseRpc;

    /**
     * enterpriseVoucherRepository
     */
    @Autowired
    private EnterpriseVoucherRepository enterpriseVoucherRepository;

    /**
     * generateIdFactory
     */
    @Autowired
    private GenerateIdFactory generateIdFactory;

    /**
     * virtualPinRpc
     */
    @Autowired
    private VirtualPinRpc virtualPinRpc;



    /**
     * submitPromise
     *
     * @param ctx ctx
     * @return {@link EnterpriseVoucher }
     */
    @Override
    public EnterpriseVoucher createAndSubmitPromise(SubmitPromiseCtx ctx) {
        //1、补充提交人信息，创建虚拟pin
        String virtualPin = virtualPinRpc.getVirtualPin(VirtualPinBo.builder()
                .orgId(ctx.getEnterpriseId().toString())
                .userIp(IpUtil.getInetAddressNoException(false))
                .userId(ctx.getEnterpriseId() + "_" + ctx.getAppointmentPhone())
                .build());
        ctx.setCustomerUserPin(virtualPin);

        //2、调用o2o同步兑换服务
        Long enterpriseVoucherId = generateIdFactory.getId();
        ctx.setEnterpriseVoucherId(enterpriseVoucherId);
        ctx.setVerticalCode("external_b");
        log.info("EnterpriseVoucherDomainServiceImpl -> createAndSubmitPromise 调用o2o同步兑换服务，ctx={}", ctx);
        O2oPromiseBo createdO2oPromiseBo = o2oServicePromiseRpc.syncCreateVoucher(ctx).get(NumConstant.NUM_0);
        log.info("EnterpriseVoucherDomainServiceImpl -> createAndSubmitPromise 调用o2o同步兑换服务，createdO2oPromiseBo={}", createdO2oPromiseBo);

        ctx.setPromiseId(createdO2oPromiseBo.getPromiseId());
        ctx.setVerticalCode(createdO2oPromiseBo.getVerticalCode());
        ctx.setServiceType(createdO2oPromiseBo.getServiceType());
        //3、提交预约
        try {
            Boolean submitted = o2oServicePromiseRpc.submitAppointment(ctx);
            //3.1 如果提交失败，作废之前兑换的voucher
            if(Boolean.FALSE.equals(submitted)) {
                //失败，异步作废
                ExecutorPoolFactory.get(ThreadPoolConfigEnum.ENTERPRISE_VOUCHER).submit(() -> invalidVoucher(InvalidO2oVoucherBo.builder()
                        .voucherId(createdO2oPromiseBo.getVoucherId())
                        .reason("b2b场景 createAndSubmitPromise 失败")
                        .invalidType(NumConstant.NUM_3)
                        .build()));
                throw new BusinessException(BusinessErrorCode.SUBMIT_VOUCHER_ERROR);
            }

            //3.2 如果提交成功，反查一下promise状态
            //O2oPromiseBo queryPromise = o2oServicePromiseRpc.getPromiseInfo(createdO2oPromiseBo.getPromiseId());

            ctx.setVoucherId(createdO2oPromiseBo.getVoucherId());
            ctx.setEnterpriseVoucherId(enterpriseVoucherId);
            ctx.setPromiseId(createdO2oPromiseBo.getPromiseId());
            //ctx.setPromiseStatus(queryPromise.getPromiseStatus());
            ctx.setPromiseStatus(PromiseStatusEnum.INIT.getStatus());

            //4、构建model
            return EnterpriseVoucherFactory.createEnterpriseVoucher(ctx);
        } catch (Exception e) {
            log.error("EnterpriseVoucherDomainServiceImpl -> createAndSubmitPromise error.", e);
            //3.1 如果提交失败，异步作废之前兑换的voucher
            ExecutorPoolFactory.get(ThreadPoolConfigEnum.ENTERPRISE_VOUCHER).submit(() -> invalidVoucher(InvalidO2oVoucherBo.builder()
                    .voucherId(createdO2oPromiseBo.getVoucherId())
                    .reason("EnterpriseVoucherDomainServiceImpl -> createAndSubmitPromise 失败")
                    .invalidType(NumConstant.NUM_3)
                    .build()));
            throw e;
        }
    }

    /**
     * invalidVoucher
     *
     * @param invalidO2oVoucherBo invalidO2oVoucherBo
     */
    private void invalidVoucher(InvalidO2oVoucherBo invalidO2oVoucherBo) {
        o2oServicePromiseRpc.invalidVoucher(invalidO2oVoucherBo);
    }

}
