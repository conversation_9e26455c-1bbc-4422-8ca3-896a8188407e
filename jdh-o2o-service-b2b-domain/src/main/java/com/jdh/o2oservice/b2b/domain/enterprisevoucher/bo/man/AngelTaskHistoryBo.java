package com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.man;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description 服务任务明细历史记录
 * @Date 2025/2/28 下午3:07
 * <AUTHOR>
 **/
@Data
public class AngelTaskHistoryBo implements Serializable {

    /**
     * 任务单Id
     */
    private Long taskId;

    /**
     * 工单操作事件
     */
    private String operateEvent;

    /**
     * 变更前任务单状态：1=待服务，2=服务中，3=服务完成，4=服务取消
     */
    private Integer beforeStatus;

    /**
     * 变更后任务单状态：1=待服务，2=服务中，3=服务完成，5=服务取消
     */
    private Integer afterStatus;

    /**
     * 变更前业务任务状态：0初始状态 21验证通过 22采样 23绑码 24送检中 25服务记录上传
     */
    private Integer beforeBizExtStatus;

    /**
     * 变更后业务任务状态：0初始状态 21验证通过 22采样 23绑码 24送检中 25服务记录上传
     */
    private Integer afterBizExtStatus;

    /**
     * 扩展信息：reason=取消原因
     */
    private String extend;

    /**
     * 操作时间
     */
    private Date operateTime;

    /**
     * 创建时间
     */
    private Date createTime;
}
