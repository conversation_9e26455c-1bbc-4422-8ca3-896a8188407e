package com.jdh.o2oservice.b2b.domain.support.address;


import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.TextParseAddressBO;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.param.AddressTextParseRpcParam;
import com.jdh.o2oservice.b2b.domain.support.address.bo.JDDistrictBo;

import java.util.List;

/**
 * 地址转换
 *
 * <AUTHOR>
 * @date 2019-12-09 15:21
 */
public interface AddressRpc {

    /**
     * 获取京标的省
     *
     * @return
     */
    List<JDDistrictBo> getProvinces();

    /**
     * 获取京标子级
     *
     * @param jdParentCode
     * @return
     */
    List<JDDistrictBo> getChildren(Integer jdParentCode);

    /**
     * 智能文本解析
     * https://lbsapi.jd.com/iframe.html?childURL=docid=2-75&childNav=1-17
     * @param param
     * @return
     */
    TextParseAddressBO parseText(AddressTextParseRpcParam param);

}
