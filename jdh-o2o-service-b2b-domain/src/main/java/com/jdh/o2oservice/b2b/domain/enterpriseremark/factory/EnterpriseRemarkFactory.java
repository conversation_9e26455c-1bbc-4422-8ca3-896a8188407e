package com.jdh.o2oservice.b2b.domain.enterpriseremark.factory;

import com.jdh.o2oservice.b2b.domain.enterpriseremark.cmd.EnterpriseRemarkCmd;
import com.jdh.o2oservice.b2b.domain.enterpriseremark.model.EnterpriseRemark;
import com.jdh.o2oservice.b2b.base.enums.YnStatusEnum;

import java.util.Date;

/**
 * 企业备注工厂类
 *
 * <AUTHOR>
 * @date 2025/03/10
 */
public class EnterpriseRemarkFactory {

    /**
     * 创建企业备注
     *
     * @param cmd 命令对象
     * @return 企业备注领域模型
     */
    public static EnterpriseRemark createEnterpriseRemark(EnterpriseRemarkCmd cmd) {
        Date now = new Date();
        return EnterpriseRemark.builder()
                .enterpriseId(cmd.getEnterpriseId())
                .enterpriseRemarkId(cmd.getEnterpriseRemarkId())
                .content(cmd.getContent())
                .yn(YnStatusEnum.YES.getCode())
                .createUser(cmd.getOperator())
                .createTime(now)
                .updateUser(cmd.getOperator())
                .updateTime(now)
                .build();
    }
}
