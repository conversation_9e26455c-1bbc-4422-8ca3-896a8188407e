package com.jdh.o2oservice.b2b.job;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import org.apache.shardingsphere.elasticjob.simple.job.SimpleJob;
import org.springframework.stereotype.Component;

/**
 * @Date 2025/3/6 下午6:15
 * <AUTHOR>
 **/
@Slf4j
@Component
public class DemoJob implements SimpleJob {

    @Override
    public void execute(ShardingContext shardingContext) {

    }
}
