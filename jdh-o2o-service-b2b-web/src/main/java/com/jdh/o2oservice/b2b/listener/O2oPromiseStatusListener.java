package com.jdh.o2oservice.b2b.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.client.springboot.annotation.JmqListener;
import com.jd.jmq.common.message.Message;
import com.jdh.o2oservice.b2b.application.enterprisevoucher.B2bEnterpriseVoucherApplication;
import com.jdh.o2oservice.b2b.base.util.RedisLockUtil;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.O2oPromiseBo;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.rpc.O2oServicePromiseRpc;
import com.jdh.o2oservice.b2b.export.enterprisevoucher.cmd.SyncPromiseStatusCmd;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * O2oPromiseStatusListener
 *
 * <AUTHOR>
 * @date 2025/02/27
 */
@Slf4j
@Component
public class O2oPromiseStatusListener implements MessageListener {

    /**
     * b2bEnterpriseVoucherApplication
     */
    @Autowired
    private B2bEnterpriseVoucherApplication b2bEnterpriseVoucherApplication;

    /**
     * o2oServicePromiseRpc
     */
    @Autowired
    private O2oServicePromiseRpc o2oServicePromiseRpc;

    /**
     * redisLockUtil
     */
    @Resource
    private RedisLockUtil redisLockUtil;

    /**
     * 同步promise状态 redis 锁
     */
    private static final String SYNC_PROMISE_STATUS_REDIS_LOCK = "o2o-service-b2b:O2oPromiseStatusListener:{0}";

    /**
     * verticalCodeList
     */
    private static List<String> verticalCodeList = Arrays.asList("");

    /**
     * 消息监听
     *
     * @param messages
     */
    @Override
    @JmqListener(id= "xx", topics = {"${topics.xx}"})
    public void onMessage(List<Message> messages) {
        if (messages == null || messages.isEmpty()) {
            return;
        }
        for (Message message : messages) {
            try {
                String text = message.getText();
                JSONObject msgObj = JSON.parseObject(text);
                Long promiseId = msgObj.getLong("promiseId");

                String redisKey = MessageFormat.format(SYNC_PROMISE_STATUS_REDIS_LOCK, promiseId.toString());
                String exceptValue = UUID.randomUUID().toString();
                try {
                    String verticalCode = msgObj.getString("verticalCode");
                    if(!verticalCodeList.contains(verticalCode)){
                        continue;
                    }
                    //以promiseId加锁，排队
                    if (redisLockUtil.tryLockWithThrow(redisKey,exceptValue,10, TimeUnit.SECONDS)) {
                        //1、反查promise信息
                        O2oPromiseBo promiseInfo = o2oServicePromiseRpc.getPromiseInfo(promiseId);
                        log.info("O2oPromiseStatusListener promiseInfo:{}", JSON.toJSONString(promiseInfo));

                        //2、同步promise状态
                        SyncPromiseStatusCmd cmd = SyncPromiseStatusCmd.builder()
                                .promiseId(promiseInfo.getPromiseId())
                                .promiseStatus(promiseInfo.getPromiseStatus())
                                .verticalCode(verticalCode)
                                .sourceVoucherId(msgObj.getString("sourceVoucherId"))
                                .build();
                        b2bEnterpriseVoucherApplication.savePromiseStatus(cmd);
                    }
                }finally {
                    redisLockUtil.unLock(redisKey, exceptValue);
                }
            }catch (Exception e) {
                log.error("O2oPromiseStatusListener error", e);
                throw e;
            }
        }
    }
}
