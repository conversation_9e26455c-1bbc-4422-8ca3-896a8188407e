package com.jdh.o2oservice.b2b.aspect;

import cn.hutool.core.bean.BeanPath;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jd.common.web.LoginContext;
import com.jdh.o2oservice.b2b.application.support.B2bOperationLogApplication;
import com.jdh.o2oservice.b2b.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.b2b.base.annotation.OperationLog;
import com.jdh.o2oservice.b2b.base.ducc.DuccConfig;
import com.jdh.o2oservice.b2b.base.enums.OpTypeEnum;
import com.jdh.o2oservice.b2b.base.exception.BusinessErrorCode;
import com.jdh.o2oservice.b2b.base.exception.BusinessException;
import com.jdh.o2oservice.b2b.base.exception.SystemErrorCode;
import com.jdh.o2oservice.b2b.base.operationlog.cmd.OperationLogClientCmd;
import com.jdh.o2oservice.b2b.base.operationlog.context.OperationLogContext;
import com.jdh.o2oservice.b2b.base.util.IpUtil;
import com.jdh.o2oservice.b2b.base.util.ResponseUtil;
import com.jdh.o2oservice.b2b.base.util.SpringUtil;
import com.jdh.o2oservice.b2b.common.response.Response;
import com.jdh.o2oservice.b2b.export.operationLog.cmd.OperationLogCmd;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Stream;

/**
 * 操作日志切面
 */
@Aspect
@Component
@Slf4j
public class OperationLogAspect {

    @Pointcut(value = "@annotation(com.jdh.o2oservice.b2b.base.annotation.OperationLog)")
    private void pointCut() {}

    /**
     * 在之后执行
     */
    @Around("pointCut()")
    public Object handle(ProceedingJoinPoint joinPoint) throws Throwable {
        String requestParam = null;
        // 日志配置
        OperationLog operationLog = null;
        String classMethod = null;
        // 操作端对象
        OperationLogClientCmd operationLogClientCmd = null;
        Object[] args = null;
        Object result = null;
        Response<?> errorResponse = null;
        try {
            String className = joinPoint.getSignature().getDeclaringTypeName();
            String methodName = joinPoint.getSignature().getName();
            classMethod = className + "#" + methodName;

            // 获取注解
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();

            // 如果方法上有logAndAlarm注解,此处不再输出出入参信息,减少日志输出
            LogAndAlarm logAndAlarm = method.getAnnotation(LogAndAlarm.class);
            boolean hasLogAndAlarm = logAndAlarm != null;

            operationLog = method.getAnnotation(OperationLog.class);
            //组装参数打印信息
            args = joinPoint.getArgs();
            // http请求
            RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
            if (requestAttributes instanceof ServletRequestAttributes) {
                ServletRequestAttributes attributes = (ServletRequestAttributes) requestAttributes;
                HttpServletRequest request = attributes.getRequest();
                // SSO 接入必定包含其他情况为空
                String pageUrl = request.getHeader("callback");
                boolean skipClass = Optional.ofNullable(args).map(Arrays::stream).orElseGet(Stream::empty).anyMatch(obj -> obj instanceof ServletRequest || obj instanceof ServletResponse || obj instanceof MultipartFile);
                String ip = IpUtil.getIpAddr(request);
                if (!skipClass) {
                    requestParam = JSON.toJSONString(args);
                }
                if (!hasLogAndAlarm) {
                    log.info("==================>>>>请求开始,请求来源页面:{},请求来源IP:{},请求地址:{},请求方式:{},请求参数:{},用户信息:{},请求方法:{},skipClass:{}", pageUrl, ip, request.getRequestURL(), request.getMethod(), requestParam, JSON.toJSONString(LoginContext.getLoginContext()), classMethod, skipClass);
                } else {
                    log.info("==================>>>>请求开始,请求来源页面:{},请求来源IP:{},请求地址:{},请求方式:{},请求参数见UMP输出日志,用户信息:{},请求方法:{},skipClass:{}", pageUrl, ip, request.getRequestURL(), request.getMethod(), JSON.toJSONString(LoginContext.getLoginContext()), classMethod, skipClass);
                }
                operationLogClientCmd = new OperationLogClientCmd();
                operationLogClientCmd.setClientIP(ip);
                operationLogClientCmd.setFromPageUrl(pageUrl);
            } else {
                if (!hasLogAndAlarm) {
                    log.info("==================>>>>请求开始,请求方法:{},请求参数:{}", classMethod, JSON.toJSONString(args));
                } else {
                    log.info("==================>>>>请求开始,请求方法:{},请求参数见UMP输出日志", classMethod);
                }
            }
            result = joinPoint.proceed();
            //执行方法，获取返回值
            if (!hasLogAndAlarm) {
                log.info("==================>>>>请求结束,返回值:{}", JSON.toJSONString(result));
            } else {
                log.info("==================>>>>请求结束,返回值见UMP输出日志");
            }
        } catch (Exception e) {
            try {
                String errorMsg = e.getMessage();
                String error = StringUtils.isBlank(errorMsg) ? "" : errorMsg.substring(0, (Math.min(errorMsg.length(), 200) - 1));
                if (e instanceof BusinessException) {
                    errorResponse = ResponseUtil.buildErrResponse(((BusinessException) e).getErrorCode());
                } else {
                    errorResponse = ResponseUtil.buildErrResponse(SystemErrorCode.UNKNOWN_ERROR, error);
                }
            } catch (Exception ex) {
                log.error("==================>>>>处理异常错误", ex);
                errorResponse = ResponseUtil.buildErrResponse(SystemErrorCode.UNKNOWN_ERROR);
            }
            throw e;
        } finally {
            // 保存日志
            recordLog(operationLog, classMethod, operationLogClientCmd, args, requestParam, result, errorResponse);
            OperationLogContext.remove();
        }
        return result;
    }



    /**
     * 获取日志配置
     *
     * @param args args
     * @return
     */
    private void recordLog(OperationLog operationLog, String classMethod, OperationLogClientCmd operationLogClientCmd, Object[] args, String requestParam, Object result, Response<?> errorResponse) {
        try {
            DuccConfig duccConfig = SpringUtil.getBean(DuccConfig.class);
            List<String> excludeRecordClassMethod = duccConfig.getExcludeRecordClassMethod();
            if (CollUtil.isNotEmpty(excludeRecordClassMethod) && excludeRecordClassMethod.contains(classMethod)) {
                return;
            }

            // 业务主键id
            String bizId = null;
            String argsBizId = getBizIdFormArgs(operationLog, args);
            // 如果配置了获取用户id配置,则从入参中获取用户id
            if (StringUtils.isNotBlank(argsBizId)) {
                bizId = argsBizId;
            }

            // 日志系统不允许操作用户为空,构造空字符
            String userPin = null;
            LoginContext loginContext = LoginContext.getLoginContext();
            // 登录上下文中获取用户id
            if (loginContext != null && StringUtils.isNotBlank(loginContext.getPin())) {
                userPin = loginContext.getPin();
            }
            B2bOperationLogApplication opLogApplication = SpringUtil.getBean(B2bOperationLogApplication.class);
            OperationLogCmd operationLogCmd = new OperationLogCmd();
            // todo 企业id
            //operationLogCmd.setEnterpriseId(null);
            operationLogCmd.setBizSceneKey(classMethod);
            operationLogCmd.setBizSceneDesc(operationLog != null ? operationLog.operationDesc() : null);
            OpTypeEnum opTypeEnum = getOperationFormArgs(operationLog, args);
            if (opTypeEnum == null) {
                opTypeEnum = operationLog != null ? operationLog.operationType() : OpTypeEnum.UNKNOWN;
            }
            operationLogCmd.setOperateType(opTypeEnum != null ? opTypeEnum.getType() : 0);
            operationLogCmd.setBizUnionId(bizId);
            operationLogCmd.setClientInfo(JSON.toJSONString(operationLogClientCmd));
            operationLogCmd.setOperator(userPin);
            handleResultType(operationLog, operationLogCmd, result, errorResponse);
            operationLogCmd.setParam(requestParam);
            handleResult(operationLog, operationLogCmd, result, errorResponse);
            OperationLogContext operationLogContext = OperationLogContext.get();
            if (operationLogContext != null && CollUtil.isNotEmpty(operationLogContext.getBizIdList())) {
                List<OperationLogCmd> operationLogCmdList = new ArrayList<>();
                operationLogContext.getBizIdList().forEach(s -> {
                    OperationLogCmd opCmd = new OperationLogCmd();
                    BeanUtils.copyProperties(operationLogCmd, opCmd);
                    opCmd.setBizUnionId(s);
                    operationLogCmdList.add(opCmd);
                });
                opLogApplication.batchInsertAsyncToLocalDB(operationLogCmdList);
            } else {
                opLogApplication.batchInsertAsyncToLocalDB(Lists.newArrayList(operationLogCmd));
            }
        } catch (Exception e) {
            log.error("==================>>>>保存日志,异常信息", e);
        }
    }

    /**
     * 从入参中获取业务id
     *
     * @param args args
     * @return
     */
    private String getBizIdFormArgs(OperationLog operationLog, Object[] args) {
        if (args == null || operationLog == null || operationLog.recordParamBizIdExpress() == null || operationLog.recordParamBizIdExpress().length < 1) {
            return null;
        }
        try {
            Map<String, Object> argsMap = new HashMap<>();
            argsMap.put("args", args);
            Object result = null;
            for (String express : operationLog.recordParamBizIdExpress()) {
                BeanPath resolver = new BeanPath(express);
                result = resolver.get(argsMap);
                if (result != null) {
                    break;
                }
            }
            if (result != null) {
                return result.toString();
            } else{
                OperationLogContext operationLogContext = OperationLogContext.get();
                if (operationLogContext != null) {
                    return operationLogContext.getBizId();
                }
            }
        } catch (Exception e) {
            log.error("==================>>>>保存日志获取业务ID信息,异常信息", e);
        }
        return null;
    }

    /**
     * 从入参中获取操作类型
     *
     * @param args args
     * @return
     */
    private OpTypeEnum getOperationFormArgs(OperationLog operationLog, Object[] args) {
        if (args == null || operationLog == null || operationLog.paramJudgeOperationTypeExpress() == null || operationLog.paramJudgeOperationTypeExpress().length < 1) {
            return null;
        }
        OpTypeEnum opTypeEnum = null;
        try {
            Map<String, Object> argsMap = new HashMap<>();
            argsMap.put("args", args);
            Object result;
            for (String express : operationLog.paramJudgeOperationTypeExpress()) {
                BeanPath resolver = new BeanPath(express);
                result = resolver.get(argsMap);
                if (result != null) {
                    opTypeEnum = OpTypeEnum.UPDATE;
                    break;
                }
            }
        } catch (Exception e) {
            log.error("==================>>>>保存日志获取操作类型,异常信息", e);
        }
        return opTypeEnum;
    }

    /**
     * 处理结果类型
     *
     * @param result
     */
    private void handleResultType(OperationLog operationLog, OperationLogCmd operationLogCmd, Object result, Response<?> errorResponse) {
        try {
            // 异常处理成失败
            if (errorResponse != null) {
                operationLogCmd.setResultType(2);
                return;
            }

            if (result == null) {
                operationLogCmd.setResultType(1);
                return;
            }
            if (result instanceof Response) {
                if (BusinessErrorCode.SUCCESS.getCode().equalsIgnoreCase(((Response<?>) result).getCode())) {
                    operationLogCmd.setResultType(1);
                } else {
                    operationLogCmd.setResultType(2);
                }
            }
            if (operationLog != null && operationLog.resultJudgeExpress() != null && operationLog.resultJudgeExpress().length > 0) {
                Map<String, Object> argsMap = new HashMap<>();
                argsMap.put("ret", result);
                operationLogCmd.setResultType(2);
                Object resultCode;
                for (String express : operationLog.resultJudgeExpress()) {
                    BeanPath resolver = new BeanPath(express);
                    resultCode = resolver.get(argsMap);
                    if (resultCode != null && Arrays.asList(operationLog.resultSuccessCode()).contains(resultCode.toString())) {
                        operationLogCmd.setResultType(1);
                        break;
                    }
                }
            }
        } catch (Exception e) {
            log.error("==================>>>>处理结果类型,异常信息", e);
        }
    }

    /**
     * 处理结果类型
     *
     * @param result
     */
    private void handleResult(OperationLog operationLog, OperationLogCmd operationLogCmd, Object result, Response<?> errorResponse) {
        try {
            // 优先处理异常
            if (errorResponse != null) {
                operationLogCmd.setResult(JSON.toJSONString(errorResponse));
                return;
            }
            if (result == null) {
                operationLogCmd.setResult(null);
                return;
            }
            if (operationLog != null && operationLog.operationType() != null && OpTypeEnum.QUERY.getType().equals(operationLog.operationType().getType())){
                if (result instanceof Response) {
                    Response<T> ret = new Response<T>();
                    ret.setCode(((Response<?>) result).getCode());
                    ret.setMsg(((Response<?>) result).getMsg());
                    ret.setTraceId(((Response<?>) result).getTraceId());
                    operationLogCmd.setResult(JSON.toJSONString(ret));
                } else {
                    operationLogCmd.setResult(null);
                }
                return;
            }
        } catch (Exception e) {
            log.error("==================>>>>处理结果,异常信息", e);
        }
        operationLogCmd.setResult(JSON.toJSONString(result));
    }

}
