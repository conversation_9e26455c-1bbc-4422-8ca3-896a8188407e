package com.jdh.o2oservice.b2b.interceptor.context;

import lombok.Builder;
import lombok.Data;

/**
 * AuthCodeVo
 *
 * <AUTHOR>
 * @date 2023/09/20
 */
@Data
@Builder
public class BizCodeVo {

    /**
     * bizCode
     */
    private String bizCode;

    /**
     * bizCodeDesc
     */
    private String bizCodeDesc;

    public BizCodeVo() {
    }

    public BizCodeVo(String bizCode, String bizCodeDesc) {
        this.bizCode = bizCode;
        this.bizCodeDesc = bizCodeDesc;
    }
}
