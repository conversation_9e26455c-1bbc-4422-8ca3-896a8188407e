package com.jdh.o2oservice.b2b.facade.support;
import com.jdh.o2oservice.b2b.application.support.B2bOperationLogApplication;
import com.jdh.o2oservice.b2b.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.b2b.base.annotation.PinB2bErp;
import com.jdh.o2oservice.b2b.base.util.ResponseUtil;
import com.jdh.o2oservice.b2b.color.GwMapUtil;
import com.jdh.o2oservice.b2b.common.response.PageDto;
import com.jdh.o2oservice.b2b.common.response.Response;
import com.jdh.o2oservice.b2b.export.operationLog.OperationLogGwExport;
import com.jdh.o2oservice.b2b.export.operationLog.cmd.OperationLogCmd;
import com.jdh.o2oservice.b2b.export.operationLog.dto.B2bOperationLogDto;
import com.jdh.o2oservice.b2b.export.operationLog.query.B2bOperationLogRequest;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.Map;

/**
 * 操作日志
 */
@Service
public class OperationLogGwExportImpl implements OperationLogGwExport {

    @Resource
    private B2bOperationLogApplication b2bOperationLogApplication;

    /**
     * 分页查询操作日志
     * @param param
     * @return
     */
    @Override
    @PinB2bErp
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.facade.support.OperationLogGwExportImpl.queryPageOperationLog")
    public Response<PageDto<B2bOperationLogDto>> queryPageOperationLog(Map<String, String> param) {
        B2bOperationLogRequest request = GwMapUtil.convertToParam(param, B2bOperationLogRequest.class);
        return ResponseUtil.buildSuccResponse(b2bOperationLogApplication.queryPageOperationLog(request));
    }

    /**
     * 保存操作日志
     *
     * @param operationLogCmd
     * @return
     */
    @Override
    public Response<Boolean> saveOperationLog(OperationLogCmd operationLogCmd) {
        return ResponseUtil.buildSuccResponse(b2bOperationLogApplication.insertAsyncToLocalDB(operationLogCmd));
    }
}
