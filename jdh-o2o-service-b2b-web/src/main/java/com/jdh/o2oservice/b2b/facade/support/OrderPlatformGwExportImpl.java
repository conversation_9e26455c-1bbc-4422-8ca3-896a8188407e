package com.jdh.o2oservice.b2b.facade.support;
import com.jdh.o2oservice.b2b.application.support.OrderPlatformApplication;
import com.jdh.o2oservice.b2b.base.annotation.EnterpriseDisableCheck;
import com.jdh.o2oservice.b2b.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.b2b.base.annotation.PinB2bErp;
import com.jdh.o2oservice.b2b.base.util.ResponseUtil;
import com.jdh.o2oservice.b2b.color.GwMapUtil;
import com.jdh.o2oservice.b2b.common.response.PageDto;
import com.jdh.o2oservice.b2b.common.response.Response;
import com.jdh.o2oservice.b2b.export.orderplatform.OrderPlatformGwExport;
import com.jdh.o2oservice.b2b.export.orderplatform.cmd.CreateOrderPlatformCmd;
import com.jdh.o2oservice.b2b.export.orderplatform.dto.OrderPlatformDto;
import com.jdh.o2oservice.b2b.export.orderplatform.query.OrderPlatformPageRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;


/**
 * OrderPlatformGwExportImpl
 *
 * <AUTHOR>
 * @date 2025/02/25
 */
@Slf4j
@Service
public class OrderPlatformGwExportImpl implements OrderPlatformGwExport {

    /**
     * orderPlatformApplication
     */
    @Autowired
    private OrderPlatformApplication orderPlatformApplication;

    /**
     * createOrderPlatform
     *
     * @param param param
     * @return {@link Response }<{@link Boolean }>
     */
    @Override
    @PinB2bErp
    @EnterpriseDisableCheck
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.facade.support.OrderPlatformGwExportImpl.createOrderPlatform")
    public Response<Boolean> createOrderPlatform(Map<String, String> param) {
        CreateOrderPlatformCmd cmd = GwMapUtil.convertToParam(param, CreateOrderPlatformCmd.class);
        return ResponseUtil.buildSuccResponse(orderPlatformApplication.createOrderPlatform(cmd));
    }

    /**
     * 分页查询订单平台
     *
     * @param param param
     * @return {@link Response }<{@link PageDto }<{@link OrderPlatformDto }>>
     */
    @Override
    @PinB2bErp
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.facade.support.OrderPlatformGwExportImpl.queryOrderPlatformPage")
    public Response<PageDto<OrderPlatformDto>> queryOrderPlatformPage(Map<String, String> param) {
        OrderPlatformPageRequest pageRequest = GwMapUtil.convertToParam(param, OrderPlatformPageRequest.class);
        return ResponseUtil.buildSuccResponse(orderPlatformApplication.queryOrderPlatformPage(pageRequest));
    }
}
