package com.jdh.o2oservice.b2b.interceptor;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.jd.common.web.LoginContext;
import com.jdh.o2oservice.b2b.application.support.AuthApplication;
import com.jdh.o2oservice.b2b.base.util.ResponseUtil;
import com.jdh.o2oservice.b2b.base.util.SpringUtil;
import com.jdh.o2oservice.b2b.common.response.Response;
import com.jdh.o2oservice.b2b.domain.support.user.RequestContext;
import com.jdh.o2oservice.b2b.domain.support.user.uim.bo.Uim2RoleBO;
import com.jdh.o2oservice.b2b.domain.support.user.uim.context.UimUserContext;
import com.jdh.o2oservice.b2b.domain.support.user.uim.convert.UimConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.env.Environment;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.net.URL;
import java.util.List;

/**
 * UIM权限拦截器
 *
 * <AUTHOR>
 */
@Slf4j
public class UimAuthInterceptor implements HandlerInterceptor {

    /**
     * 环境
     */
    Environment environment;

    /**
     * 线索ERP单点登录拦截器
     */
    public UimAuthInterceptor() {

    }

    /**
     * preHandle
     *
     * @param request  请求
     * @param response 响应
     * @param handler  处理程序
     * @return boolean
     * @throws Exception Exception
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        LoginContext loginContext = LoginContext.getLoginContext();
        String pin = loginContext.getPin();
        log.info("UimAuthInterceptor preHandle pin={}", pin);
        // sso排除了一部分路径,排除路径不存在登录上下文,直接忽略
        if (loginContext == null || StringUtils.isBlank(loginContext.getPin())) {
            return true;
        }
        return setUimUserContext(request, response, loginContext);
    }

    /**
     * postHandle
     *
     * @param httpServletRequest  HTTP Servlet请求
     * @param httpServletResponse HTTP Servlet响应
     * @param o                   O
     * @param modelAndView        模型和视图
     * @throws Exception 例外情况
     */
    @Override
    public void postHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, ModelAndView modelAndView) throws Exception {
    }

    /**
     * 完工后
     *
     * @param request  请求
     * @param response 响应
     * @param handler  处理程序
     * @param ex       例如
     * @throws Exception 例外情况
     */
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        UimUserContext.remove();
    }

    /**
     * 设置环境变量
     *
     * @param environment evn
     */
    public void setEnvironment(Environment environment) {
        this.environment = environment;
    }

    /**
     * 将角色设置到上下文
     *
     * @param context ctx
     */
    private Boolean setUimUserContext(HttpServletRequest request, HttpServletResponse response, LoginContext context) throws ServletException {
        UimUserContext uimUserContext = new UimUserContext();
        AuthApplication application = SpringUtil.getBean(AuthApplication.class);
        List<Uim2RoleBO> roleBOList = application.getRole(context.getPin());
        log.info("UimAuthInterceptor setUimUserContext roleBOList={}", JSON.toJSONString(roleBOList));
        if (CollUtil.isEmpty(roleBOList)) {
            returnJson(response, "401", "无权限");
            return false;
        }
        uimUserContext.setRoleListContext(UimConvert.INSTANCE.toUimRoleContextList(roleBOList));
        uimUserContext.setLoginContext(UimConvert.INSTANCE.toUimLoginContext(context));
        Boolean checkRet = checkAuthUri(request, uimUserContext);
        UimUserContext.put(uimUserContext);
        return Boolean.TRUE.equals(checkRet);
    }


    /**
     * 校验接口权限,防止越权
     *
     * @param httpServletRequest
     */
    private Boolean checkAuthUri(HttpServletRequest httpServletRequest, UimUserContext uimUserContext) {
        try {
            RequestContext requestContext = new RequestContext();
            String requestUri = httpServletRequest.getRequestURI();
            requestContext.setRequestUri(requestUri);
            String callbackUrl = httpServletRequest.getHeader("callback");
            if (StringUtils.isNotBlank(callbackUrl)) {
                URL url = new URL(callbackUrl);
                requestContext.setPageUri(url.getPath());
            }
            uimUserContext.setRequestContext(requestContext);
            log.info("UimAuthInterceptor#checkAuthUri uimUserContext={}", JSON.toJSONString(uimUserContext));
        } catch (Exception e) {
            log.error("UimAuthInterceptor#checkAuthUri error", e);
        }
        return true;
    }

    /**
     * 返回格式化后的json到前端页面
     *
     * @param response     response
     * @param errorCode    errorCode
     * @param errorMessage errorMessage
     * @throws Exception e
     */
    private void returnJson(HttpServletResponse response, String errorCode, String errorMessage) {
        PrintWriter writer = null;
        // 返回配置为josn格式
        response.setContentType("application/json;charset=UTF-8");
        try {
            Response error = ResponseUtil.buildErrResponse(errorCode, errorMessage);
            writer = response.getWriter();
            // fastjson转换一下，返回的前端才是格式化后的json格式
            writer.print(JSON.toJSONString(error));
        } catch (Exception e) {
            log.error("UimAuthInterceptor#returnJson error", e);
        } finally {
            if (writer != null)
                writer.close();
        }
    }
}
