package com.jdh.o2oservice.b2b.facade.openapi;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.jdh.o2oservice.b2b.application.enterprisesku.B2bEnterpriseSkuApplication;
import com.jdh.o2oservice.b2b.application.enterprisevoucher.B2bEnterpriseVoucherApplication;
import com.jdh.o2oservice.b2b.application.enterprisevoucher.converter.EnterpriseVoucherAppConvert;
import com.jdh.o2oservice.b2b.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.b2b.base.constant.CommonConstant;
import com.jdh.o2oservice.b2b.base.exception.BusinessErrorCode;
import com.jdh.o2oservice.b2b.base.exception.BusinessException;
import com.jdh.o2oservice.b2b.base.util.AssertUtils;
import com.jdh.o2oservice.b2b.base.util.ResponseUtil;
import com.jdh.o2oservice.b2b.common.response.Response;
import com.jdh.o2oservice.b2b.domain.enterprise.model.JdhB2bEnterprise;
import com.jdh.o2oservice.b2b.domain.enterprise.model.JdhEnterpriseIdentifier;
import com.jdh.o2oservice.b2b.domain.enterprise.repository.JdhB2bEnterpriseRepository;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.PromiseTimelineBo;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.QueryPromiseTimelineBo;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.model.EnterpriseVoucher;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.query.EnterpriseVoucherQuery;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.repository.EnterpriseVoucherRepository;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.FileUrlBo;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.man.AgencyAppointDateBo;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.man.AgencyQueryDateBo;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.man.GetFileUrlBo;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.man.JdhStationBo;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.rpc.O2oManServicePromiseRpc;
import com.jdh.o2oservice.b2b.export.enterprisesku.dto.B2bEnterpriseSkuDto;
import com.jdh.o2oservice.b2b.export.enterprisesku.query.B2bEnterpriseSkuListRequest;
import com.jdh.o2oservice.b2b.export.enterprisevoucher.cmd.InvalidVoucherCmd;
import com.jdh.o2oservice.b2b.export.enterprisevoucher.cmd.ModifyPromiseTimeCmd;
import com.jdh.o2oservice.b2b.export.enterprisevoucher.cmd.SubmitPromiseCmd;
import com.jdh.o2oservice.b2b.export.enterprisevoucher.dto.*;
import com.jdh.o2oservice.b2b.export.enterprisevoucher.query.AvailableTimeRequest;
import com.jdh.o2oservice.b2b.export.enterprisevoucher.query.ModifyPromiseOrderPlatformRequest;
import com.jdh.o2oservice.b2b.export.enterprisevoucher.query.PromiseDetailRequest;
import com.jdh.o2oservice.b2b.export.openapi.B2bEnterpriseOpenAPIJsfExport;
import com.jdh.o2oservice.b2b.export.openapi.cmd.ModifyVoucherCmd;
import com.jdh.o2oservice.b2b.export.openapi.dto.*;
import com.jdh.o2oservice.b2b.export.openapi.query.AngelServiceRecordRequest;
import com.jdh.o2oservice.b2b.export.openapi.query.GetFileUrlRequest;
import com.jdh.o2oservice.b2b.export.openapi.query.QueryEnterpriseVoucherActionLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2025/9/16
 */
@Service
@Slf4j
public class B2bEnterpriseOpenAPIJsfExportImpl implements B2bEnterpriseOpenAPIJsfExport {

    @Resource
    B2bEnterpriseVoucherApplication b2bEnterpriseVoucherApplication;

    @Resource
    B2bEnterpriseSkuApplication b2bEnterpriseSkuApplication;

    @Resource
    O2oManServicePromiseRpc manServicePromiseRpc;

    /**
     * 企业服务单
     */
    @Autowired
    private EnterpriseVoucherRepository enterpriseVoucherRepository;

    @Autowired
    private JdhB2bEnterpriseRepository jdhB2bEnterpriseRepository;

    /**
     * 提交预约
     *
     * @param cmd
     * @return
     */
    @Override
    @LogAndAlarm
    public Response<Long> submitPromise(SubmitPromiseCmd cmd) {
        return ResponseUtil.buildSuccResponse(b2bEnterpriseVoucherApplication.submitPromise(cmd));
    }

    /**
     * 查询企业sku列表
     *
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm
    public Response<List<B2bEnterpriseSkuDto>> queryEnterpriseSkuList(B2bEnterpriseSkuListRequest request) {
        return ResponseUtil.buildSuccResponse(b2bEnterpriseSkuApplication.queryEnterpriseSkuList(request));
    }

    /**
     * 查询可约时间和是否可约
     *
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm
    public Response<PromiseAvailableTimeAndStationDto> queryAvailableTime(AvailableTimeRequest request) {
        PromiseAvailableTimeAndStationDto promiseAvailableTimeAndStationDto = new PromiseAvailableTimeAndStationDto();
        AgencyQueryDateBo bo = EnterpriseVoucherAppConvert.INS.convertAgencyQueryDateBo(request);
        List<JdhStationBo> stationBoListoList = manServicePromiseRpc.queryStationDtoList(bo);
        if(CollectionUtils.isNotEmpty(stationBoListoList)){
            promiseAvailableTimeAndStationDto.setIsAvailable(Boolean.TRUE);
        }
        List<AgencyAppointDateBo> appointDateBos = manServicePromiseRpc.queryAvailableTime(bo);
        promiseAvailableTimeAndStationDto.setPromiseAvailableTimeDtoList(EnterpriseVoucherAppConvert.INS.toAvailableTimeDto(appointDateBos));

        return ResponseUtil.buildSuccResponse(promiseAvailableTimeAndStationDto);
    }

    /**
     * 批量查询fileId对应URL
     *
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm
    public Response<List<FileUrlDto>> queryMultiFileUrl(GetFileUrlRequest request) {
        GetFileUrlBo bo = EnterpriseVoucherAppConvert.INS.convertGetFileUrlBo(request);
        List<FileUrlBo> fileUrlBos = manServicePromiseRpc.queryMultiFileUrl(bo);
        return ResponseUtil.buildSuccResponse(EnterpriseVoucherAppConvert.INS.convertFileUrlDtoList(fileUrlBos));
    }

    /**
     * 查询企业服务单操作日志
     *
     * @param query
     * @return
     */
    @Override
    @LogAndAlarm
    public Response<List<EnterpriseVoucherActionLogDto>> queryEnterpriseVoucherActionLog(QueryEnterpriseVoucherActionLog query) {
        EnterpriseVoucher enterpriseVoucher = enterpriseVoucherRepository.queryEnterpriseVoucher(EnterpriseVoucherQuery.builder().enterpriseId(query.getEnterpriseId()).enterpriseVoucherId(query.getEnterpriseVoucherId()).build());
        if (Objects.isNull(enterpriseVoucher)){
            return ResponseUtil.buildErrResponse(BusinessErrorCode.ENTERPRISE_VOUCHER_NO_EXIST);
        }
        QueryPromiseTimelineBo queryPromiseTimelineBo = new QueryPromiseTimelineBo();
        queryPromiseTimelineBo.setPromiseId(enterpriseVoucher.getPromiseId());
        PromiseTimelineBo promiseTimelineBo = manServicePromiseRpc.queryPromiseTimeline(queryPromiseTimelineBo);
        return ResponseUtil.buildSuccResponse(EnterpriseVoucherAppConvert.INS.convertEnterpriseVoucherActionLogDtoList(promiseTimelineBo.getPromiseTimelineDetailBoList()));
    }

    /**
     * 修改服务单信息。
     *
     * @param modifyVoucherCmd 包含要修改的优惠券信息的命令对象。
     * @return 返回修改结果的DTO对象。
     */
    @Override
    @LogAndAlarm
    public Response<ModifyVoucherResultDTO> modifyVoucher(ModifyVoucherCmd modifyVoucherCmd) {
        AssertUtils.nonNull(modifyVoucherCmd.getEnterpriseId(),"企业ID为空");
        AssertUtils.nonNull(modifyVoucherCmd.getEnterpriseVoucherId(),"企业订单ID为空");

        if (!needApi(modifyVoucherCmd.getEnterpriseId())){
            return null;
        }

        ModifyVoucherResultDTO resultDTO = new ModifyVoucherResultDTO();
        resultDTO.setResult(Boolean.TRUE);
        EnterpriseVoucher enterpriseVoucher = enterpriseVoucherRepository.queryEnterpriseVoucher(EnterpriseVoucherQuery.builder().enterpriseId(modifyVoucherCmd.getEnterpriseId()).enterpriseVoucherId(modifyVoucherCmd.getEnterpriseVoucherId()).build());
        if (Objects.isNull(enterpriseVoucher)){
            resultDTO.setResult(Boolean.FALSE);
            resultDTO.setMessage("企业订单不存在");
        }


        //1.修改预约
        if (Objects.nonNull(modifyVoucherCmd.getAppointmentTime())){
            ModifyPromiseTimeCmd cmd = new ModifyPromiseTimeCmd();
            cmd.setPromiseId(enterpriseVoucher.getPromiseId());
            cmd.setAppointmentTime(modifyVoucherCmd.getAppointmentTime());
            cmd.setOperator(String.valueOf(modifyVoucherCmd.getEnterpriseId()));
            //TODO
//            cmd.setOperatorRoleType();
            b2bEnterpriseVoucherApplication.modifyAppointmentTime(cmd);
        }

        //2.修改基本信息
        if (StringUtils.isNotBlank(modifyVoucherCmd.getSourceOrderId()) || StringUtils.isNotBlank(modifyVoucherCmd.getSourceOrderPlatform())){
            ModifyPromiseOrderPlatformRequest request = new ModifyPromiseOrderPlatformRequest();
            request.setEnterpriseVoucherId(modifyVoucherCmd.getEnterpriseVoucherId());
            request.setSourceOrderId(modifyVoucherCmd.getSourceOrderId());
            request.setSourceOrderPlatform(modifyVoucherCmd.getSourceOrderPlatform());
            b2bEnterpriseVoucherApplication.modifyEnterpriseVoucher(request);
        }


        return ResponseUtil.buildSuccResponse(resultDTO);
    }

    /**
     * 作废服务单
     *
     * @param cmd
     * @return
     */
    @Override
    @LogAndAlarm
    public Response<InvalidVoucherResultDTO> invalidVoucher(InvalidVoucherCmd cmd) {
        AssertUtils.nonNull(cmd.getEnterpriseId(),"企业ID为空");
        AssertUtils.nonNull(cmd.getEnterpriseVoucherId(),"企业订单ID为空");
        if (!needApi(cmd.getEnterpriseId())){
            return null;
        }
        //鉴权,置换voucherId
        InvalidVoucherResultDTO resultDTO = new InvalidVoucherResultDTO();
        resultDTO.setResult(Boolean.TRUE);

        EnterpriseVoucher enterpriseVoucher = enterpriseVoucherRepository.queryEnterpriseVoucher(EnterpriseVoucherQuery.builder().enterpriseId(cmd.getEnterpriseId()).enterpriseVoucherId(cmd.getEnterpriseVoucherId()).build());
        if (Objects.isNull(enterpriseVoucher)){
            resultDTO.setResult(Boolean.FALSE);
            resultDTO.setMessage("企业订单不存在");
        }

        //鉴权通过，则调用业务逻辑
        cmd.setVoucherId(enterpriseVoucher.getVoucherId());
        try {
            b2bEnterpriseVoucherApplication.invalidVoucher(cmd);
        }catch (BusinessException b){
            resultDTO.setResult(Boolean.FALSE);
            resultDTO.setMessage(b.getMessage());
        }catch (Exception e){
            resultDTO.setResult(Boolean.FALSE);
            resultDTO.setMessage("作废失败，请稍后再试");
        }
        return ResponseUtil.buildSuccResponse(resultDTO);
    }

    /**
     * 查询企业履约单详情
     *
     * @param promiseDetailRequest 承诺详情请求对象，包含查询条件。
     * @return CompletePromiseDetailsDto 对象，表示查询到的承诺详情。
     */
    @Override
    @LogAndAlarm
    public Response<CompletePromiseDetailsDto> queryPromiseDetail(PromiseDetailRequest promiseDetailRequest) {
        AssertUtils.nonNull(promiseDetailRequest.getEnterpriseId(),"企业ID为空");
        AssertUtils.nonNull(promiseDetailRequest.getEnterpriseVoucherId(),"企业订单ID为空");
        if (!needApi(promiseDetailRequest.getEnterpriseId())){
            return null;
        }
        //鉴权,置换promiseId
        EnterpriseVoucher enterpriseVoucher = enterpriseVoucherRepository.queryEnterpriseVoucher(EnterpriseVoucherQuery.builder().enterpriseId(promiseDetailRequest.getEnterpriseId()).enterpriseVoucherId(promiseDetailRequest.getEnterpriseVoucherId()).build());
        if (Objects.isNull(enterpriseVoucher)){
            return null;
        }

        promiseDetailRequest.setPromiseId(enterpriseVoucher.getPromiseId());
        CompletePromiseDetailsDto completePromiseDetailsDto = b2bEnterpriseVoucherApplication.queryCompletePromiseDetails(promiseDetailRequest);
        //转化toB出参
        completePromiseDetailsDto.setEnterpriseVoucherId(promiseDetailRequest.getEnterpriseVoucherId());
        CompletePromiseDetailsBDto bDto = EnterpriseVoucherAppConvert.INS.convert(completePromiseDetailsDto);
//        convertToB(completePromiseDetailsDto);
        return ResponseUtil.buildSuccResponse(EnterpriseVoucherAppConvert.INS.convert(bDto));
    }

    /**
     * 查询电子护理单
     *
     * @param request AngelServiceRecordRequest 对象，包含查询条件。
     * @return AngelServiceRecordDTO 列表，表示查询到的电子护理单记录。
     */
    @Override
    @LogAndAlarm
    public Response<List<AngelServiceRecordDTO>> queryAngelServiceRecordList(AngelServiceRecordRequest request) {
        //鉴权
        AssertUtils.nonNull(request.getEnterpriseId(),"企业ID为空");
        AssertUtils.nonNull(request.getEnterpriseVoucherId(),"企业订单ID为空");
        if (!needApi(request.getEnterpriseId())){
            return null;
        }
        //鉴权,置换promiseId
        EnterpriseVoucher enterpriseVoucher = enterpriseVoucherRepository.queryEnterpriseVoucher(EnterpriseVoucherQuery.builder().enterpriseId(request.getEnterpriseId()).enterpriseVoucherId(request.getEnterpriseVoucherId()).build());
        if (Objects.isNull(enterpriseVoucher)){
            return null;
        }
        request.setPromiseId(enterpriseVoucher.getPromiseId());
        List<AngelServiceRecordDTO> recordDTOS = b2bEnterpriseVoucherApplication.queryAngelServiceRecordList(request);

        return ResponseUtil.buildSuccResponse(recordDTOS);

    }




    private Boolean needApi(Long enterpriseId){
        JdhB2bEnterprise jdhB2bEnterprise = jdhB2bEnterpriseRepository.find(JdhEnterpriseIdentifier.builder().enterpriseId(enterpriseId).build());
        if (Objects.isNull(jdhB2bEnterprise)){
            return Boolean.FALSE;
        }
        return Objects.equals(CommonConstant.ONE,jdhB2bEnterprise.getNeedApi());
    }





}
