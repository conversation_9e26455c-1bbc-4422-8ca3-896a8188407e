package com.jdh.o2oservice.b2b.interceptor;

import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.support.config.FastJsonConfig;
import com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter;
import com.jd.ssa.oidc.client.interceptor.ErpSsoInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.http.MediaType;
import org.springframework.http.converter.ByteArrayHttpMessageConverter;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.ViewControllerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: duanqiaona1
 * @Date: 2024/4/24 10:13 上午
 * @Description:
 */
@Configuration
@Slf4j
public class ErpSsoInterceptorConfig implements WebMvcConfigurer {

    @Autowired
    private Environment env;


    /**
     * excludePath
     */
    private static final String[] CLASSPATH_RESOURCE_LOCATIONS = {"classpath:/META-INF/resources/", "classpath:/resources/", "classpath:/static/", "classpath:/public/"};


    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        log.info("ErpSsoInterceptorConfig->addInterceptors 拦截器 start");
        //sso单点登录
        try {
            log.info("ErpSsoInterceptorConfig->addInterceptors sso拦截器 start");
            registry.addInterceptor(erpSsoInterceptor()).addPathPatterns("/**").order(1);
            log.info("ErpSsoInterceptorConfig->addInterceptors sso拦截器 end");
        } catch (Exception e) {
            log.error("ErpSsoInterceptorConfig->addInterceptors sso拦截器加载异常", e);
        }
        //uim权限
        try {
            log.info("ErpSsoInterceptorConfig->addInterceptors uim拦截器 start");
            registry.addInterceptor(uimAuthInterceptor()).addPathPatterns("/**").order(2);
            log.info("ErpSsoInterceptorConfig->addInterceptors uim拦截器 end");
        } catch (Exception e) {
            log.error("ErpSsoInterceptorConfig->addInterceptors uim拦截器加载异常", e);
        }
        log.info("ErpSsoInterceptorConfig->addInterceptors 拦截器 end");
    }


    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {

        ByteArrayHttpMessageConverter byteConverter = new ByteArrayHttpMessageConverter();

        StringHttpMessageConverter converter = new StringHttpMessageConverter(StandardCharsets.UTF_8);

        //1.先定义一个convert转换消息的对象
        FastJsonHttpMessageConverter fastConverter = new FastJsonHttpMessageConverter();
        //2.添加fastjson的配置信息，比如：是否要格式化返回的json数据
        FastJsonConfig fastJsonConfig = new FastJsonConfig();
        fastJsonConfig.setSerializerFeatures(
                SerializerFeature.PrettyFormat,
                SerializerFeature.WriteNullStringAsEmpty,
                SerializerFeature.WriteNullListAsEmpty,
                SerializerFeature.WriteMapNullValue
        );
//        fastJsonConfig.setDateFormat("yyyy-MM-dd HH:mm:ss");
        //处理中文乱码问题(不然出现中文乱码)
        List<MediaType> fastMediaTypes = new ArrayList<MediaType>();
        fastMediaTypes.add(MediaType.APPLICATION_JSON_UTF8);
        fastMediaTypes.add(MediaType.APPLICATION_JSON);
        fastMediaTypes.add(new MediaType(MediaType.TEXT_PLAIN, StandardCharsets.UTF_8));
        fastMediaTypes.add(new MediaType(MediaType.TEXT_HTML, StandardCharsets.UTF_8));

        converter.setSupportedMediaTypes(fastMediaTypes);
        fastConverter.setSupportedMediaTypes(fastMediaTypes);
        //3. 添加配置信息
        fastConverter.setFastJsonConfig(fastJsonConfig);
        //4. 将convert添加到converters当中
        converters.add(byteConverter);
        converters.add(converter);
        converters.add(fastConverter);
    }


    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("swagger-ui.html").addResourceLocations("classpath:/META-INF/resources/");
        registry.addResourceHandler("/webjars/**").addResourceLocations("classpath:/META-INF/resources/webjars/");
        if (!registry.hasMappingForPattern("/**")) {
            log.info("ErpSsoInterceptorConfig->addResourceHandlers 静态资源加载 start");
            registry.addResourceHandler("/**").addResourceLocations(
                    CLASSPATH_RESOURCE_LOCATIONS);
            log.info("ErpSsoInterceptorConfig->addResourceHandlers 静态资源加载 end");
        }

    }

    /**
     * 拦截某个请求跳转固定位置
     *
     * @param registry registry
     */
    @Override
    public void addViewControllers(ViewControllerRegistry registry) {
    }

    /**
     * 定义sso拦截器Bean
     *
     * @return interceptor
     */
    @Bean
    public ErpSsoInterceptor erpSsoInterceptor() throws Exception {
        ErpSsoInterceptor interceptor = new ErpSsoInterceptor();
        interceptor.setEnvironment(this.env);
        interceptor.afterPropertiesSet();
        return interceptor;
    }

    /**
     * 定义uim拦截器Bean
     *
     * @return interceptor
     */
    @Bean
    public UimAuthInterceptor uimAuthInterceptor() throws Exception {
        UimAuthInterceptor interceptor = new UimAuthInterceptor();
        interceptor.setEnvironment(this.env);
        return interceptor;
    }

}
