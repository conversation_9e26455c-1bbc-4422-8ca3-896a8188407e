package com.jdh.o2oservice.b2b.color;
import lombok.Data;

import java.io.Serializable;
@Data
public class ColorBizParam implements Serializable {

    private Long enterpriseId;

    private String pin;

    /**
     * 企业代管模式
     */
    private Boolean escrowMode;

    /**
     * cookie中企业id
     */
    private String enterpriseNo;

    /**
     * cookie中erp账号
     */
    private String escrowNo;

    /**
     * 企业用户状态 1-启用 0-禁用
     */
    private Integer enterpriseUserAvailable;
}
