package com.jdh.o2oservice.b2b.web.controller.admin.enterpriseuser;
import com.jd.common.web.LoginContext;
import com.jdh.o2oservice.b2b.application.enterpriseuser.B2bEnterpriseUserApplication;
import com.jdh.o2oservice.b2b.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.b2b.base.exception.SystemErrorCode;
import com.jdh.o2oservice.b2b.base.util.AssertUtils;
import com.jdh.o2oservice.b2b.base.util.ResponseUtil;
import com.jdh.o2oservice.b2b.common.response.PageDto;
import com.jdh.o2oservice.b2b.common.response.Response;
import com.jdh.o2oservice.b2b.export.enterpriseuser.cmd.CreateB2bEnterpriseUserCmd;
import com.jdh.o2oservice.b2b.export.enterpriseuser.cmd.DeleteB2bEnterpriseUserCmd;
import com.jdh.o2oservice.b2b.export.enterpriseuser.cmd.UpdateB2bEnterpriseUserCmd;
import com.jdh.o2oservice.b2b.export.enterpriseuser.cmd.UpdateB2bEnterpriseUserStatusCmd;
import com.jdh.o2oservice.b2b.export.enterpriseuser.dto.B2bEnterpriseUserDto;
import com.jdh.o2oservice.b2b.export.enterpriseuser.query.B2bEnterpriseUserPageRequest;
import com.jdh.o2oservice.b2b.export.enterpriseuser.query.B2bEnterpriseUserRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;

/**
 * 企业用户
 */
@Slf4j
@RestController
@RequestMapping("/b2b/enterpriseUser")
public class B2bEnterpriseUserController {

    @Resource
    private B2bEnterpriseUserApplication b2bEnterpriseUserApplication;

    /**
     * 创建企业用户
     * @param cmd
     * @return
     */
    @PostMapping(value = "/createEnterpriseUser")
    @LogAndAlarm(jKey = "B2bEnterpriseUserController.createEnterpriseUser")
    public Response<Boolean> createEnterpriseUser(@RequestBody CreateB2bEnterpriseUserCmd cmd){
        AssertUtils.nonNull(cmd, SystemErrorCode.PARAM_NULL_ERROR);
        String pin = LoginContext.getLoginContext().getPin();
        cmd.setOperator(pin);
        Boolean result = b2bEnterpriseUserApplication.createEnterpriseUser(cmd);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 修改企业用户
     * @param cmd
     * @return
     */
    @PostMapping(value = "/updateEnterpriseUser")
    @LogAndAlarm(jKey = "B2bEnterpriseUserController.updateEnterpriseUser")
    public Response<Boolean> updateEnterpriseUser(@RequestBody UpdateB2bEnterpriseUserCmd cmd){
        AssertUtils.nonNull(cmd, SystemErrorCode.PARAM_NULL_ERROR);
        String pin = LoginContext.getLoginContext().getPin();
        cmd.setOperator(pin);
        Boolean result = b2bEnterpriseUserApplication.updateEnterpriseUser(cmd);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 修改企业用户状态
     * @param cmd
     * @return
     */
    @PostMapping(value = "/updateEnterpriseUserStatus")
    @LogAndAlarm(jKey = "B2bEnterpriseUserController.updateEnterpriseUserStatus")
    public Response<Boolean> updateEnterpriseUserStatus(@RequestBody UpdateB2bEnterpriseUserStatusCmd cmd){
        AssertUtils.nonNull(cmd, SystemErrorCode.PARAM_NULL_ERROR);
        String pin = LoginContext.getLoginContext().getPin();
        cmd.setOperator(pin);
        Boolean result = b2bEnterpriseUserApplication.updateEnterpriseUserStatus(cmd);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 删除企业用户
     * @param cmd
     * @return
     */
    @PostMapping(value = "/deleteEnterpriseUser")
    @LogAndAlarm(jKey = "B2bEnterpriseUserController.deleteEnterpriseUser")
    public Response<Boolean> deleteEnterpriseUser(@RequestBody DeleteB2bEnterpriseUserCmd cmd){
        AssertUtils.nonNull(cmd, SystemErrorCode.PARAM_NULL_ERROR);
        String pin = LoginContext.getLoginContext().getPin();
        cmd.setOperator(pin);
        Boolean result = b2bEnterpriseUserApplication.deleteEnterpriseUser(cmd);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 查询企业用户
     * @param request
     * @return
     */
    @PostMapping(value = "/queryEnterpriseUser")
    @LogAndAlarm(jKey = "B2bEnterpriseUserController.queryEnterpriseUser")
    public Response<B2bEnterpriseUserDto> queryEnterpriseUser(@RequestBody B2bEnterpriseUserRequest request){
        B2bEnterpriseUserDto result = b2bEnterpriseUserApplication.queryEnterpriseUser(request);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 分页查询企业用户
     * @param request
     * @return
     */
    @PostMapping(value = "/queryPageEnterpriseUser")
    @LogAndAlarm(jKey = "B2bEnterpriseUserController.queryPageEnterpriseUser")
    public Response<PageDto<B2bEnterpriseUserDto>> queryPageEnterpriseUser(@RequestBody B2bEnterpriseUserPageRequest request){
        PageDto<B2bEnterpriseUserDto> result = b2bEnterpriseUserApplication.queryPageEnterpriseUser(request);
        return ResponseUtil.buildSuccResponse(result);
    }
}
