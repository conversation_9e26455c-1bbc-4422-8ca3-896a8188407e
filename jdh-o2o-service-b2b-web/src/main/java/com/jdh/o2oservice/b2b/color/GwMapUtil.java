package com.jdh.o2oservice.b2b.color;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jd.ump.profiler.util.StringUtil;
import com.jdh.o2oservice.b2b.application.enterpriseuser.B2bEnterpriseUserApplication;
import com.jdh.o2oservice.b2b.base.ducc.DuccConfig;
import com.jdh.o2oservice.b2b.base.exception.BusinessErrorCode;
import com.jdh.o2oservice.b2b.base.exception.BusinessException;
import com.jdh.o2oservice.b2b.base.util.AssertUtils;
import com.jdh.o2oservice.b2b.base.util.ColorGwRequest;
import com.jdh.o2oservice.b2b.base.util.GwConstants;
import com.jdh.o2oservice.b2b.base.util.SpringUtil;
import com.jdh.o2oservice.b2b.export.enterprise.dto.EnterpriseEscrowAuthDto;
import com.jdh.o2oservice.b2b.export.enterpriseuser.dto.B2bEnterpriseUserDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 网关map转换工具类
 */
@Slf4j
@Component
public class GwMapUtil {

    /**
     * 参数转换
     *
     * @param gwMap
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T extends Object> T convertToParam(Map<?, ?> gwMap, Class<T> clazz) {
        ColorGwRequest colorGwRequest = new ColorGwRequest(gwMap);
        String body = colorGwRequest.getBody();
        T domain = null;
        if (StringUtil.isBlank(body)) {
            log.info("GwMapUtil -> convertToParam param error  body is null, gwMap={}, clazz={}", JSON.toJSONString(gwMap), clazz);
            domain = ReflectUtil.newInstance(clazz);
        }else{
            domain = colorGwRequest.toDomain(clazz);
        }
        String pin = GwMapUtil.getPin(gwMap);
        log.info("GwMapUtil -> convertToParam pin={}", pin);
        String clientIp = GwMapUtil.getIP(gwMap);
        String cookie = GwMapUtil.getCookie(gwMap);
        log.info("GwMapUtil -> convertToParam cookie={}", cookie);

        ColorBizParam colorBizParam = buildColorBizParam(pin, cookie);
        log.info("GwMapUtil -> convertToParam colorBizParam={}", JSON.toJSONString(colorBizParam));

        if(StrUtil.isNotEmpty(colorBizParam.getPin()) && Objects.nonNull(domain)){
            putUserPinField(domain,colorBizParam.getPin());
        }
        if(StrUtil.isNotEmpty(clientIp) && Objects.nonNull(domain)){
            putClientIpField(domain,clientIp);
        }
        if(Objects.nonNull(colorBizParam.getEnterpriseId()) && Objects.nonNull(domain)){
            putEnterpriseIdField(domain,colorBizParam.getEnterpriseId());
        }
        if(Objects.nonNull(colorBizParam.getEscrowMode()) && Objects.nonNull(domain)){
            putEscrowModeField(domain,colorBizParam.getEscrowMode());
        }
        if(StrUtil.isNotEmpty(colorBizParam.getEscrowNo()) && Objects.nonNull(domain)){
            putUserPinField(domain,colorBizParam.getEscrowNo());
        }
        return domain;
    }

    public static ColorBizParam buildColorBizParam(String pin, String cookie){
        ColorBizParam colorBizParam = new ColorBizParam();
        // 企业id
        Long enterpriseId = null;
        // 企业代管模式
        boolean escrowMode = false;
        // cookie中企业id
        String enterpriseNo = "";
        // cookie中erp账号
        String escrowNo = "";
        if (StringUtils.isNotBlank(cookie)){
            String[] split = cookie.split("; ");
            for (String spl : split) {
                try {
                    String[] kv = spl.split("=");
                    if (kv[0].equals("enterpriseNo")){
                        enterpriseNo =  kv[1];
                    }
                    if (kv[0].equals("escrowNo")){
                        escrowNo =  kv[1];
                    }
                } catch (Exception e) {
                }
            }
        }
        log.info("GwMapUtil -> buildColorBizParam before pin={},enterpriseId={},escrowMode={},enterpriseNo={},escrowNo={}", pin, enterpriseId, escrowMode, enterpriseNo, escrowNo);
        // 判断代管模式
        if (StringUtils.isNotBlank(enterpriseNo) && StringUtils.isNotBlank(escrowNo)){
            log.info("GwMapUtil -> buildColorBizParam escrowMode");
            // 企业代管权限配置开关
            Boolean escrowAuthConfigSwitch = SpringUtil.getBean(DuccConfig.class).getEnterpriseEscrowAuthConfigSwitch();
            if (!escrowAuthConfigSwitch){
                log.info("GwMapUtil -> buildColorBizParam escrowAuthConfigSwitch match escrowMode");
                enterpriseId = Long.valueOf(enterpriseNo);//代管企业id
                escrowMode = true;// 代管模式
                pin = escrowNo;// 代管人员erp
            }else {
                // 企业代管权限配置
                List<EnterpriseEscrowAuthDto> enterpriseEscrowAuthList = getEnterpriseEscrowAuthList(enterpriseNo);
                if (CollectionUtils.isEmpty(enterpriseEscrowAuthList)){// 非代管模式
                    log.info("GwMapUtil -> buildColorBizParam no1 match escrowMode");
                    enterpriseId = getEnterpriseId(pin, colorBizParam);
                }else {
                    List<String> erpList = enterpriseEscrowAuthList.get(0).getErpList();
                    if (erpList.contains(escrowNo)){// 代管模式
                        log.info("GwMapUtil -> buildColorBizParam match escrowMode");
                        enterpriseId = Long.valueOf(enterpriseNo);//代管企业id
                        escrowMode = true;// 代管模式
                        pin = escrowNo;// 代管人员erp
                    }else {// 非代管模式
                        log.info("GwMapUtil -> buildColorBizParam no2 match escrowMode");
                        enterpriseId = getEnterpriseId(pin, colorBizParam);
                    }
                }
            }
        }else {
            log.info("GwMapUtil -> buildColorBizParam no3 match escrowMode");
            enterpriseId = getEnterpriseId(pin, colorBizParam);
        }

        colorBizParam.setEnterpriseNo(enterpriseNo);
        colorBizParam.setEscrowNo(escrowNo);
        colorBizParam.setEscrowMode(escrowMode);
        colorBizParam.setEnterpriseId(enterpriseId);
        colorBizParam.setPin(pin);
        return colorBizParam;
    }

    private static List<EnterpriseEscrowAuthDto> getEnterpriseEscrowAuthList(String enterpriseNo) {
        // 企业代管权限配置
        List<EnterpriseEscrowAuthDto> enterpriseEscrowAuthList = JSON.parseArray(SpringUtil.getBean(DuccConfig.class).getEnterpriseEscrowAuthConfig(), EnterpriseEscrowAuthDto.class);
        if (CollectionUtils.isEmpty(enterpriseEscrowAuthList)){
            log.info("GwMapUtil -> getEnterpriseEscrowAuthList enterpriseEscrowAuthList empty");
            return Lists.newArrayList();
        }
        // 过滤当前企业代管权限配置
        enterpriseEscrowAuthList = enterpriseEscrowAuthList.stream().filter(e->e.getEnterpriseId().equals(Long.valueOf(enterpriseNo))).collect(Collectors.toList());
        log.info("GwMapUtil -> getEnterpriseEscrowAuthList enterpriseEscrowAuthList={}", JSON.toJSONString(enterpriseEscrowAuthList));
        return enterpriseEscrowAuthList;
    }

    /**
     * 参数转换
     *
     * @return
     */
    public static Map<String, Object> parseBody(Map<?, ?> gwMap) {
        ColorGwRequest colorGwRequest = new ColorGwRequest(gwMap);
        String body = colorGwRequest.getBody();
        Map<String, Object> params;
        if (StringUtils.isNoneBlank(body)) {
            params = JSON.parseObject(body);
            log.info("GwMapUtil -> parseBody");
        }else{
            params = new HashMap<>();
        }
        String pin = GwMapUtil.getPin(gwMap);
        if(StrUtil.isNotEmpty(pin)){
            params.put("userPin", pin);
        }
        return params;
    }

    /**
     * 默认塞入userPin，字段名必须为userPin
     *
     * @param domain domain
     * @param pin    pin
     */
    private static <T> void putUserPinField(T domain,String pin){
        try {
            ReflectUtil.invoke(domain,"setUserPin",pin);
        }catch (Throwable throwable){
            //log.error("GwMapUtil putUserPinField error",throwable);
        }
    }

    private static <T> void putClientIpField(T domain, String clientIp){
        try {
            ReflectUtil.invoke(domain,"setClientIp",clientIp);
        }catch (Throwable throwable){
            //log.error("GwMapUtil putClientIpField error",throwable);
        }
    }

    private static <T> void putEnterpriseIdField(T domain, Long enterpriseId){
        try {
            ReflectUtil.invoke(domain,"setEnterpriseId",enterpriseId);
        }catch (Throwable throwable){
            //log.error("GwMapUtil putClientIpField error",throwable);
        }
    }

    private static <T> void putEscrowModeField(T domain, Boolean escrowMode){
        try {
            ReflectUtil.invoke(domain,"setEscrowMode",escrowMode);
        }catch (Throwable throwable){
            //log.error("GwMapUtil putClientIpField error",throwable);
        }
    }

    private static <T> void putEscrowNoField(T domain, String clientIp){
        try {
            ReflectUtil.invoke(domain,"setEscrowNo",clientIp);
        }catch (Throwable throwable){
            //log.error("GwMapUtil putClientIpField error",throwable);
        }
    }


    /**
     * @param request
     * @return
     */
    public static String getPin(Map<?, ?> request) {
        if (null == request) {
            return null;
        }
        Object val = request.get(GwConstants.PIN);
        if (val == null) {
            return null;
        } else {
            return String.valueOf(val);
        }
    }

    public static String getCookie(Map<?, ?> request) {
        if (null == request) {
            return null;
        }
        Object val = request.get(GwConstants.COOKIE);
        if (val == null) {
            return null;
        } else {
            return String.valueOf(val);
        }
    }

    /**
     * 校验pin是否登录
     *
     * @param map
     * @return
     */
    public static void checkGwPin(Map<?, ?> map) {
        //获取pin
        String pin = getPin(map);
        //校验pin
        checkPin(pin);
    }

    /**
     * 校验pin是否登录
     *
     * @param userPin
     * @return
     */
    public static void checkPin(String userPin) {
        if (StringUtil.isBlank(userPin)) {
            log.info("GwMapUtil -> checkPin 用户未登录，请先登录, userPin={}", userPin);
            throw new BusinessException(BusinessErrorCode.UNKNOWN_USER_LOGIN);
        }
    }


    /**
     * @param request
     * @return
     */
    public static String getIP(Map<?, ?> request) {
        Object val = request.get(GwConstants.IP);
        if (val == null) {
            return null;
        } else {
            return String.valueOf(val);
        }
    }

    public static Long getEnterpriseId(String pin, ColorBizParam colorBizParam){
        if (StringUtils.isBlank(pin)){
            log.info("GwMapUtil -> getEnterpriseId pin empty");
            return 0L;
        }
        B2bEnterpriseUserDto enterpriseUser = SpringUtil.getBean(B2bEnterpriseUserApplication.class).findEnterpriseUserByPin(pin);
        AssertUtils.nonNull(enterpriseUser, BusinessErrorCode.ENTERPRISE_USER_NO_EXIST);
        colorBizParam.setEnterpriseUserAvailable(enterpriseUser.getAvailable());
        return enterpriseUser.getEnterpriseId();
    }

}
