package com.jdh.o2oservice.b2b;

import com.alibaba.fastjson.JSON;
import com.jd.security.configsec.spring.config.JDSecurityPropertySourceFactory;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.*;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * @Date 2025/2/23 下午6:24
 * <AUTHOR>
 **/
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@PropertySources(value = {
        @PropertySource(value = {"classpath:important.properties"}, encoding = "utf-8", factory = JDSecurityPropertySourceFactory.class, ignoreResourceNotFound = true),
        @PropertySource(value = {"classpath:config.properties"}, encoding = "utf-8", ignoreResourceNotFound = true),
})
@ImportResource(locations = {"classpath:jsf-provider.xml", "classpath:jsf-consumer.xml", "classpath:spring-config-matrix.xml"})
@EnableAspectJAutoProxy(proxyTargetClass = true, exposeProxy = true)
@MapperScan("com.jdh.o2oservice.b2b.infrastructure.repository.db")
@EnableTransactionManagement
@Slf4j
public class StartApplication extends SpringBootServletInitializer {

    /**
     * 配置springboot打war情况下启动问题
     *
     * @param application 应用程序
     * @return {@link SpringApplicationBuilder}
     */
    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(StartApplication.class);
    }

    public static void main(String[] args) {
        SpringApplication.run(StartApplication.class, args);
    }

    /**
     * 应用启动后执行
     * @param ctx
     * @return
     */
    @Bean
    public CommandLineRunner commandLineRunner(ApplicationContext ctx) {
        return args -> {
            String[] beanNames =  ctx.getBeanDefinitionNames();
            log.info("StartApplication beanNames={}", JSON.toJSONString(beanNames));
        };
    }
}
