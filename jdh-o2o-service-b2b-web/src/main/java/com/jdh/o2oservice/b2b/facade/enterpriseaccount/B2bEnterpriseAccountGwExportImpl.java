package com.jdh.o2oservice.b2b.facade.enterpriseaccount;
import com.jdh.o2oservice.b2b.application.enterpriseaccount.B2bEnterpriseAccountApplication;
import com.jdh.o2oservice.b2b.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.b2b.base.annotation.PinB2bErp;
import com.jdh.o2oservice.b2b.base.util.ResponseUtil;
import com.jdh.o2oservice.b2b.color.GwMapUtil;
import com.jdh.o2oservice.b2b.common.response.Response;
import com.jdh.o2oservice.b2b.export.enterprise.query.B2bEnterpriseRequest;
import com.jdh.o2oservice.b2b.export.enterpriseaccount.B2bEnterpriseAccountGwExport;
import com.jdh.o2oservice.b2b.export.enterpriseaccount.dto.B2bEnterpriseAccountCreditAmountTipDto;
import com.jdh.o2oservice.b2b.export.enterpriseaccount.dto.B2bEnterpriseAcountContractDto;
import com.jdh.o2oservice.b2b.export.enterpriseaccount.query.B2bEnterpriseAccountRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.Map;


/**
 * @ClassName:B2bEnterpriseAccountGwExport
 * @Description:B2b企业账户Export
 * @Author: liwenming
 * @Date: 2025/2/27 15:14
 * @Vserion: 1.0
 **/
@Service("b2bEnterpriseAccountGwExportImpl")
@Slf4j
public class B2bEnterpriseAccountGwExportImpl implements B2bEnterpriseAccountGwExport {

    /** */
    @Resource
    private B2bEnterpriseAccountApplication b2bEnterpriseAccountApplication;


    /**
     * 查询企业账户和合同信息
     *
     * @param b2bEnterpriseRequest
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.facade.enterpriseaccount.B2bEnterpriseAccountGwExportImpl.queryEnterpriseAccountAndContract")
    public Response<B2bEnterpriseAcountContractDto> queryEnterpriseAccountAndContract(B2bEnterpriseRequest b2bEnterpriseRequest) {
        B2bEnterpriseAcountContractDto contractDto = b2bEnterpriseAccountApplication.queryEnterpriseAccount(b2bEnterpriseRequest);
        return ResponseUtil.buildSuccResponse(contractDto);
    }

    /**
     * 查询信用额度提醒
     * @param param
     * @return
     */
    @Override
    @PinB2bErp
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.facade.enterpriseaccount.B2bEnterpriseAccountGwExportImpl.queryAccountCreditAmountTip")
    public Response<B2bEnterpriseAccountCreditAmountTipDto> queryAccountCreditAmountTip(Map<String, String> param) {
        B2bEnterpriseAccountRequest request = GwMapUtil.convertToParam(param, B2bEnterpriseAccountRequest.class);
        return ResponseUtil.buildSuccResponse(b2bEnterpriseAccountApplication.queryAccountCreditAmountTip(request));
    }
}
