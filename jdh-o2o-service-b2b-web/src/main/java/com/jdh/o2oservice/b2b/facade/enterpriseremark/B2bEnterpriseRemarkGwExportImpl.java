package com.jdh.o2oservice.b2b.facade.enterpriseremark;

import com.jdh.o2oservice.b2b.application.enterpriseremark.B2bEnterpriseRemarkApplication;
import com.jdh.o2oservice.b2b.base.annotation.EnterpriseDisableCheck;
import com.jdh.o2oservice.b2b.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.b2b.base.annotation.PinB2bErp;
import com.jdh.o2oservice.b2b.base.util.ResponseUtil;
import com.jdh.o2oservice.b2b.color.GwMapUtil;
import com.jdh.o2oservice.b2b.common.response.PageDto;
import com.jdh.o2oservice.b2b.common.response.Response;
import com.jdh.o2oservice.b2b.export.enterpriseremark.B2bEnterpriseRemarkGwExport;
import com.jdh.o2oservice.b2b.export.enterpriseremark.cmd.AddEnterpriseRemarkCmd;
import com.jdh.o2oservice.b2b.export.enterpriseremark.cmd.DeleteEnterpriseRemarkCmd;
import com.jdh.o2oservice.b2b.export.enterpriseremark.dto.EnterpriseRemarkDto;
import com.jdh.o2oservice.b2b.export.enterpriseremark.query.EnterpriseRemarkPageRequest;
import com.jdh.o2oservice.b2b.export.enterpriseremark.query.EnterpriseRemarkRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 企业备注对外接口实现
 *
 * <AUTHOR>
 * @date 2025/03/10
 */
@Slf4j
@Service("b2bEnterpriseRemarkGwExportImpl")
public class B2bEnterpriseRemarkGwExportImpl implements B2bEnterpriseRemarkGwExport {

    /**
     * 企业备注应用层
     */
    @Resource
    private B2bEnterpriseRemarkApplication b2bEnterpriseRemarkApplication;

    /**
     * 添加企业备注
     *
     * @param param 参数
     * @return 是否成功
     */
    @Override
    @PinB2bErp
    @EnterpriseDisableCheck
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.facade.enterpriseremark.B2bEnterpriseRemarkGwExportImpl.addEnterpriseRemark")
    public Response<Boolean> addEnterpriseRemark(Map<String, String> param) {
        // 转换参数
        AddEnterpriseRemarkCmd cmd = GwMapUtil.convertToParam(param, AddEnterpriseRemarkCmd.class);
        // 调用应用层方法
        return ResponseUtil.buildSuccResponse(b2bEnterpriseRemarkApplication.addEnterpriseRemark(cmd));
    }

    /**
     * 查询企业备注
     *
     * @param param 参数
     * @return 企业备注
     */
    @Override
    @PinB2bErp
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.facade.enterpriseremark.B2bEnterpriseRemarkGwExportImpl.queryEnterpriseRemark")
    public Response<EnterpriseRemarkDto> queryEnterpriseRemark(Map<String, String> param) {
        EnterpriseRemarkRequest request = GwMapUtil.convertToParam(param, EnterpriseRemarkRequest.class);
        return ResponseUtil.buildSuccResponse(b2bEnterpriseRemarkApplication.queryEnterpriseRemark(request));
    }

    /**
     * 分页查询企业备注
     *
     * @param param 参数
     * @return 分页结果
     */
    @Override
    @PinB2bErp
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.facade.enterpriseremark.B2bEnterpriseRemarkGwExportImpl.queryEnterpriseRemarkPage")
    public Response<PageDto<EnterpriseRemarkDto>> queryEnterpriseRemarkPage(Map<String, String> param) {
        // 转换参数
        EnterpriseRemarkPageRequest request = GwMapUtil.convertToParam(param, EnterpriseRemarkPageRequest.class);

        // 调用应用层方法
        return ResponseUtil.buildSuccResponse(b2bEnterpriseRemarkApplication.queryEnterpriseRemarkPage(request));
    }

    /**
     * 查询企业备注列表
     *
     * @param param 参数
     * @return 企业备注列表
     */
    @Override
    @PinB2bErp
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.facade.enterpriseremark.B2bEnterpriseRemarkGwExportImpl.queryEnterpriseRemarkList")
    public Response<List<EnterpriseRemarkDto>> queryEnterpriseRemarkList(Map<String, String> param) {
        EnterpriseRemarkRequest request = GwMapUtil.convertToParam(param, EnterpriseRemarkRequest.class);
        return ResponseUtil.buildSuccResponse(b2bEnterpriseRemarkApplication.queryEnterpriseRemarkList(request));
    }

    /**
     * 删除企业备注
     *
     * @param param 参数
     * @return 是否成功
     */
    @Override
    @PinB2bErp
    @EnterpriseDisableCheck
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.facade.enterpriseremark.B2bEnterpriseRemarkGwExportImpl.deleteEnterpriseRemark")
    public Response<Boolean> deleteEnterpriseRemark(Map<String, String> param) {
        // 获取当前登录用户PIN
        DeleteEnterpriseRemarkCmd cmd = GwMapUtil.convertToParam(param, DeleteEnterpriseRemarkCmd.class);

        // 调用应用层方法
        return ResponseUtil.buildSuccResponse(b2bEnterpriseRemarkApplication.deleteEnterpriseRemark(cmd));
    }
}
