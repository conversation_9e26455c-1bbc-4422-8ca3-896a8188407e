package com.jdh.o2oservice.b2b.facade.enterprisesku;

import com.jdh.o2oservice.b2b.application.enterprisesku.B2bEnterpriseSkuApplication;
import com.jdh.o2oservice.b2b.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.b2b.base.annotation.PinB2bErp;
import com.jdh.o2oservice.b2b.base.util.ResponseUtil;
import com.jdh.o2oservice.b2b.base.util.UserPinContext;
import com.jdh.o2oservice.b2b.color.GwMapUtil;
import com.jdh.o2oservice.b2b.common.response.PageDto;
import com.jdh.o2oservice.b2b.common.response.Response;
import com.jdh.o2oservice.b2b.export.enterprisesku.B2bEnterpriseSkuGwExport;
import com.jdh.o2oservice.b2b.export.enterprisesku.dto.B2bEnterpriseSkuDto;
import com.jdh.o2oservice.b2b.export.enterprisesku.query.B2bEnterpriseSkuListRequest;
import com.jdh.o2oservice.b2b.export.enterprisesku.query.B2bEnterpriseSkuPageRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 企业SKU对外接口实现
 *
 * <AUTHOR>
 * @date 2025/03/15
 */
@Slf4j
@Service
public class B2bEnterpriseSkuGwExportImpl implements B2bEnterpriseSkuGwExport {

    /**
     * 企业SKU应用层
     */
    @Resource
    private B2bEnterpriseSkuApplication b2bEnterpriseSkuApplication;

    /**
     * 查询当前登录用户企业的SKU列表
     *
     * @param param 参数，可通过content字段搜索，不传则返回所有
     * @return 企业SKU列表
     */
    @Override
    @PinB2bErp
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.facade.enterprisesku.B2bEnterpriseSkuGwExportImpl.queryEnterpriseSkuList")
    public Response<List<B2bEnterpriseSkuDto>> queryEnterpriseSkuList(Map<String, String> param) {
        // 获取当前登录用户PIN
        String userPin = UserPinContext.get();
        log.info("B2bEnterpriseSkuGwExportImpl queryEnterpriseSkuList userPin={}", userPin);

        // 转换参数
        B2bEnterpriseSkuListRequest request = GwMapUtil.convertToParam(param, B2bEnterpriseSkuListRequest.class);
        request.setUserPin(userPin);

        // 调用应用层方法
        return ResponseUtil.buildSuccResponse(b2bEnterpriseSkuApplication.queryEnterpriseSkuList(request));
    }

    /**
     * 分页查询企业SKU
     *
     * @param param 参数
     * @return 分页结果
     */
    @Override
    @PinB2bErp
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.facade.enterprisesku.B2bEnterpriseSkuGwExportImpl.queryPageEnterpriseSku")
    public Response<PageDto<B2bEnterpriseSkuDto>> queryPageEnterpriseSku(Map<String, String> param) {
        B2bEnterpriseSkuPageRequest request = GwMapUtil.convertToParam(param, B2bEnterpriseSkuPageRequest.class);
        return ResponseUtil.buildSuccResponse(b2bEnterpriseSkuApplication.queryPageEnterpriseSku(request));
    }
}
