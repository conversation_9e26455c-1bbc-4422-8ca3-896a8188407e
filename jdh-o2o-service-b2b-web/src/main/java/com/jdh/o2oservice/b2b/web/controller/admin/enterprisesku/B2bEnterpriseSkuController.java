package com.jdh.o2oservice.b2b.web.controller.admin.enterprisesku;
import com.jd.common.web.LoginContext;
import com.jdh.o2oservice.b2b.application.enterprisesku.B2bEnterpriseSkuApplication;
import com.jdh.o2oservice.b2b.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.b2b.base.exception.SystemErrorCode;
import com.jdh.o2oservice.b2b.base.util.AssertUtils;
import com.jdh.o2oservice.b2b.base.util.ResponseUtil;
import com.jdh.o2oservice.b2b.common.response.PageDto;
import com.jdh.o2oservice.b2b.common.response.Response;
import com.jdh.o2oservice.b2b.export.enterprisesku.cmd.CreateB2bEnterpriseSkuCmd;
import com.jdh.o2oservice.b2b.export.enterprisesku.cmd.DeleteB2bEnterpriseSkuCmd;
import com.jdh.o2oservice.b2b.export.enterprisesku.cmd.UpdateB2bEnterpriseSkuCmd;
import com.jdh.o2oservice.b2b.export.enterprisesku.dto.B2bEnterpriseSkuDto;
import com.jdh.o2oservice.b2b.export.enterprisesku.dto.JdhSkuInfoDto;
import com.jdh.o2oservice.b2b.export.enterprisesku.query.B2bEnterpriseSkuPageRequest;
import com.jdh.o2oservice.b2b.export.enterprisesku.query.B2bEnterpriseSkuRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;

/**
 * @Description 企业sku
 * @Date 2025/2/24 下午8:08
 * <AUTHOR>
 **/
@Slf4j
@RestController
@RequestMapping("/b2b/enterpriseSku")
public class B2bEnterpriseSkuController {

    @Resource
    private B2bEnterpriseSkuApplication b2bEnterpriseSkuApplication;

    /**
     * 创建企业sku
     * @param cmd
     * @return
     */
    @PostMapping(value = "/createEnterpriseSku")
    @LogAndAlarm(jKey = "B2bEnterpriseSkuController.createEnterpriseSku")
    public Response<Boolean> createEnterpriseSku(@RequestBody CreateB2bEnterpriseSkuCmd cmd){
        AssertUtils.nonNull(cmd, SystemErrorCode.PARAM_NULL_ERROR);
        String pin = LoginContext.getLoginContext().getPin();
        cmd.setOperator(pin);
        Boolean result = b2bEnterpriseSkuApplication.createEnterpriseSku(cmd);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 修改企业sku
     * @param cmd
     * @return
     */
    @PostMapping(value = "/updateEnterpriseSku")
    @LogAndAlarm(jKey = "B2bEnterpriseSkuController.updateEnterpriseSku")
    public Response<Boolean> updateEnterpriseSku(@RequestBody UpdateB2bEnterpriseSkuCmd cmd){
        AssertUtils.nonNull(cmd, SystemErrorCode.PARAM_NULL_ERROR);
        String pin = LoginContext.getLoginContext().getPin();
        cmd.setOperator(pin);
        Boolean result = b2bEnterpriseSkuApplication.updateEnterpriseSku(cmd);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 删除企业sku
     * @param cmd
     * @return
     */
    @PostMapping(value = "/deleteEnterpriseSku")
    @LogAndAlarm(jKey = "B2bEnterpriseSkuController.deleteEnterpriseSku")
    public Response<Boolean> deleteEnterpriseSku(@RequestBody DeleteB2bEnterpriseSkuCmd cmd){
        AssertUtils.nonNull(cmd, SystemErrorCode.PARAM_NULL_ERROR);
        String pin = LoginContext.getLoginContext().getPin();
        cmd.setOperator(pin);
        Boolean result = b2bEnterpriseSkuApplication.deleteEnterpriseSku(cmd);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 查询企业sku
     * @param request
     * @return
     */
    @PostMapping(value = "/queryEnterpriseSku")
    @LogAndAlarm(jKey = "B2bEnterpriseSkuController.queryEnterpriseSku")
    public Response<B2bEnterpriseSkuDto> queryEnterpriseSku(@RequestBody B2bEnterpriseSkuRequest request){
        B2bEnterpriseSkuDto result = b2bEnterpriseSkuApplication.queryEnterpriseSku(request);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 分页查询企业sku
     * @param request
     * @return
     */
    @PostMapping(value = "/queryPageEnterpriseSku")
    @LogAndAlarm(jKey = "B2bEnterpriseSkuController.queryPageEnterpriseSku")
    public Response<PageDto<B2bEnterpriseSkuDto>> queryPageEnterpriseSku(@RequestBody B2bEnterpriseSkuPageRequest request){
        PageDto<B2bEnterpriseSkuDto> result = b2bEnterpriseSkuApplication.queryPageEnterpriseSku(request);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 查询sku
     * @param request
     * @return
     */
    @PostMapping(value = "/querySku")
    @LogAndAlarm(jKey = "B2bEnterpriseSkuController.querySku")
    public Response<JdhSkuInfoDto> querySku(@RequestBody B2bEnterpriseSkuRequest request){
        JdhSkuInfoDto result = b2bEnterpriseSkuApplication.querySku(request);
        return ResponseUtil.buildSuccResponse(result);
    }
}
