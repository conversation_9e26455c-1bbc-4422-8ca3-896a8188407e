package com.jdh.o2oservice.b2b.web.controller.admin.enterprisebill;

import com.jd.common.web.LoginContext;
import com.jdh.o2oservice.b2b.application.enterprisebill.convert.B2bEnterpriseBillConvert;
import com.jdh.o2oservice.b2b.application.enterprisebill.service.B2bEnterpriseBillApplication;
import com.jdh.o2oservice.b2b.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.b2b.base.exception.SystemErrorCode;
import com.jdh.o2oservice.b2b.base.util.AssertUtils;
import com.jdh.o2oservice.b2b.base.util.ResponseUtil;
import com.jdh.o2oservice.b2b.common.response.PageDto;
import com.jdh.o2oservice.b2b.common.response.Response;
import com.jdh.o2oservice.b2b.domain.enterprisebill.context.B2bEnterpriseBillQueryContext;
import com.jdh.o2oservice.b2b.export.enterprisebill.cmd.B2bEnterpriseBillCmd;
import com.jdh.o2oservice.b2b.export.enterprisebill.dto.B2bEnterpriseBillDto;
import com.jdh.o2oservice.b2b.export.enterprisebill.query.B2bEnterpriseBillRequest;
import com.jdh.o2oservice.export.angel.cmd.BindAngelMasterCmd;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @ClassName:B2bEnterpriseBillController
 * @Description: 企业账单
 * @Author: liwenming
 * @Date: 2025/2/25 15:14
 * @Vserion: 1.0
 **/
@Slf4j
@RestController
@RequestMapping("/b2b/bill")
public class B2bEnterpriseBillController {

    /** */
    @Autowired
    private B2bEnterpriseBillApplication b2bEnterpriseBillApplication;

    /**
     * 查询企业账单列表
     * @param b2bEnterpriseBillRequest
     * @return
     */
    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/queryEnterpriseBillPage")
    @LogAndAlarm(jKey = "B2bEnterpriseBillController.queryEnterpriseBillPage")
    public Response<PageDto<B2bEnterpriseBillDto>> queryEnterpriseBillPage(@RequestBody B2bEnterpriseBillRequest b2bEnterpriseBillRequest) {
        B2bEnterpriseBillQueryContext queryContext = B2bEnterpriseBillConvert.ins.queryToContext(b2bEnterpriseBillRequest);
        PageDto<B2bEnterpriseBillDto> result = b2bEnterpriseBillApplication.queryEnterpriseBillPage(queryContext);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 企业账单调账
     * @param b2bEnterpriseBillCmd
     * @return
     */
    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/adjustEnterpriseBill")
    @LogAndAlarm(jKey = "B2bEnterpriseBillController.adjustEnterpriseBill")
    public Response<Boolean> adjustEnterpriseBill(@RequestBody B2bEnterpriseBillCmd b2bEnterpriseBillCmd) {
        AssertUtils.nonNull(b2bEnterpriseBillCmd, SystemErrorCode.PARAM_NULL_ERROR);
        String pin = LoginContext.getLoginContext().getPin();
        b2bEnterpriseBillCmd.setAdjustUser(pin);
        return ResponseUtil.buildSuccResponse(b2bEnterpriseBillApplication.adjustEnterpriseBill(b2bEnterpriseBillCmd));
    }

    /**
     * 企业账单--确认到账
     * @param b2bEnterpriseBillCmd
     * @return
     */
    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/configEnterpriseBill")
    @LogAndAlarm(jKey = "B2bEnterpriseBillController.configEnterpriseBill")
    public Response<Boolean> configEnterpriseBill(@RequestBody B2bEnterpriseBillCmd b2bEnterpriseBillCmd) {
        AssertUtils.nonNull(b2bEnterpriseBillCmd, SystemErrorCode.PARAM_NULL_ERROR);
        String pin = LoginContext.getLoginContext().getPin();
        b2bEnterpriseBillCmd.setAdjustUser(pin);
        return ResponseUtil.buildSuccResponse(b2bEnterpriseBillApplication.configEnterpriseBill(b2bEnterpriseBillCmd));
    }
}
