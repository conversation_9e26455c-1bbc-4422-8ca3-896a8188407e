package com.jdh.o2oservice.b2b.facade.enterprisebill;

import com.jdh.o2oservice.b2b.application.enterpriseaccount.B2bEnterpriseAccountApplication;
import com.jdh.o2oservice.b2b.application.enterprisebill.service.B2bEnterpriseBillApplication;
import com.jdh.o2oservice.b2b.application.enterpriseuser.B2bEnterpriseUserApplication;
import com.jdh.o2oservice.b2b.base.annotation.EnterpriseDisableCheck;
import com.jdh.o2oservice.b2b.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.b2b.base.annotation.PinB2bErp;
import com.jdh.o2oservice.b2b.base.exception.SystemErrorCode;
import com.jdh.o2oservice.b2b.base.util.AssertUtils;
import com.jdh.o2oservice.b2b.base.util.ResponseUtil;
import com.jdh.o2oservice.b2b.base.util.UserPinContext;
import com.jdh.o2oservice.b2b.color.GwMapUtil;
import com.jdh.o2oservice.b2b.common.response.PageDto;
import com.jdh.o2oservice.b2b.common.response.Response;
import com.jdh.o2oservice.b2b.domain.enterprisebill.context.B2bEnterpriseBillQueryContext;
import com.jdh.o2oservice.b2b.domain.enterprisebill.enums.EnterpriseBillErrorCode;
import com.jdh.o2oservice.b2b.export.enterprise.cmd.B2bEnterpriseContractCmd;
import com.jdh.o2oservice.b2b.export.enterprise.query.B2bEnterprisePageRequest;
import com.jdh.o2oservice.b2b.export.enterprise.query.B2bEnterpriseRequest;
import com.jdh.o2oservice.b2b.export.enterpriseaccount.dto.B2bEnterpriseAcountContractDto;
import com.jdh.o2oservice.b2b.export.enterpriseaccount.dto.B2bEnterpriseContractDetailDto;
import com.jdh.o2oservice.b2b.export.enterprisebill.B2bEnterpriseBillGwExport;
import com.jdh.o2oservice.b2b.export.enterprisebill.cmd.B2bEnterpriseBillCmd;
import com.jdh.o2oservice.b2b.export.enterprisebill.dto.B2bEnterpriseBillDetailDto;
import com.jdh.o2oservice.b2b.export.enterprisebill.dto.B2bEnterpriseBillDto;
import com.jdh.o2oservice.b2b.export.enterprisebill.query.B2bEnterpriseBillRequest;
import com.jdh.o2oservice.b2b.export.enterprisesku.query.B2bEnterpriseSkuListRequest;
import com.jdh.o2oservice.b2b.export.enterpriseuser.dto.B2bEnterpriseUserDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Map;

/**
 * @ClassName:B2bEnterpriseBillGwExport
 * @Description:B2b企业账单Export
 * @Author: liwenming
 * @Date: 2025/2/27 15:14
 * @Vserion: 1.0
 **/
@Slf4j
@Service("b2bEnterpriseBillGwExportImpl")
public class B2bEnterpriseBillGwExportImpl implements B2bEnterpriseBillGwExport {

    /** */
    @Resource
    private B2bEnterpriseBillApplication b2bEnterpriseBillApplication;
    /** */
    @Resource
    private B2bEnterpriseAccountApplication b2bEnterpriseAccountApplication;
    /** */
    @Resource
    private B2bEnterpriseUserApplication b2bEnterpriseUserApplication;


    /**
     * 查询企业月账单
     *
     * @param param
     * @return
     */
    @Override
    @PinB2bErp
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.facade.enterprisebill.B2bEnterpriseBillGwExportImpl.queryEnterpriseBill")
    public Response<B2bEnterpriseBillDto> queryEnterpriseBill(Map<String, String> param) {
        B2bEnterpriseBillRequest b2bEnterpriseBillRequest = GwMapUtil.convertToParam(param, B2bEnterpriseBillRequest.class);
        AssertUtils.nonNull(b2bEnterpriseBillRequest, SystemErrorCode.PARAM_NULL_ERROR);
        AssertUtils.nonNull(b2bEnterpriseBillRequest.getEnterpriseId(), EnterpriseBillErrorCode.ENTERPRISE_ID_NULL);
        AssertUtils.nonNull(b2bEnterpriseBillRequest.getBillDate(), EnterpriseBillErrorCode.BILL_DATE_NULL);
        B2bEnterpriseBillQueryContext queryContext = new B2bEnterpriseBillQueryContext();
        queryContext.setEnterpriseId(b2bEnterpriseBillRequest.getEnterpriseId());
        queryContext.setBillDate(b2bEnterpriseBillRequest.getBillDate());
        return ResponseUtil.buildSuccResponse(b2bEnterpriseBillApplication.queryEnterpriseBillByDate(queryContext));
    }

    /**
     * 查询企业月账单明细
     *
     * @param param
     * @return
     */
    @Override
    @PinB2bErp
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.facade.enterprisebill.B2bEnterpriseBillGwExportImpl.queryEnterpriseBillDetail")
    public Response<PageDto<B2bEnterpriseBillDetailDto>> queryEnterpriseBillDetail(Map<String, String> param) {
        B2bEnterpriseBillRequest b2bEnterpriseBillRequest = GwMapUtil.convertToParam(param, B2bEnterpriseBillRequest.class);
        AssertUtils.nonNull(b2bEnterpriseBillRequest, SystemErrorCode.PARAM_NULL_ERROR);
        AssertUtils.nonNull(b2bEnterpriseBillRequest.getEnterpriseId(), EnterpriseBillErrorCode.ENTERPRISE_ID_NULL);
        AssertUtils.nonNull(b2bEnterpriseBillRequest.getBillDate(), EnterpriseBillErrorCode.BILL_DATE_NULL);
        B2bEnterpriseBillQueryContext queryContext = new B2bEnterpriseBillQueryContext();
        queryContext.setEnterpriseId(b2bEnterpriseBillRequest.getEnterpriseId());
        queryContext.setBillDate(b2bEnterpriseBillRequest.getBillDate());
        queryContext.setEnterpriseSkuIdSet(b2bEnterpriseBillRequest.getEnterpriseSkuIdSet());
        queryContext.setPromiseStatusSet(b2bEnterpriseBillRequest.getPromiseStatusSet());
        queryContext.setPromiseId(b2bEnterpriseBillRequest.getPromiseId());
        queryContext.setStatus(b2bEnterpriseBillRequest.getStatus());
        queryContext.setPageNum(b2bEnterpriseBillRequest.getPageNum());
        queryContext.setPageSize(b2bEnterpriseBillRequest.getPageSize());
        queryContext.setOrderTimeStart(b2bEnterpriseBillRequest.getOrderTimeStart());
        queryContext.setOrderTimeEnd(b2bEnterpriseBillRequest.getOrderTimeEnd());
        queryContext.setEnterpriseVoucherId(b2bEnterpriseBillRequest.getEnterpriseVoucherId());
        return ResponseUtil.buildSuccResponse(b2bEnterpriseBillApplication.queryEnterpriseBillDetailByDate(queryContext));
    }

    /**
     * 查询企业月账单明细
     *
     * @param b2bEnterpriseBillRequest
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.facade.enterprisebill.B2bEnterpriseBillGwExportImpl.queryEnterpriseBillDetailList")
    public Response<PageDto<B2bEnterpriseBillDetailDto>> queryEnterpriseBillDetailList(B2bEnterpriseBillRequest b2bEnterpriseBillRequest) {
        AssertUtils.nonNull(b2bEnterpriseBillRequest, SystemErrorCode.PARAM_NULL_ERROR);
        B2bEnterpriseUserDto b2bEnterpriseUserDto = b2bEnterpriseUserApplication.findEnterpriseUserByPin(b2bEnterpriseBillRequest.getUserPin());
        AssertUtils.nonNull(b2bEnterpriseUserDto.getEnterpriseId(), EnterpriseBillErrorCode.ENTERPRISE_ID_NULL);
        AssertUtils.nonNull(b2bEnterpriseBillRequest.getBillDate(), EnterpriseBillErrorCode.BILL_DATE_NULL);
        B2bEnterpriseBillQueryContext queryContext = new B2bEnterpriseBillQueryContext();
        queryContext.setEnterpriseId(b2bEnterpriseUserDto.getEnterpriseId());
        queryContext.setBillDate(b2bEnterpriseBillRequest.getBillDate());
        queryContext.setEnterpriseSkuIdSet(b2bEnterpriseBillRequest.getEnterpriseSkuIdSet());
        queryContext.setPromiseStatusSet(b2bEnterpriseBillRequest.getPromiseStatusSet());
        queryContext.setPromiseId(b2bEnterpriseBillRequest.getPromiseId());
        queryContext.setStatus(b2bEnterpriseBillRequest.getStatus());
        queryContext.setOrderTimeStart(b2bEnterpriseBillRequest.getOrderTimeStart());
        queryContext.setOrderTimeEnd(b2bEnterpriseBillRequest.getOrderTimeEnd());
        return ResponseUtil.buildSuccResponse(b2bEnterpriseBillApplication.queryEnterpriseBillDetailByDate(queryContext));
    }

    /**
     * 导出企业月账单明细
     *
     * @param param@return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.facade.enterprisebill.B2bEnterpriseBillGwExportImpl.exportEnterpriseBillDetail")
    public Response<Boolean> exportEnterpriseBillDetail(Map<String, String> param) {
        B2bEnterpriseBillRequest b2bEnterpriseBillRequest = GwMapUtil.convertToParam(param, B2bEnterpriseBillRequest.class);
        return ResponseUtil.buildSuccResponse(b2bEnterpriseBillApplication.exportEnterpriseBillDetail(b2bEnterpriseBillRequest));
    }

    /**
     * 企业月账单Job
     */
    @Override
    public Response<Boolean> enterpriseBillOfMonthJob() {
        log.info("[B2bEnterpriseBillGwExportImpl.enterpriseBillOfMonthJob] start");
        LocalDate today = LocalDate.now();
        LocalDate lastMonthFirstDay = today.minusMonths(1).withDayOfMonth(1);
        LocalDate lastMonthLastDay = today.withDayOfMonth(1);
        b2bEnterpriseBillApplication.createEnterpriseBill(lastMonthFirstDay.toString(),lastMonthLastDay.toString());
        return ResponseUtil.buildSuccResponse(true);
    }

    /**
     * 企业账单--确认账单
     *
     * @param param
     * @return
     */
    @Override
    @PinB2bErp
    @EnterpriseDisableCheck
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.facade.enterprisebill.B2bEnterpriseBillGwExportImpl.configEnterpriseBill")
    public Response<Boolean> configEnterpriseBill(Map<String, String> param) {
        B2bEnterpriseBillCmd b2bEnterpriseBillCmd = GwMapUtil.convertToParam(param, B2bEnterpriseBillCmd.class);
        AssertUtils.nonNull(b2bEnterpriseBillCmd, SystemErrorCode.PARAM_NULL_ERROR);
        return ResponseUtil.buildSuccResponse(b2bEnterpriseBillApplication.confirmEnterpriseBill(b2bEnterpriseBillCmd));
    }

    /**
     * 查询合同信息
     *
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.facade.enterprisebill.B2bEnterpriseBillGwExportImpl.queryAccountInfoByContractNum")
    public Response<B2bEnterpriseContractDetailDto> queryAccountInfoByContractNum(B2bEnterpriseRequest request) {
        return ResponseUtil.buildSuccResponse(b2bEnterpriseAccountApplication.queryAccountInfoByContractNum(request));
    }

    /**
     * 保存合同
     *
     * @param cmd
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.facade.enterprisebill.B2bEnterpriseBillGwExportImpl.saveEnterpriseContract")
    public Response<Boolean> saveEnterpriseContract(B2bEnterpriseContractCmd cmd) {
        return ResponseUtil.buildSuccResponse(b2bEnterpriseAccountApplication.saveEnterpriseContract(cmd));
    }

    /**
     * 删除合同
     *
     * @param cmd
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.facade.enterprisebill.B2bEnterpriseBillGwExportImpl.deleteEnterpriseContract")
    public Response<Boolean> deleteEnterpriseContract(B2bEnterpriseContractCmd cmd) {
        return ResponseUtil.buildSuccResponse(b2bEnterpriseAccountApplication.deleteEnterpriseContract(cmd));
    }

    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.facade.enterprisebill.B2bEnterpriseBillGwExportImpl.queryPageEnterpriseContract")
    public Response<PageDto<B2bEnterpriseAcountContractDto>> queryPageEnterpriseContract(B2bEnterprisePageRequest pageRequest) {
        return ResponseUtil.buildSuccResponse(b2bEnterpriseAccountApplication.queryPageEnterpriseContract(pageRequest));
    }
}
