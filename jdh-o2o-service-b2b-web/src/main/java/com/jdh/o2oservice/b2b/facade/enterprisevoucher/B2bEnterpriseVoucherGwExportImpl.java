package com.jdh.o2oservice.b2b.facade.enterprisevoucher;
import com.jdh.o2oservice.b2b.application.enterprisevoucher.B2bEnterpriseVoucherApplication;
import com.jdh.o2oservice.b2b.application.enterprisevoucher.converter.EnterpriseVoucherAppConvert;
import com.jdh.o2oservice.b2b.base.annotation.EnterpriseDisableCheck;
import com.jdh.o2oservice.b2b.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.b2b.base.annotation.PinB2bErp;
import com.jdh.o2oservice.b2b.base.ducc.DuccConfig;
import com.jdh.o2oservice.b2b.base.exception.BusinessErrorCode;
import com.jdh.o2oservice.b2b.base.exception.BusinessException;
import com.jdh.o2oservice.b2b.base.util.AssertUtils;
import com.jdh.o2oservice.b2b.base.util.ResponseUtil;
import com.jdh.o2oservice.b2b.color.GwMapUtil;
import com.jdh.o2oservice.b2b.common.response.PageDto;
import com.jdh.o2oservice.b2b.common.response.Response;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.PromiseTimelineBo;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.bo.QueryPromiseTimelineBo;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.model.EnterpriseVoucher;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.query.EnterpriseVoucherQuery;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.repository.EnterpriseVoucherRepository;
import com.jdh.o2oservice.b2b.domain.enterprisevoucher.rpc.O2oManServicePromiseRpc;
import com.jdh.o2oservice.b2b.export.enterprisevoucher.B2bEnterpriseVoucherGwExport;
import com.jdh.o2oservice.b2b.export.enterprisevoucher.cmd.InvalidVoucherCmd;
import com.jdh.o2oservice.b2b.export.enterprisevoucher.cmd.ModifyPromiseTimeCmd;
import com.jdh.o2oservice.b2b.export.enterprisevoucher.cmd.SubmitPromiseByUploadCmd;
import com.jdh.o2oservice.b2b.export.enterprisevoucher.cmd.SubmitPromiseCmd;
import com.jdh.o2oservice.b2b.export.enterprisevoucher.dto.*;
import com.jdh.o2oservice.b2b.export.enterprisevoucher.query.*;
import com.jdh.o2oservice.b2b.export.openapi.dto.AngelServiceRecordDTO;
import com.jdh.o2oservice.b2b.export.openapi.dto.EnterpriseVoucherActionLogDto;
import com.jdh.o2oservice.b2b.export.openapi.query.AngelServiceRecordRequest;
import com.jdh.o2oservice.b2b.export.openapi.query.QueryEnterpriseVoucherActionLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * B2bEnterpriseVoucherGwExportImpl
 *
 * <AUTHOR>
 * @date 2025/02/27
 */
@Service
@Slf4j
public class B2bEnterpriseVoucherGwExportImpl implements B2bEnterpriseVoucherGwExport {

    /**
     * b2bEnterpriseVoucherApplication
     */
    @Autowired
    private B2bEnterpriseVoucherApplication b2bEnterpriseVoucherApplication;

    /**
     * EnterpriseVoucherRepository
     */
    @Resource
    private EnterpriseVoucherRepository enterpriseVoucherRepository;

    /**
     * duccConfig
     */
    @Autowired
    private DuccConfig duccConfig;

    @Resource
    O2oManServicePromiseRpc manServicePromiseRpc;

    /**
     * 提交
     *
     * @param param param
     * @return {@link Response }<{@link Boolean }>
     */
    @Override
    @PinB2bErp
    @EnterpriseDisableCheck
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.facade.enterprisevoucher.B2bEnterpriseVoucherGwExportImpl.submitPromise")
    public Response<Boolean> submitPromise(Map<String, String> param) {
        SubmitPromiseCmd submitPromiseCmd = GwMapUtil.convertToParam(param, SubmitPromiseCmd.class);
        return ResponseUtil.buildSuccResponse(b2bEnterpriseVoucherApplication.submitPromise(submitPromiseCmd) > 0);
    }

    /**
     * 可用时间
     *
     * @param param param
     * @return {@link Response }<{@link List }<{@link PromiseAvailableTimeDto }>>
     */
    @Override
    @PinB2bErp
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.facade.enterprisevoucher.B2bEnterpriseVoucherGwExportImpl.queryAvailableTime")
    public Response<List<PromiseAvailableTimeDto>> queryAvailableTime(Map<String,String> param){
        AvailableTimeRequest timeRequest = GwMapUtil.convertToParam(param, AvailableTimeRequest.class);
        return ResponseUtil.buildSuccResponse(b2bEnterpriseVoucherApplication.queryAvailableTime(timeRequest));
    }

    /**
     * 修改预约时间
     *
     * @param param param
     * @return {@link Response }<{@link Boolean }>
     */
    @Override
    @PinB2bErp
    @EnterpriseDisableCheck
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.facade.enterprisevoucher.B2bEnterpriseVoucherGwExportImpl.modifyAppointmentTime")
    public Response<Boolean> modifyAppointmentTime(Map<String, String> param) {
        ModifyPromiseTimeCmd cmd = GwMapUtil.convertToParam(param, ModifyPromiseTimeCmd.class);
        return ResponseUtil.buildSuccResponse(b2bEnterpriseVoucherApplication.modifyAppointmentTime(cmd));
    }

    /**
     * 作废服务
     *
     * @param param param
     * @return {@link Response }<{@link Boolean }>
     */
    @Override
    @PinB2bErp
    @EnterpriseDisableCheck
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.facade.enterprisevoucher.B2bEnterpriseVoucherGwExportImpl.invalidVoucher")
    public Response<Boolean> invalidVoucher(Map<String, String> param) {
        InvalidVoucherCmd cmd = GwMapUtil.convertToParam(param, InvalidVoucherCmd.class);
        return ResponseUtil.buildSuccResponse(b2bEnterpriseVoucherApplication.invalidVoucher(cmd));
    }

    /**
     * 履约单详情
     *
     * @param param param
     * @return {@link Response }<{@link CompletePromiseDetailsDto }>
     */
    @Override
    @PinB2bErp
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.facade.enterprisevoucher.B2bEnterpriseVoucherGwExportImpl.queryPromiseDetail")
    public Response<CompletePromiseDetailsDto> queryPromiseDetail(Map<String, String> param) {
        PromiseDetailRequest request = GwMapUtil.convertToParam(param, PromiseDetailRequest.class);
        return ResponseUtil.buildSuccResponse(b2bEnterpriseVoucherApplication.queryCompletePromiseDetails(request));
    }

    /**
     * 分页查询履约单列表
     *
     * @param param param
     * @return {@link Response }<{@link PageDto }<{@link CompletePromiseDetailsDto }>>
     */
    @Override
    @PinB2bErp
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.facade.enterprisevoucher.B2bEnterpriseVoucherGwExportImpl.queryPromiseDetailPage")
    public Response<PageDto<PromiseDetailsDto>> queryPromiseDetailPage(Map<String, String> param) {
        PromiseDetailPageRequest pageRequest = GwMapUtil.convertToParam(param, PromiseDetailPageRequest.class);
        return ResponseUtil.buildSuccResponse(b2bEnterpriseVoucherApplication.queryPromiseDetailPage(pageRequest));
    }

    @Override
    @PinB2bErp
    @LogAndAlarm
    public Response<Boolean> exportPromiseDetailPage(Map<String, String> param) {
        ExportPromiseDetailPageRequest pageRequest = GwMapUtil.convertToParam(param, ExportPromiseDetailPageRequest.class);
        return ResponseUtil.buildSuccResponse(b2bEnterpriseVoucherApplication.exportPromiseDetailPage(pageRequest));
    }

    /**
     * 查询上传excel的模板url地址
     *
     * @param param
     * @return
     */
    @Override
    @PinB2bErp
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.facade.enterprisevoucher.B2bEnterpriseVoucherGwExportImpl.queryUploadExcelTempUrl")
    public Response<UploadExcelTempUrlDto> queryUploadExcelTempUrl(Map<String, String> param) {
        UploadExcelTempUrlRequest request = GwMapUtil.convertToParam(param, UploadExcelTempUrlRequest.class);
        String uploadTempType = request.getUploadTempType();

        Map<String, String> excelTempUrlMap = duccConfig.getUploadExcelTempUrl();
        if(excelTempUrlMap.containsKey(uploadTempType)){
            String tempUrl = excelTempUrlMap.get(uploadTempType);

            return ResponseUtil.buildSuccResponse(UploadExcelTempUrlDto.builder()
                    .uploadTempType(uploadTempType)
                    .tempUrl(tempUrl)
                    .build());
        }else{
            throw new BusinessException(BusinessErrorCode.URL_TEMPLATE_TYPE_NOT_EXIT);
        }
    }

    /**
     * submitPromiseByUpload
     *
     * @param param
     * @return
     */
    @Override
    @PinB2bErp
    @EnterpriseDisableCheck
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.facade.enterprisevoucher.B2bEnterpriseVoucherGwExportImpl.submitPromiseByUpload")
    public Response<Boolean> submitPromiseByUpload(Map<String, String> param) {
        SubmitPromiseByUploadCmd cmd = GwMapUtil.convertToParam(param, SubmitPromiseByUploadCmd.class);
        return ResponseUtil.buildSuccResponse(b2bEnterpriseVoucherApplication.submitPromiseByUpload(cmd));
    }

    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.facade.enterprisevoucher.B2bEnterpriseVoucherGwExportImpl.queryIntendedAngel")
    @Override
    public Response<IntendedAngelDto> queryIntendedAngel(Map<String, String> param) {
        QueryIntendedAngelRequest request = GwMapUtil.convertToParam(param, QueryIntendedAngelRequest.class);
        return ResponseUtil.buildSuccResponse(b2bEnterpriseVoucherApplication.queryIntendedAngel(request));
    }

    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.facade.enterprisevoucher.B2bEnterpriseVoucherGwExportImpl.syncPromiseStatus")
    @Override
    public Response<Boolean> syncPromiseStatus(Map<String, String> param) {
        SyncPromiseStatusRequest request = GwMapUtil.convertToParam(param, SyncPromiseStatusRequest.class);
        return ResponseUtil.buildSuccResponse(b2bEnterpriseVoucherApplication.syncPromiseStatus(request));
    }

    @LogAndAlarm
    @Override
    public Response<Boolean> syncPromiseStatusTask() {
        return ResponseUtil.buildSuccResponse(b2bEnterpriseVoucherApplication.syncPromiseStatus(null));
    }

    @Override
    @LogAndAlarm
    public Response<Boolean> modifyPromiseOrderPlatform(Map<String, String> param) {
        ModifyPromiseOrderPlatformRequest request = GwMapUtil.convertToParam(param, ModifyPromiseOrderPlatformRequest.class);
        return ResponseUtil.buildSuccResponse(b2bEnterpriseVoucherApplication.modifyEnterpriseVoucher(request));

    }

    @Override
    public Response<Long> queryEnterpriseId(Long enterpriseVoucherId) {
        AssertUtils.nonNull(enterpriseVoucherId, "enterpriseVoucherId is null");
        EnterpriseVoucher enterpriseVoucher = enterpriseVoucherRepository.queryEnterpriseVoucher(EnterpriseVoucherQuery.builder().enterpriseVoucherId(enterpriseVoucherId).build());
        if(Objects.nonNull(enterpriseVoucher)){
            return ResponseUtil.buildSuccResponse(enterpriseVoucher.getEnterpriseId());
        }
        return ResponseUtil.buildErrResponse(BusinessErrorCode.ENTERPRISE_VOUCHER_NO_EXIST);
    }

    /**
     * 查询电子护理单
     *
     * @param param AngelServiceRecordRequest 对象，包含查询条件。
     * @return AngelServiceRecordDTO 列表，表示查询到的电子护理单记录。
     */
    @Override
    @LogAndAlarm
    public Response<AngelServiceRecordDTO> queryBAngelServiceRecordList(Map<String, String> param) {
        AngelServiceRecordRequest request = GwMapUtil.convertToParam(param, AngelServiceRecordRequest.class);
        //鉴权,置换promiseId
        EnterpriseVoucher enterpriseVoucher = enterpriseVoucherRepository.queryEnterpriseVoucher(EnterpriseVoucherQuery.builder().enterpriseId(request.getEnterpriseId()).enterpriseVoucherId(request.getEnterpriseVoucherId()).build());
        if (Objects.isNull(enterpriseVoucher)){
            return null;
        }
        request.setPromiseId(enterpriseVoucher.getPromiseId());

        List<AngelServiceRecordDTO> recordDTOS = b2bEnterpriseVoucherApplication.queryAngelServiceRecordList(request);

        return ResponseUtil.buildSuccResponse(recordDTOS.get(0));
    }

    @Override
    public Response<List<EnterpriseVoucherActionLogDto>> queryEnterpriseVoucherActionLog(Map<String, String> param) {
        QueryEnterpriseVoucherActionLog query = GwMapUtil.convertToParam(param, QueryEnterpriseVoucherActionLog.class);
        EnterpriseVoucher enterpriseVoucher = enterpriseVoucherRepository.queryEnterpriseVoucher(EnterpriseVoucherQuery.builder().enterpriseId(query.getEnterpriseId()).enterpriseVoucherId(query.getEnterpriseVoucherId()).build());
        if (Objects.isNull(enterpriseVoucher)){
            return ResponseUtil.buildErrResponse(BusinessErrorCode.ENTERPRISE_VOUCHER_NO_EXIST);
        }
        QueryPromiseTimelineBo queryPromiseTimelineBo = new QueryPromiseTimelineBo();
        queryPromiseTimelineBo.setPromiseId(enterpriseVoucher.getPromiseId());
        PromiseTimelineBo promiseTimelineBo = manServicePromiseRpc.queryPromiseTimeline(queryPromiseTimelineBo);
        return ResponseUtil.buildSuccResponse(EnterpriseVoucherAppConvert.INS.convertEnterpriseVoucherActionLogDtoList(promiseTimelineBo.getPromiseTimelineDetailBoList()));
    }

}
