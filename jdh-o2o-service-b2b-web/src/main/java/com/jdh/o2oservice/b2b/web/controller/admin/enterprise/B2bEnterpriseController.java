package com.jdh.o2oservice.b2b.web.controller.admin.enterprise;
import com.jd.common.web.LoginContext;
import com.jdh.o2oservice.b2b.application.enterprise.B2bEnterpriseApplication;
import com.jdh.o2oservice.b2b.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.b2b.base.exception.SystemErrorCode;
import com.jdh.o2oservice.b2b.base.util.AssertUtils;
import com.jdh.o2oservice.b2b.base.util.ResponseUtil;
import com.jdh.o2oservice.b2b.common.response.PageDto;
import com.jdh.o2oservice.b2b.common.response.Response;
import com.jdh.o2oservice.b2b.export.enterprise.cmd.CreateB2bEnterpriseCmd;
import com.jdh.o2oservice.b2b.export.enterprise.cmd.UpdateB2bEnterpriseCmd;
import com.jdh.o2oservice.b2b.export.enterprise.cmd.UpdateB2bEnterpriseStatusCmd;
import com.jdh.o2oservice.b2b.export.enterprise.dto.B2bEnterpriseDto;
import com.jdh.o2oservice.b2b.export.enterprise.query.B2bEnterprisePageRequest;
import com.jdh.o2oservice.b2b.export.enterprise.query.B2bEnterpriseRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @Description 企业信息
 * @Date 2025/2/24 下午8:07
 * <AUTHOR>
 **/
@Slf4j
@RestController
@RequestMapping("/b2b/enterprise")
public class B2bEnterpriseController {

    @Resource
    private B2bEnterpriseApplication b2bEnterpriseApplication;

    /**
     * 创建企业
     * @param cmd
     * @return
     */
    @PostMapping(value = "/createEnterprise")
    @LogAndAlarm(jKey = "B2bEnterpriseController.createEnterprise")
    public Response<Boolean> createEnterprise(@RequestBody CreateB2bEnterpriseCmd cmd){
        AssertUtils.nonNull(cmd, SystemErrorCode.PARAM_NULL_ERROR);
        String pin = LoginContext.getLoginContext().getPin();
        cmd.setOperator(pin);
        Boolean result = b2bEnterpriseApplication.createEnterprise(cmd);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 修改企业
     * @param cmd
     * @return
     */
    @PostMapping(value = "/updateEnterprise")
    @LogAndAlarm(jKey = "B2bEnterpriseController.updateEnterprise")
    public Response<Boolean> updateEnterprise(@RequestBody UpdateB2bEnterpriseCmd cmd){
        AssertUtils.nonNull(cmd, SystemErrorCode.PARAM_NULL_ERROR);
        String pin = LoginContext.getLoginContext().getPin();
        cmd.setOperator(pin);
        Boolean result = b2bEnterpriseApplication.updateEnterprise(cmd);
        return ResponseUtil.buildSuccResponse(result);
    }


    /**
     * 修改企业状态
     * @param cmd
     * @return
     */
    @PostMapping(value = "/updateEnterpriseStatus")
    @LogAndAlarm(jKey = "B2bEnterpriseController.updateEnterpriseStatus")
    public Response<Boolean> updateEnterpriseStatus(@RequestBody UpdateB2bEnterpriseStatusCmd cmd){
        AssertUtils.nonNull(cmd, SystemErrorCode.PARAM_NULL_ERROR);
        String pin = LoginContext.getLoginContext().getPin();
        cmd.setOperator(pin);
        Boolean result = b2bEnterpriseApplication.updateEnterpriseStatus(cmd);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 查询企业
     * @param request
     * @return
     */
    @PostMapping(value = "/queryEnterprise")
    @LogAndAlarm(jKey = "B2bEnterpriseController.queryEnterprise")
    public Response<B2bEnterpriseDto> queryEnterprise(@RequestBody B2bEnterpriseRequest request){
        B2bEnterpriseDto result = b2bEnterpriseApplication.queryEnterprise(request);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 分页查询企业
     * @param request
     * @return
     */
    @PostMapping(value = "/queryPageEnterprise")
    @LogAndAlarm(jKey = "B2bEnterpriseController.queryPageEnterprise")
    public Response<PageDto<B2bEnterpriseDto>> queryPageEnterprise(@RequestBody B2bEnterprisePageRequest request){
        PageDto<B2bEnterpriseDto> result = b2bEnterpriseApplication.queryPageEnterprise(request);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 企业代管添加cookie
     * 预发：https://jdh-o2oservice-b2b-beta.jd.com/b2b/enterprise/enterpriseEscrowAddCookie?enterpriseId=企业id
     * @param req
     * @param res
     */
    @RequestMapping(value = "/enterpriseEscrowAddCookie")
    public void enterpriseEscrowAddCookie(HttpServletRequest req, HttpServletResponse res) throws Exception {
        b2bEnterpriseApplication.enterpriseEscrowAddCookie(req, res);
    }

    /**
     * 企业代管移除cookie
     * 预发：https://jdh-o2oservice-b2b-beta.jd.com/b2b/enterprise/enterpriseEscrowRemoveCookie?enterpriseId=企业id
     * @param req
     * @param res
     */
    @RequestMapping(value = "/enterpriseEscrowRemoveCookie")
    public void enterpriseEscrowRemoveCookie(HttpServletRequest req, HttpServletResponse res) throws Exception {
        b2bEnterpriseApplication.enterpriseEscrowRemoveCookie(req, res);
    }

}
