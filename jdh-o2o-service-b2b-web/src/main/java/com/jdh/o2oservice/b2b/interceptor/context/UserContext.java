package com.jdh.o2oservice.b2b.interceptor.context;

import com.jd.common.web.LoginContext;
import com.jdh.o2oservice.export.auth.BizCodeDto;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: duanqiaona1
 * @Date: 2024/4/25 9:14 上午
 * @Description:
 */
@Data
public class UserContext implements Serializable {
    private static final long serialVersionUID = 2085326312315104681L;

    /**
     * 登录信息上下文
     */
    private LoginContext loginContext;

    /**
     * 权限信息上下文
     */
    private List<BizCodeDto> roleListContext;

    /**
     *  获取业务代码VO列表
     *
     * @return {@link List}<{@link BizCodeVo}>
     */
    private List<BizCodeVo> bizCodeVoList;
}
