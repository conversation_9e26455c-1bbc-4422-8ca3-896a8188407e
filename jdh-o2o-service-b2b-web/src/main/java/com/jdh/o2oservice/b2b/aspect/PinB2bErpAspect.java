package com.jdh.o2oservice.b2b.aspect;
import cn.hutool.core.util.BooleanUtil;
import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.b2b.base.annotation.PinB2bErp;
import com.jdh.o2oservice.b2b.base.constant.NumConstant;
import com.jdh.o2oservice.b2b.base.exception.BusinessErrorCode;
import com.jdh.o2oservice.b2b.base.exception.BusinessException;
import com.jdh.o2oservice.b2b.base.exception.SystemErrorCode;
import com.jdh.o2oservice.b2b.base.util.ResponseUtil;
import com.jdh.o2oservice.b2b.base.util.UserPinContext;
import com.jdh.o2oservice.b2b.color.ColorBizParam;
import com.jdh.o2oservice.b2b.color.GwMapUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Map;
import java.util.Objects;

;

/**
 * 用户校验切面
 */
@Slf4j
@Aspect
@Component
public class PinB2bErpAspect {

    /**
     * 拦截的注解
     */
    @Pointcut("@annotation(com.jdh.o2oservice.b2b.base.annotation.PinB2bErp)")
    public void pointCut() {
    }


    /**
     * 用户操作校验
     *
     * @param proceedingJoinPoint 切点
     * @return Object
     * @throws Throwable 异常
     */
    @Around("pointCut()")
    public Object aroundOperation(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        Object result = null;
        try {
            // 获取参数
            Object[] args = proceedingJoinPoint.getArgs();
            Signature signature = proceedingJoinPoint.getSignature();
            Class<?> cls = proceedingJoinPoint.getTarget().getClass();
            String methodName = signature.getName();
            Method method;
            if (signature instanceof MethodSignature) {
                method = ((MethodSignature) signature).getMethod();
            } else {
                return null;
            }

            // 实际调用方法,避免spring事务AOP无法获取实际方法
            Method realMethod = cls.getDeclaredMethod(methodName, method.getParameterTypes());

            // 校验pin,如果userPin存在会存入ThreadLocal中
            Object userPinCheck = pinCheck(cls, realMethod, args);
            //不为null说明校验pin没通过
            if (userPinCheck != null) {
                return userPinCheck;
            }
            result = proceedingJoinPoint.proceed();
        } finally {
            // 执行结束,清除ThreadLocal中pin
            UserPinContext.remove();
        }
        return result;
    }

    /**
     * userPin校验
     *
     * @param cls    类
     * @param method 方法
     * @param args   参数数组
     * @return Object
     */
    private Object pinCheck(Class<?> cls, Method method, Object[] args) {
        try {
            String pin = null;
            String cookie = null;
            // rpc接口统一使用的Map作为接收参数,校验参数个数、参数类型
            if (ArrayUtils.isNotEmpty(args) && args.length == 1 && args[0] instanceof Map) {
                pin = GwMapUtil.getPin((Map<?, ?>) args[0]);
                log.info("PinB2bErpAspect pinCheck pin={}", pin);
                cookie = GwMapUtil.getCookie((Map<?, ?>) args[0]);
                log.info("PinB2bErpAspect pinCheck cookie={}", cookie);
                ColorBizParam colorBizParam = GwMapUtil.buildColorBizParam(pin, cookie);
                log.info("PinB2bErpAspect pinCheck colorBizParam={}", JSON.toJSONString(colorBizParam));
                // 非代管模式且没有登录pin
                if (BooleanUtil.isFalse(colorBizParam.getEscrowMode()) && StringUtils.isBlank(colorBizParam.getPin())){
                    log.info("PinB2bErpAspect pinCheck 非代管模式且没有登录pin");
                    return ResponseUtil.buildErrResponse(BusinessErrorCode.UNKNOWN_USER_LOGIN);
                }
                // 非代管模式，被禁用的用户无法登录
                if (BooleanUtil.isFalse(colorBizParam.getEscrowMode()) && Objects.nonNull(colorBizParam.getEnterpriseUserAvailable())
                        && NumConstant.NUM_0.equals(colorBizParam.getEnterpriseUserAvailable())){
                    log.info("PinB2bErpAspect pinCheck 企业用户禁用，您没有访问权限");
                    return ResponseUtil.buildErrResponse(BusinessErrorCode.ENTERPRISE_USER_DISABLE);
                }
                if (StringUtils.isNotBlank(colorBizParam.getPin())){
                    // 将pin放入ThreadLocal
                    UserPinContext.put(pin);
                }
            }
            return null;
        } catch (BusinessException be) {
            log.error("PinB2bErpAspect -> pinCheck, biz error",be);
            return ResponseUtil.buildErrResponse(be.getErrorCode());
        } catch (Exception e) {
            log.error("PinB2bErpAspect -> pinCheck, error",e);
            return ResponseUtil.buildErrResponse(SystemErrorCode.UNKNOWN_ERROR);
        }
    }


}