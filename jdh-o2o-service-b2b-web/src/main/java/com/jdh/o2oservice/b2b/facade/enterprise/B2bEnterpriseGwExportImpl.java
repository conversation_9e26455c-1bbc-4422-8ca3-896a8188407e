package com.jdh.o2oservice.b2b.facade.enterprise;
import com.jdh.o2oservice.b2b.application.enterprise.B2bEnterpriseApplication;
import com.jdh.o2oservice.b2b.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.b2b.base.annotation.PinB2bErp;
import com.jdh.o2oservice.b2b.base.util.AssertUtils;
import com.jdh.o2oservice.b2b.base.util.ResponseUtil;
import com.jdh.o2oservice.b2b.color.GwMapUtil;
import com.jdh.o2oservice.b2b.common.response.Response;
import com.jdh.o2oservice.b2b.domain.enterprisebill.enums.EnterpriseBillErrorCode;
import com.jdh.o2oservice.b2b.export.enterprise.B2bEnterpriseGwExport;
import com.jdh.o2oservice.b2b.export.enterprise.dto.B2bEnterpriseDto;
import com.jdh.o2oservice.b2b.export.enterprise.dto.B2bEnterpriseEscrowDto;
import com.jdh.o2oservice.b2b.export.enterprise.dto.B2bEnterpriseRelInfoDto;
import com.jdh.o2oservice.b2b.export.enterprise.query.B2bEnterpriseEscrowRequest;
import com.jdh.o2oservice.b2b.export.enterprise.query.B2bEnterpriseRequest;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.Map;

/**
 * @Description 企业信息服务
 * @Date 2025/2/24 下午3:25
 * <AUTHOR>
 **/
@Service
public class B2bEnterpriseGwExportImpl implements B2bEnterpriseGwExport {

    @Resource
    private B2bEnterpriseApplication b2bEnterpriseApplication;

    /**
     * 查询企业关联信息
     * @param param
     * @return
     */
    @Override
    @PinB2bErp
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.facade.enterprise.B2bEnterpriseGwExportImpl.queryEnterpriseRelInfo")
    public Response<B2bEnterpriseRelInfoDto> queryEnterpriseRelInfo(Map<String, String> param) {
        B2bEnterpriseRequest request = GwMapUtil.convertToParam(param, B2bEnterpriseRequest.class);
        return ResponseUtil.buildSuccResponse(b2bEnterpriseApplication.queryEnterpriseRelInfo(request));
    }

    /**
     * 查询企业
     *
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.facade.enterprise.B2bEnterpriseGwExportImpl.queryEnterprise")
    public Response<B2bEnterpriseDto> queryEnterprise(B2bEnterpriseRequest request) {
        AssertUtils.nonNull(request.getEnterpriseId(), EnterpriseBillErrorCode.ENTERPRISE_ID_NULL);
        return ResponseUtil.buildSuccResponse(b2bEnterpriseApplication.queryEnterprise(request));
    }

    /**
     * 查询企业
     *
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.facade.enterprise.B2bEnterpriseGwExportImpl.queryEnterpriseByPromiseId")
    public Response<B2bEnterpriseDto> queryEnterpriseByPromiseId(B2bEnterpriseRequest request) {
        return ResponseUtil.buildSuccResponse(b2bEnterpriseApplication.queryEnterpriseByPromiseId(request));
    }

    /**
     * 企业代管
     * @param param
     * @return
     */
    @Override
    @PinB2bErp
    @LogAndAlarm(jKey = "com.jdh.o2oservice.b2b.facade.enterprise.B2bEnterpriseGwExportImpl.enterpriseEscrow")
    public Response<B2bEnterpriseEscrowDto> enterpriseEscrow(Map<String, String> param) {
        B2bEnterpriseEscrowRequest request = GwMapUtil.convertToParam(param, B2bEnterpriseEscrowRequest.class);
        return ResponseUtil.buildSuccResponse(b2bEnterpriseApplication.enterpriseEscrow(request));
    }
}
