package com.jdh.o2oservice.b2b.web.controller.admin.enterpriseaccount;

import com.jd.common.web.LoginContext;
import com.jdh.o2oservice.b2b.application.enterpriseaccount.B2bEnterpriseAccountApplication;
import com.jdh.o2oservice.b2b.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.b2b.base.exception.SystemErrorCode;
import com.jdh.o2oservice.b2b.base.util.AssertUtils;
import com.jdh.o2oservice.b2b.base.util.ResponseUtil;
import com.jdh.o2oservice.b2b.common.response.PageDto;
import com.jdh.o2oservice.b2b.common.response.Response;
import com.jdh.o2oservice.b2b.export.enterprise.cmd.B2bEnterpriseContractCmd;
import com.jdh.o2oservice.b2b.export.enterprise.query.B2bEnterprisePageRequest;
import com.jdh.o2oservice.b2b.export.enterprise.query.B2bEnterpriseRequest;
import com.jdh.o2oservice.b2b.export.enterpriseaccount.dto.B2bEnterpriseAcountContractDto;
import com.jdh.o2oservice.b2b.export.enterpriseaccount.dto.B2bEnterpriseContractDetailDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @Description 企业合同信息
 * @Date 2025/5/7 10:07
 * <AUTHOR>
 **/
@Slf4j
@RestController
@RequestMapping("/b2b/contract")
public class B2bEnterpriseContractController {

    @Resource
    private B2bEnterpriseAccountApplication b2bEnterpriseAccountApplication;

    /**
     * 创建企业合同
     * @param cmd
     * @return
     */
    @PostMapping(value = "/createContract")
    @LogAndAlarm(jKey = "B2bEnterpriseContractController.createContract")
    public Response<Boolean> createContract(@Validated @RequestBody B2bEnterpriseContractCmd cmd){
        AssertUtils.nonNull(cmd, SystemErrorCode.PARAM_NULL_ERROR);
        String pin = LoginContext.getLoginContext().getPin();
        cmd.setOperator(pin);
        Boolean result = b2bEnterpriseAccountApplication.saveEnterpriseContract(cmd);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 删除合同
     * @param cmd
     * @return
     */
    @PostMapping(value = "/deleteContract")
    @LogAndAlarm(jKey = "B2bEnterpriseContractController.deleteContract")
    public Response<Boolean> deleteContract(@Validated @RequestBody B2bEnterpriseContractCmd cmd){
        AssertUtils.nonNull(cmd, SystemErrorCode.PARAM_NULL_ERROR);
        String pin = LoginContext.getLoginContext().getPin();
        cmd.setOperator(pin);
        Boolean result = b2bEnterpriseAccountApplication.deleteEnterpriseContract(cmd);
        return ResponseUtil.buildSuccResponse(result);
    }



    /**
     * 查询企业合同
     * @param b2bEnterpriseRequest
     * @return
     */
    @PostMapping(value = "/queryContract")
    @LogAndAlarm(jKey = "B2bEnterpriseContractController.queryContract")
    public Response<B2bEnterpriseContractDetailDto> queryContract(@RequestBody B2bEnterpriseRequest b2bEnterpriseRequest){
        B2bEnterpriseContractDetailDto result = b2bEnterpriseAccountApplication.queryAccountInfoByContractNum(b2bEnterpriseRequest);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 分页查询企业合同列表
     * @param request
     * @return
     */
    @PostMapping(value = "/queryPageContract")
    @LogAndAlarm(jKey = "B2bEnterpriseContractController.queryPageContract")
    public Response<PageDto<B2bEnterpriseAcountContractDto>> queryPageContract(@RequestBody B2bEnterprisePageRequest request){
        PageDto<B2bEnterpriseAcountContractDto> result = b2bEnterpriseAccountApplication.queryPageEnterpriseContract(request);
        return ResponseUtil.buildSuccResponse(result);
    }

}
