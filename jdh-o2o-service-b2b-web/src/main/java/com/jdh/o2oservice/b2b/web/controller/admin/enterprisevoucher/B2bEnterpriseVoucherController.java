package com.jdh.o2oservice.b2b.web.controller.admin.enterprisevoucher;
import com.jd.common.web.LoginContext;
import com.jdh.o2oservice.b2b.application.enterprisevoucher.B2bEnterpriseVoucherApplication;
import com.jdh.o2oservice.b2b.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.b2b.base.exception.SystemErrorCode;
import com.jdh.o2oservice.b2b.base.util.AssertUtils;
import com.jdh.o2oservice.b2b.base.util.ResponseUtil;
import com.jdh.o2oservice.b2b.common.response.PageDto;
import com.jdh.o2oservice.b2b.common.response.Response;
import com.jdh.o2oservice.b2b.export.enterprisevoucher.cmd.AfreshDispatchCmd;
import com.jdh.o2oservice.b2b.export.enterprisevoucher.cmd.AppointDispatchCmd;
import com.jdh.o2oservice.b2b.export.enterprisevoucher.cmd.InvalidVoucherCmd;
import com.jdh.o2oservice.b2b.export.enterprisevoucher.cmd.ModifyPromiseTimeCmd;
import com.jdh.o2oservice.b2b.export.enterprisevoucher.dto.*;
import com.jdh.o2oservice.b2b.export.enterprisevoucher.query.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.List;

/**
 * @Description 企业服务单
 * @Date 2025/2/26 下午2:34
 * <AUTHOR>
 **/
@Slf4j
@RestController
@RequestMapping("/b2b/enterpriseVoucher")
public class B2bEnterpriseVoucherController {

    @Resource
    private B2bEnterpriseVoucherApplication b2bEnterpriseVoucherApplication;


    /**
     * 分页查询履约单
     * @param request
     * @return
     */
    @PostMapping(value = "/queryPromiseDetailPage")
    @LogAndAlarm(jKey = "B2bEnterpriseVoucherController.queryPromiseDetailPage")
    public Response<PageDto<PromiseDetailsDto>> queryPromiseDetailPage(@RequestBody PromiseDetailPageRequest request){
        PageDto<PromiseDetailsDto> result = b2bEnterpriseVoucherApplication.queryPromiseDetailPage(request);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 查询履约单详情
     * @param request
     * @return
     */
    @PostMapping(value = "/queryCompletePromiseDetails")
    @LogAndAlarm(jKey = "B2bEnterpriseVoucherController.queryCompletePromiseDetails")
    public Response<CompletePromiseDetailsDto> queryCompletePromiseDetails(@RequestBody PromiseDetailRequest request){
        CompletePromiseDetailsDto result = b2bEnterpriseVoucherApplication.queryCompletePromiseDetails(request);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 查询可用时间
     * @param request
     * @return
     */
    @PostMapping(value = "/queryAvailableTime")
    @LogAndAlarm(jKey = "B2bEnterpriseVoucherController.queryAvailableTime")
    public Response<List<PromiseAvailableTimeDto>> queryAvailableTime(@RequestBody AvailableTimeRequest request){
        List<PromiseAvailableTimeDto> result = b2bEnterpriseVoucherApplication.queryAvailableTime(request);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 修改预约时间
     * @param cmd
     * @return
     */
    @PostMapping(value = "/modifyAppointmentTime")
    @LogAndAlarm(jKey = "B2bEnterpriseVoucherController.modifyAppointmentTime")
    public Response<Boolean> modifyAppointmentTime(@RequestBody ModifyPromiseTimeCmd cmd){
        AssertUtils.nonNull(cmd, SystemErrorCode.PARAM_NULL_ERROR);
        String pin = LoginContext.getLoginContext().getPin();
        cmd.setOperator(pin);
        Boolean result = b2bEnterpriseVoucherApplication.modifyAppointmentTime(cmd);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 作废服务单
     * @param cmd
     * @return
     */
    @PostMapping(value = "/invalidVoucher")
    @LogAndAlarm(jKey = "B2bEnterpriseVoucherController.invalidVoucher")
    public Response<Boolean> invalidVoucher(@RequestBody InvalidVoucherCmd cmd){
        AssertUtils.nonNull(cmd, SystemErrorCode.PARAM_NULL_ERROR);
        String pin = LoginContext.getLoginContext().getPin();
        cmd.setOperator(pin);
        Boolean result = b2bEnterpriseVoucherApplication.invalidVoucher(cmd);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 查询录音
     * @param request
     * @return
     */
    @PostMapping(value = "/queryCallRecordList")
    @LogAndAlarm(jKey = "B2bEnterpriseVoucherController.queryCallRecordList")
    public Response<List<PromiseCallRecordDto>> queryCallRecordList(@RequestBody RecordingRequest request){
        List<PromiseCallRecordDto> result = b2bEnterpriseVoucherApplication.queryCallRecordList(request);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 查询录音链接
     * @param request
     * @return
     */
    @PostMapping(value = "/queryCallRecordUrl")
    @LogAndAlarm(jKey = "B2bEnterpriseVoucherController.queryCallRecordUrl")
    public Response<String> queryCallRecordUrl(@RequestBody RecordingRequest request){
        String result = b2bEnterpriseVoucherApplication.queryCallRecordUrl(request);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 查询服务记录
     * @param request
     * @return
     */
    @PostMapping(value = "/queryServiceRecord")
    @LogAndAlarm(jKey = "B2bEnterpriseVoucherController.queryServiceRecord")
    public Response<PromiseServiceRecordDto> queryServiceRecord(@RequestBody ServiceRecordRequest request){
        PromiseServiceRecordDto result = b2bEnterpriseVoucherApplication.queryServiceRecord(request);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 重新派单
     * @param cmd
     * @return
     */
    @PostMapping(value = "/reDispatch")
    @LogAndAlarm(jKey = "B2bEnterpriseVoucherController.reDispatch")
    public Response<Boolean> reDispatch(@RequestBody AfreshDispatchCmd cmd){
        AssertUtils.nonNull(cmd, SystemErrorCode.PARAM_NULL_ERROR);
        String pin = LoginContext.getLoginContext().getPin();
        cmd.setOperator(pin);
        Boolean result = b2bEnterpriseVoucherApplication.reDispatch(cmd);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 查询服务者详情
     * @param request
     * @return
     */
    @PostMapping(value = "/queryAngelDetail")
    @LogAndAlarm(jKey = "B2bEnterpriseVoucherController.queryAngelDetail")
    public Response<PromiseAngelDetailDto> queryAngelDetail(@RequestBody AngelDetailRequest request){
        PromiseAngelDetailDto result = b2bEnterpriseVoucherApplication.queryAngelDetail(request);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 定向派单
     * @param cmd
     * @return
     */
    @PostMapping(value = "/targetDispatch")
    @LogAndAlarm(jKey = "B2bEnterpriseVoucherController.targetDispatch")
    public Response<Boolean> targetDispatch(@RequestBody AppointDispatchCmd cmd){
        AssertUtils.nonNull(cmd, SystemErrorCode.PARAM_NULL_ERROR);
        String pin = LoginContext.getLoginContext().getPin();
        cmd.setOperator(pin);
        Boolean result = b2bEnterpriseVoucherApplication.targetDispatch(cmd);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 查询派单明细
     * @param request
     * @return
     */
    @PostMapping(value = "/queryDispatchDetailList")
    @LogAndAlarm(jKey = "B2bEnterpriseVoucherController.queryDispatchDetailList")
    public Response<PageDto<PromiseDispatchDetailDto>> queryDispatchDetailList(@RequestBody DispatchDetailRequest request){
        PageDto<PromiseDispatchDetailDto> result = b2bEnterpriseVoucherApplication.queryDispatchDetailList(request);
        return ResponseUtil.buildSuccResponse(result);
    }

}
