server:
  port: 80
  servlet:
    context-path: /api
spring:
  profiles:
    active: production
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
    default-property-inclusion: non_null
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  servlet:
    # 应用的访问路径
    context-path: /
  devtools:
    restart:
      enabled: true
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
  ## jmq
  # https://joyspace.jd.com/pages/W1Rw2L8996bu5FsPgUta
  # dev为测试站，可以本地直接链接 http://test.taishan.jd.com/jmq/application
  # 集群接入地址：https://joyspace.jd.com/pages/OCNOfPvDOzYzOzim8HBd
  jmq:
    enabled: true
    producers:
      reachStoreProducer:
        password: d0af9c1e556746d39334e9cfece1e723
        app: jdho2oservice
        address: nameserver.jmq.jd.local:80
        enabled: true
topics:
  jdhReachStoreConsumer:
    locCodeSendTopic: loc_code_sync

# mybatisPlus
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  typeAliasesPackage: com.jdh.o2oservice.b2b.infrastructure.repository.db.po
  mapper-locations: classpath:sqlmap/*.xml

# redis 配置
redis:
  jimUrl: jim://3452526826096240630/28508
  endpoint: http://cfs.jim.jd.local

# forest配置
forest:
  backend: okhttp3
  max-retry-count: 3 # 请求失败后重试次数，默认为0次不重试
  max-retry-interval: 5000 #重试间隔时间
  connect-timeout: 5000 #链接超时时间
  timeout: 5000  # 请求超时时间
  ## 日志总开关，打开/关闭Forest请求/响应日志（默认为 true）
  log-enabled: true
  ## 打开/关闭Forest请求日志（默认为 true）
  log-request: true
  ## 打开/关闭Forest响应状态日志（默认为 true）
  log-response-status: true
  ## 打开/关闭Forest响应内容日志（默认为 false）
  log-response-content: true

# aces
tde:
  enableGM: false
  isProd: true
  rPath: ''
  token: eyJzaWciOiJUYkh6ZHJOWjUyay9Gc0pkSC9hc3UxNmlTY3hZVk9sYjZPNDc4ODVZVUFJZUxjV0hhTVA0OGZ5ZTZtYWdwaGl1cUNIc3pOdnRRcjFHUTdqdUYrRkFiREpWOEF4dk1ORndNNStGanFxVkZZNmlOZlJzNUduSUxFNGZFdUw3eDRTQ2ZjTnpTYUNzSXNEWE5VZnpGYWNvdVJhUnE1dnpZVm54RkhXQmI3RldaN2t1UGovSUcvZWo1VW5WS2g0RlMzQUQ3U0loZUVDQ0ZqN3FTUUJtSFNtRTdONEZkdytTbmJORlNyZTJzbHRTV1poMmgwRTVUMzhkbzI3N0RZWktBZkJuR0dMejlsNXBlS01qZHNBQ0pwQm80b3VwWWlwNzJGM3ZzMHUxL1UwOUp2T3ZuOU13UDc3d1V0ZytMS1pQcDc0eWNEYXVwdExRMHQyTzNJZWtqaGFLQ3c9PSIsImRhdGEiOnsiYWN0IjoiY3JlYXRlIiwiZWZmZWN0aXZlIjoxNjY4OTYwMDAwMDAwLCJleHBpcmVkIjoxNzMyMTE4NDAwMDAwLCJpZCI6Ik1qaGlPR1F6TXpRdFl6YzVZeTAwWkdJeExXRmxaall0WVRNNU0yUTNNMkZqT1dVMSIsImtleSI6ImxGKzNXVE5seTBIK2hNU2JGaGFoN21UVUU2QURKU3NJSXBCQXRTNnI5UVE9Iiwic2VydmljZSI6InBoeXNpY2FsZXhhbWluYXRpb25fRGpXdEp3Ym8iLCJzdHlwZSI6MX0sImV4dGVybmFsRGF0YSI6eyJ6b25lIjoiQ04tMCJ9fQ==

# oss
oss:
  accessKey: JDC_96582803E0CF4A86AE2D5B168D28
  secretKey: BF9DB00B1F82F1F2721F492DE17230DE
  internalEndPoint: s3-internal.cn-north-1.jdcloud-oss.com
  publicEndPoint: s3.cn-north-1.jdcloud-oss.com
  region: cn-north-1
  connectionTimeout: 30000
  defaultBucket: o2o-service

# ES配置类
elasticsearchfactory:
  clusterName: jiesi-jdos-home-service
  nodeConnectionAddress: prod-3-40000-jiesi-jdos-home-service.jd.local:40100;prod-2-40000-jiesi-jdos-home-service.jd.local:40100;prod-1-40000-jiesi-jdos-home-service.jd.local:40100;
  securityUser: jiesi-jdos-home-service
  securityPassword: 80949DF4A91357A8

# ducc
laf:
  config:
    logger:
      enabled: true
      type: logback
      key: logger.level
    manager:
      application: jdos_jdh-o2o-service-b2b
      namespace: jdh_o2o_service_b2b
      parameters:
        - name: autoListener
          value: true
      profile: production
      resources:
        - name: common
          uri: ucc://${laf.config.manager.application}:${laf.config.manager.token}@ducc.jd.local/v1/namespace/${laf.config.manager.namespace}/config/common/profiles/${laf.config.manager.profile}?longPolling=60000&necessary=false
      token: 01ea1a456e184960a7e3fa12b41247e4
      baseUrl: http://ducc-api.jd.local

# elasticjob
elasticjob:
  regCenter:
    #zookeeper 的ip:port
    serverLists: jkfwzk.zk.jddb.com:3525
    #名命空间，自己定义就好了
    namespace: jdh-o2o-service-b2b/yfb
    digest: xfyl1024:wBNrsia8BQTD
  jobs:
    # 服务单过期任务
    jdhVoucherExpireJob:
      elasticJobClass: com.jdh.o2oservice.b2b.job.DemoJob
      cron: 0 10 0 * * ?
      shardingTotalCount: 1
      overwrite: true


# sso
# https://cf.jd.com/pages/viewpage.action?pageId=748451623
sso:
  clientId: "jdh-o2o-service"
  clientSecret: "7ec95cfccb0643e79253171e88d24f6f"
  excludePath: "/product/route,/provider/ship,/tools"
#  ajaxCallbackPolicy: "Referer"
#  endpoint: "https://ssa.jd.com"
#  apiEndpoint: "http://ssa.jd.local"


# uim权限系统配置  http://uim2.jd.com/system/info?systemCode=xqCV5Q2ZUaAS4D4S
uim:
  appKey: 78701b16c7bf4eada5aa048649217888
  appToken: 70c5b895e5614178a8e3c79bb208a49c
  tenantCode: CN.JD.GROUP
  dimResCode: belongBizCodeDataSource

#达达配置
dada:
  base:
    url: http://openplatform.imdada.local
  sourceId: 715670
  appKey: dada6b3e2cf3789bdc6
  appSecret: bb78f4c8415e03819fdf98c81d485c8d

#easyJob配置
jdd:
  easyjob:
    enable: true
    host: http://schedule.jdfin.local
    appId: jdh-o2o-service
    secret: 8b805c6c75d34a46cfb8b1c71b510b64

# 统一操作日志系统分配的appName
opLog:
  appName: jdh-o2o-service-b2b