<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:jsf="http://jsf.jd.com/schema/jsf"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
    http://jsf.jd.com/schema/jsf http://jsf.jd.com/schema/jsf/jsf.xsd" default-autowire="byName">

    <jsf:server id="jsf" protocol="jsf" threads="1000" threadpool="cached" queuetype="normal" queues="0"/>

    <!-- 企业信息服务 -->
    <jsf:provider id="b2bEnterpriseGwExportJsf"
                  interface="com.jdh.o2oservice.b2b.export.enterprise.B2bEnterpriseGwExport"
                  alias="${common.provider.alias}"
                  ref="b2bEnterpriseGwExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 企业服务单 -->
    <jsf:provider id="b2bEnterpriseVoucherGwExport"
                  interface="com.jdh.o2oservice.b2b.export.enterprisevoucher.B2bEnterpriseVoucherGwExport"
                  alias="${common.provider.alias}"
                  ref="b2bEnterpriseVoucherGwExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 企业账单 -->
    <jsf:provider id="b2bEnterpriseBillGwExport"
                  interface="com.jdh.o2oservice.b2b.export.enterprisebill.B2bEnterpriseBillGwExport"
                  alias="${common.provider.alias}"
                  ref="b2bEnterpriseBillGwExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 企业账户 -->
    <jsf:provider id="b2bEnterpriseAccountGwExport"
                  interface="com.jdh.o2oservice.b2b.export.enterpriseaccount.B2bEnterpriseAccountGwExport"
                  alias="${common.provider.alias}"
                  ref="b2bEnterpriseAccountGwExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 操作日志 -->
    <jsf:provider id="operationLogGwExport"
                  interface="com.jdh.o2oservice.b2b.export.operationLog.OperationLogGwExport"
                  alias="${common.provider.alias}"
                  ref="operationLogGwExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 企业备注 -->
    <jsf:provider id="b2bEnterpriseRemarkGwExport"
                  interface="com.jdh.o2oservice.b2b.export.enterpriseremark.B2bEnterpriseRemarkGwExport"
                  alias="${common.provider.alias}"
                  ref="b2bEnterpriseRemarkGwExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 外部平台 -->
    <jsf:provider id="orderPlatformGwExport"
                  interface="com.jdh.o2oservice.b2b.export.orderplatform.OrderPlatformGwExport"
                  alias="${common.provider.alias}"
                  ref="orderPlatformGwExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 外部平台 -->
    <jsf:provider id="b2bEnterpriseSkuGwExport"
                  interface="com.jdh.o2oservice.b2b.export.enterprisesku.B2bEnterpriseSkuGwExport"
                  alias="${common.provider.alias}"
                  ref="b2bEnterpriseSkuGwExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>



    <jsf:provider id="b2bEnterpriseOpenAPIJsfExport"
                  interface="com.jdh.o2oservice.b2b.export.openapi.B2bEnterpriseOpenAPIJsfExport"
                  alias="${common.provider.alias}"
                  ref="b2bEnterpriseOpenAPIJsfExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>



</beans>