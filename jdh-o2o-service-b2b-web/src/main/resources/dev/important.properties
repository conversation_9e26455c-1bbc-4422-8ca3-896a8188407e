

spring.datasource.dynamic.primary=master
spring.datasource.dynamic.druid=master
spring.datasource.dynamic.druid.initialSize=5
spring.datasource.dynamic.druid.minIdle=10
spring.datasource.dynamic.druid.maxActive=20
spring.datasource.dynamic.druid.maxWait=60000
spring.datasource.dynamic.druid.timeBetweenEvictionRunsMillis=60000
spring.datasource.dynamic.druid.minEvictableIdleTimeMillis=300000
spring.datasource.dynamic.druid.maxEvictableIdleTimeMillis=900000
spring.datasource.dynamic.druid.validationQuery=SELECT 1 FROM DUAL
spring.datasource.dynamic.druid.testWhileIdle=true
spring.datasource.dynamic.druid.testOnBorrow=false
spring.datasource.dynamic.druid.testOnReturn=false
spring.datasource.dynamic.druid.filters=slf4j,wall
spring.datasource.dynamic.druid.filter.wall.config.multi-statement-allow=true
spring.datasource.dynamic.druid.slf4j.connectionLogEnabled=true
spring.datasource.dynamic.druid.slf4j.statementLogEnabled=true
spring.datasource.dynamic.druid.slf4j.resultSetLogEnabled=false
druid.log.rs=false


## \u5C65\u7EA6\u4E2D\u53F0\u6570\u636E\u5E93\u914D\u7F6E
spring.datasource.dynamic.datasource.master.driverClassName=com.mysql.cj.jdbc.Driver
spring.datasource.dynamic.datasource.master.url=************************************************************************************************************************************************************************
spring.datasource.dynamic.datasource.master.username=jdh_reach_store_rw
spring.datasource.dynamic.datasource.master.password=mgMGXm8xitzaVlWL


## \u9884\u7EA6\u4E2D\u5FC3\u4ECE\u5E93\u67E5\u8BE2
spring.datasource.dynamic.datasource.appointment.driverClassName=com.mysql.cj.jdbc.Driver
spring.datasource.dynamic.datasource.appointment.url=**********************************************************************************************************************************************************************
spring.datasource.dynamic.datasource.appointment.username=healthcare_cust_rw
spring.datasource.dynamic.datasource.appointment.password=cJlIQMR8ftQYFXL6






environment=dev
######################################################
########################[JMQ]#########################
######################################################

##\u81EA\u8425\u4E0D\u62C6\u5355mq\u6D88\u606F\u4E3B\u9898
jmq.middleware.filter.no.split.topic=0_275_physicalexammq_PRE
## \u81EA\u8425\u62C6\u5355\u6D88\u606F
jmq.middleware.filter.split.topic=physicalexammq_0_275_v2


##POP\u4E0D\u62C6\u5355mq\u6D88\u606F\u4E3B\u9898
jmq.middleware.filter.pop.no.split.topic=physicalexamination_0_276_PRE

##POP\u62C6\u5355mq\u6D88\u606F\u4E3B\u9898
jmq.middleware.filter.pop.split.topic=physicalexamination_0_275_PRE

##POP\u8BA2\u5355\u5B8C\u6210\u6D88\u606F\u4E3B\u9898
jmq.middleware.filter.pop.order.complete.topic=physicalexamination_0_275_PRE

## \u5546\u5BB6\u57DF\u9884\u7EA6\u7ED3\u679C\u56DE\u8C03topic
provider.appointment.result.callback=


jmq.middleware.filter.no.split.topic_pre=0_275_physicalexammq_PRE_TRANSFER