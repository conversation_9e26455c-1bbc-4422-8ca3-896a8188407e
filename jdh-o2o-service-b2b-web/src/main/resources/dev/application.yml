server:
  port: 80
  servlet:
    context-path: /api
spring:
  profiles:
    active: dev
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
    default-property-inclusion: non_null
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  servlet:
    # 应用的访问路径
    context-path: /api
  devtools:
    restart:
      enabled: true
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
  ## jmq
  # https://joyspace.jd.com/pages/W1Rw2L8996bu5FsPgUta
  # dev为测试站，可以本地直接链接 http://test.taishan.jd.com/jmq/application
  # 集群接入地址：https://joyspace.jd.com/pages/OCNOfPvDOzYzOzim8HBd
  jmq:
    enabled: true
    producers:
      reachStoreProducer:
        password: 6303daad915749a889a78331cfe421e4
        app: jdhreachstore
        address: test-nameserver.jmq.jd.local:50088
        enabled: true
      reachStoreMq2Producer:
        password: C4AE77FB
        app: jdho2oservice
        address: jmq-cluster.jd.local:80
        enabled: true
    consumers:
      jdhReachStoreConsumer:
        password: 6303daad915749a889a78331cfe421e4
        app: jdhreachstore
        address: test-nameserver.jmq.jd.local:50088
        enabled: true
      jdhReachStoreMq2Consumer:
        password: C4AE77FB
        app: jdho2oservice
        address: jmq-cluster.jd.local:80
      jdhNethpSyncMqConsumer:
        password: C4AE77FB
        app: jdho2oservice
        address: jmq-cluster.jd.local:80
        enabled: true
      jdhRefundMqConsumer:
        password: C4AE77FB
        app: jdho2oservice
        address: jmq-cluster.jd.local:80
        enabled: true
      locCodeConsumer:
        password: d0af9c1e556746d39334e9cfece1e723
        app: jdho2oservice
        address: nameserver.jmq.jd.local:80
        enabled: true
topics:
  jdhReachStoreConsumer:
    locCodeSendTopic: loc_order_send_code
    xfylLocCodeSendForwardYfbTopic: xfyl_loc_code_sync_forward_yfb
    providerCallback: provider_callback_topic_yfb
    nethpTriageEvent: NethpTriageEvent
    xfylNethpTriageEventForwardYfbTopic: xfyl_NethpTriageEvent_forward_yfb
    nethpTriageDiagRecycleEvent: triage_diag_recycle_event
    xfylNethpTriageDiagRecycleEventForwardYfbTopic: xfyl_triage_diag_recycle_event_forward_yfb
    refundResult: physicalexammq_orbCancelResult
    delayMessageTopic: delay_task_topic_test
    o2oServiceReport: o2o_service_report
    o2oServiceOrderStatus: o2o_service_order_status
    jdOrderbinlakeTopic: jd_order_binlake_yfb
    jdOrderbinlakeVtpTransferTopic: jd_order_binlake_vtp_transfer_yfb
    jdhPromiseBinlakeTopic: jdh_promise_binlake_yfb
    jdhMedicalPromiseBinlakeTopic: jdh_medical_promise_binlake_yfb
    jdhAngelWorkBinlakeTopic: jdh_angel_work_binlake_yfb
    jdhAngelTaskBinlakeTopic: jdh_angel_task_binlake_yfb
    jdhAngelShipBinlakeTopic: jdh_angel_ship_binlake_yfb
    withdrawChangeEventEventTopic: withdrawChangeEvent
    withdrawChangeEventEventForwardYfbTopic: withdrawChangeEvent_yfb
    cancelPayTopic: physicalexammq_ODC_CANCEL_v2
    reachNoticeTopic: o2o_reach_notice_yfb
    jdGmsProductCategoryPropertyTopic: xfyl_gms_product_category_property_yfb
    jdGmsProductCategoryPropertyTopicYF: xfyl_gms_product_category_property_yfb
    examinationSkuBinLakeTopic: examination_man_sku_binlake_pre
    vtpafs: test
    yunDingPrivacyNotificationTopic: yunding-privacy-notification
    callRecordingRetrievalTopic: xfyl_call_recording_retrieval
    callBindReleaseTopic: xfyl_call_bind_release
  jdhReachStoreMq2Consumer:
    nethpDoctorAuditEventTopic: DoctorAuditEvent
    xfylNethpDoctorAuditEventForwardYfbTopic: xfyl_DoctorAuditEvent_forward_yfb
    nethpDoctorChangeEventTopic: DoctorInfoChangeEvent
    xfylNethpDoctorChangeEventForwardYfbTopic: xfyl_DoctorInfoChangeEvent_forward_yfb
  order:
    middleware-no-split: 0_275_physicalexammq_PRE
    middleware-no-split-pre: 0_275_physicalexammq_PRE_TRANSFER
    pop-middleware-no-split: physicalexamination_0_276_PRE
    pop-middleware-split: physicalexamination_0_275_PRE
    self-middleware-no-split: physicalexammq_0_276_v2_PRE
    self-middleware-split: physicalexammq_0_275_v2_PRE
    middleware-pop-complete: physicalexammq_ODC_COMPLETE_PRE
  event:
    consumer.topic: o2o_service_event_topic_test
    forward.topic: promise_event_forward_topic_test
    medicalPromiseTopic: test
  reach:
    task.topic: o2o_service_reach_task_topic
    jdApp.push.topic: test
    file.submit.topic: sound_submit_topic
  settlement:
    ebsTopic: healthcare_examin_order_ebs
  delay:
    event.topic: o2o_delay_task_topic_yfb

# mybatisPlus
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
    cache-enabled: true
  typeAliasesPackage: com.jdh.o2oservice.infrastructure.repository.db.po
  mapper-locations: classpath:sqlmap/*.xml



# redis 配置
redis:
  jimUrl: jim://2914173422341158041/110000259
  endpoint: http://test.cfs.jim.jd.local

# forest配置
forest:
  backend: okhttp3
  max-retry-count: 3 # 请求失败后重试次数，默认为0次不重试
  max-retry-interval: 5000 #重试间隔时间
  connect-timeout: 5000 #链接超时时间
  timeout: 5000  # 请求超时时间
  ## 日志总开关，打开/关闭Forest请求/响应日志（默认为 true）
  log-enabled: true
  ## 打开/关闭Forest请求日志（默认为 true）
  log-request: true
  ## 打开/关闭Forest响应状态日志（默认为 true）
  log-response-status: true
  ## 打开/关闭Forest响应内容日志（默认为 false）
  log-response-content: true


# aces
tde:
  enableGM: false
  isProd: false
  rPath: ''
  token: eyJzaWciOiJBMDFMVlNzK2RicjRINEZsNnphblZQcFh4cjYxOFJ5OFBNTThPbm91R3ExcU94bGhjQjNEcmlqTHlDUUlLbHJNNkt5aTVORmxBamZVcHZKKzR2UWVBZnM3M3YrbEN0YzRMNlVhTVduaDBVYjk0NU1wck0wNFVFejFXQXN5dVFNVmxtM0hVc3ZNQzZoeXhMYnVHWVZXZXhxY0dRRktsYUkveGkzcGtTRnBHT2lxd0pMbEJLQlFRdjZBWXhpN2swOVIwYjgvVmFOL0xaRDJvdEd4M08ycXFtMFVmeGpqZ0JXV3B2dzFubWt5OUJBZlI2U25ldDVtbmMwZ0gxQXBWZlpaWmord21CMWFzRzExc2RMMzJIcEZkSWtwczFtLzlKaUlrSXBSV05RRllIU3VaNUtQZDZqaElIUS91MWIzaldOS0xMOVNMQjhDQ3RJNnNaTW1YR2RsWkE9PSIsImRhdGEiOnsiYWN0IjoiY3JlYXRlIiwiZWZmZWN0aXZlIjoxNzE0MzIwMDAwMDAwLCJleHBpcmVkIjoxNzc3NDc4NDAwMDAwLCJpZCI6Ik1UWXdOelk1WW1RdFl6RTFOaTAwTURnNExXSXhNell0TVdGbE1tSm1PRGt3WkRFeCIsImtleSI6IlArdTFjRG1ncVU5Z3ZkSm90QWxDdmlnSENsU2xJR1NURVljN0ttZlZvYlU9Iiwic2VydmljZSI6ImpkaG8yb3NlcnZpY2VfT1FoS3hKcW4iLCJzdHlwZSI6Mn0sImV4dGVybmFsRGF0YSI6eyJ6b25lIjoiQ04tMCJ9fQ==
# oss
oss:
  accessKey: JDC_78AC9ABFB94BD2B9280265E55CBC
  secretKey: 3B93ABFB7CE4A0907352C70E732923BB
  internalEndPoint: s3-internal.cn-north-1.jdcloud-oss.com
  publicEndPoint: s3.cn-north-1.jdcloud-oss.com
  region: cn-north-1
  connectionTimeout: 30000
  defaultBucket: o2o-service-yfb

# ES配置类
elasticsearchfactory:
  clusterName: jiesi-6.3
  nodeConnectionAddress: ************:40100;*************:20100;*************:20100
  securityUser: jiesi-6.3
  securityPassword: A5F4B854D85E052C9BA3C3EC051BFDFBF7952167

# ducc
laf:
  config:
    logger:
      enabled: true
      type: logback
      key: logger.level
    manager:
      application: jdos_kj_jdh-reach-store
      namespace: jdh_reach_store
      parameters:
        - name: autoListener
          value: true
      profile: dev
      resources:
        - name: common
          uri: ucc://${laf.config.manager.application}:${laf.config.manager.token}@test.ducc.jd.local/v1/namespace/${laf.config.manager.namespace}/config/common/profiles/${laf.config.manager.profile}?longPolling=60000&necessary=false
        - name: logging
          uri: ucc://${laf.config.manager.application}:${laf.config.manager.token}@test.ducc.jd.local/v1/namespace/${laf.config.manager.namespace}/config/logging/profiles/${laf.config.manager.profile}?longPolling=60000&necessary=false
        - name: reach
          uri: ucc://${laf.config.manager.application}:${laf.config.manager.token}@test.ducc.jd.local/v1/namespace/${laf.config.manager.namespace}/config/reach/profiles/${laf.config.manager.profile}?longPolling=60000&necessary=false
        - name: jm_via_config
          uri: ucc://${laf.config.manager.application}:${laf.config.manager.token}@test.ducc.jd.local/v1/namespace/${laf.config.manager.namespace}/config/jm_via_config/profiles/${laf.config.manager.profile}?longPolling=60000&necessary=false
        - name: via_config
          uri: ucc://${laf.config.manager.application}:${laf.config.manager.token}@test.ducc.jd.local/v1/namespace/${laf.config.manager.namespace}/config/via_config/profiles/${laf.config.manager.profile}?longPolling=60000&necessary=false
        - name: ability_executor_config
          uri: ucc://${laf.config.manager.application}:${laf.config.manager.token}@test.ducc.jd.local/v1/namespace/${laf.config.manager.namespace}/config/ability_executor_config/profiles/${laf.config.manager.profile}?longPolling=60000&necessary=false
        - name: provider
          uri: ucc://${laf.config.manager.application}:${laf.config.manager.token}@test.ducc.jd.local/v1/namespace/${laf.config.manager.namespace}/config/provider_config/profiles/${laf.config.manager.profile}?longPolling=60000&necessary=false
        - name: export_config
          uri: ucc://${laf.config.manager.application}:${laf.config.manager.token}@test.ducc.jd.local/v1/namespace/${laf.config.manager.namespace}/config/export_config/profiles/${laf.config.manager.profile}?longPolling=60000&necessary=false
        - name: fee_config
          uri: ucc://${laf.config.manager.application}:${laf.config.manager.token}@test.ducc.jd.local/v1/namespace/${laf.config.manager.namespace}/config/fee_config/profiles/${laf.config.manager.profile}?longPolling=60000&necessary=false
        - name: dispatch
          uri: ucc://${laf.config.manager.application}:${laf.config.manager.token}@test.ducc.jd.local/v1/namespace/${laf.config.manager.namespace}/config/dispatch/profiles/${laf.config.manager.profile}?longPolling=60000&necessary=false
        - name: angel_promise
          uri: ucc://${laf.config.manager.application}:${laf.config.manager.token}@test.ducc.jd.local/v1/namespace/${laf.config.manager.namespace}/config/angel_promise/profiles/${laf.config.manager.profile}?longPolling=60000&necessary=false
        - name: dict_config
          uri: ucc://${laf.config.manager.application}:${laf.config.manager.token}@test.ducc.jd.local/v1/namespace/${laf.config.manager.namespace}/config/dict_config/profiles/${laf.config.manager.profile}?longPolling=60000&necessary=false
        - name: groovy_script
          uri: ucc://${laf.config.manager.application}:${laf.config.manager.token}@ducc.jd.local/v1/namespace/${laf.config.manager.namespace}/config/groovy_script/profiles/${laf.config.manager.profile}?longPolling=60000&necessary=false
        - name: man_via_config
          uri: ucc://${laf.config.manager.application}:${laf.config.manager.token}@ducc.jd.local/v1/namespace/${laf.config.manager.namespace}/config/man_via_config/profiles/${laf.config.manager.profile}?longPolling=60000&necessary=false
        - name: feedback_config
          uri: ucc://${laf.config.manager.application}:${laf.config.manager.token}@test.ducc.jd.local/v1/namespace/${laf.config.manager.namespace}/config/feedback_config/profiles/${laf.config.manager.profile}?longPolling=60000&necessary=false
      token: 1d1cf3485d99450ab71d70efb3028bdc
      baseUrl: http://test.ducc-api.jd.local

## elasticjob
elasticjob:
  regCenter:
    #zookeeper 的ip:port
    serverLists: testpubli.zk.jddb.com:2181
    #名命空间，自己定义就好了
    namespace: publictest-noauth/xfyl/o2oServiceJob
    digest:
  jobs:
    # 服务单过期任务
    jdhO2oVoucherExpireJob:
      elasticJobClass: com.jdh.o2oservice.job.promise.voucher.VoucherExpireJob
      cron: 0 1 * * * ?
      shardingTotalCount: 1
      overwrite: true
    # 商家域自动处理预约中状态任务
    jdhProviderPromiseAutoProcessJob:
      elasticJobClass: com.jdh.o2oservice.job.provider.ProviderPromiseAutoProcessJob
      cron: 0 0 6 * * ?
      shardingTotalCount: 1
      overwrite: true
    # 服务者接单任务
    jdhO2oDispatchCalculateFactorJob:
      elasticJobClass: com.jdh.o2oservice.job.dispatch.DispatchCalculateFactorJob
      cron: 0 0 0 * * ?
      shardingTotalCount: 1
      overwrite: true
    # 定时清理状态为已过期以及逻辑删除排班
    detectExpiredScheduleJob:
      elasticJobClass: com.jdh.o2oservice.job.angel.DetectExpiredScheduleJob
      cron: 0 1 0 * * ?
      shardingTotalCount: 1
      overwrite: true
    # 重试ebs失败任务
    settlementEbsFailJob:
      elasticJobClass: com.jdh.o2oservice.job.settlement.SettlementEbsFailJob
      cron: 0 0 0/1 * * ?
      shardingTotalCount: 1
      overwrite: true
    providerBillCreateJob:
      elasticJobClass: com.jdh.o2oservice.job.provider.ProviderBillCreateJob
      cron: 0 2 0 * * ?
      shardingTotalCount: 1
      overwrite: true
    angelStationInventoryRollJob:
      elasticJobClass: com.jdh.o2oservice.job.angel.AngelStationInventoryRollJob
      cron: 0 2 0 * * ?
      shardingTotalCount: 1
      overwrite: true

timLine:
  aspId: 00_2d8cd9fb6a764197
  secret: a28593b761ca4140b71b530dd3c3df6a
  noticeId: ~xfyl
  url: http://open.timline.jd.com/open-apis/v1/messages/notice
  noticeMessage: notice_message
  toTerminal: 7
  accessUrl: http://open.timline.jd.com/open-apis/v1/auth/get_access_token
  app: ee

#短链
#生成短连域名
shortUrlService:
  domain: 3.cn
  #生成短链长度
  length: 8
  #生成短链秘钥，登录http://s.3.cn/userIndex.action 线上点击API文档获取，建议产品提供稳定的key 现在为徐兵舰
  key: 4a5873d27bddb4142ed6cb6d70ee0bec

##地图
gis:
  map:
    baseurl: https://uat-proxy.jd.com
    appkey: 11y6vpd87lu5kwxtx9OKn7K97tA986K87O91
    scene: openapi
    tag:
# sso
# https://cf.jd.com/pages/viewpage.action?pageId=748451623
sso:
  clientId: "jdh-o2o-service"
  clientSecret: "7ec95cfccb0643e79253171e88d24f6f"
  ## 多个路径以“,”分割
  excludePath: "/api/,/product/route,/provider/ship"
#  ajaxCallbackPolicy: "Referer"
#  endpoint: "https://ssa.jd.com"
#  apiEndpoint: "http://ssa.jd.local"


# uim权限系统配置  http://uim2.jd.com/system/info?systemCode=xqCV5Q2ZUaAS4D4S
uim:
  appKey: 90e39b02d6b444cdb67de17d7266fafe
  appToken: 9ae07c31130642d6a6d99340623831da
  tenantCode: CN.JD.GROUP
  dimResCode: belongBizCodeDataSource

#达达配置
dada:
  base:
    url: http://openplatform.qa.imdada.local
  sourceId: 74917
  appKey: dada3af1e313b0d1c9a
  appSecret: ee2164e6c9b1a593133a2ed5f6b79553

#普通配置
o2o:
  angelPromise:
    angelTrack: https://laputa-yf.jd.com/serviceHome/nurse/nurseposition?promiseId=%s
    angelTransferTrack: https://laputa-yf.jd.com/nurse-on-site-service/service/position?promiseId=%s&shipId=%s&angelType=%s
    orderListLink: https://laputa-yf.jd.com/nurse-on-site-service/service/order-list
    orderDetailLink: https://laputa-yf.jd.com/nurse-on-site-service/service/orderDetail?workId=%s

#闪送配置
shansong:
  config:
    url:
    clientId:
    accessToken:
    shopId:

# 统一操作日志系统分配的appName
opLog:
  appName: jdh-o2o-service
  merchantAppName: xfyl-shop