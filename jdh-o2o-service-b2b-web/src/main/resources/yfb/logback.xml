<?xml version='1.0' encoding='UTF-8' ?>

<configuration>

    <!-- 日志存放路径  -->
    <property name="log.path" value="/export/Logs/Domains/jdh-o2o-service-b2b.jd.com/server1/logs/"/>
    <!-- 日志输出格式 -->
    <property name="log.pattern"
              value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%p] [%C{0}:%L,%M\\(\\)] [%X{PFTID}] [%X{logid}] [%thread] -%m%n"/>


    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
    </appender>

    <appender name="LOGGER-DEBUG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/debug.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/debug.log.%d{yyyy-w}</fileNamePattern>
            <maxHistory>60</maxHistory>
            <totalSizeCap>512MB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>DEBUG</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <appender name="LOGGER-INFO" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/info.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/info.log.%d{yyyy-w}</fileNamePattern>
            <maxHistory>60</maxHistory>
            <totalSizeCap>512MB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
        </filter>
    </appender>

    <appender name="LOGGER-ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/error.log.%d{yyyy-w}</fileNamePattern>
            <maxHistory>60</maxHistory>
            <totalSizeCap>512MB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <appender name="LOGGER-SQL" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/sqlmap.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/sqlmap.log.%d{yyyy-w}</fileNamePattern>
            <maxHistory>60</maxHistory>
            <totalSizeCap>512MB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
    </appender>


    <!-- sql -->
    <logger name="com.jdh.o2oservice.b2b.infrastructure.db"  additivity="false" level="debug">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="LOGGER-SQL"/>
    </logger>

    <logger name="com.jd.jim.cli.springcache" additivity="false" level="debug">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="LOGGER-DEBUG"/>
    </logger>

    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="LOGGER-DEBUG"/>
        <appender-ref ref="LOGGER-INFO"/>
        <appender-ref ref="LOGGER-ERROR"/>
    </root>

</configuration>
