<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:jsf="http://jsf.jd.com/schema/jsf"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
                            http://jsf.jd.com/schema/jsf http://jsf.jd.com/schema/jsf/jsf.xsd"
       default-autowire="byName">

    <jsf:registry id="jsfRegistry" protocol="jsfRegistry" index="${jsf.registry.index}"/>

    <!--全局唯一id-->
    <jsf:consumer id="globalServiceID" interface="com.global.service.id.GlobalServiceID" retries="2" protocol="jsf"
                  alias="${com.global.service.id.GlobalServiceID.alias}" timeout="5000">
        <jsf:parameter key="token" value="${com.global.service.id.GlobalServiceID.token}" hide="true"/>
    </jsf:consumer>

    <!-- 外呼服务 -->
    <jsf:consumer id="callJsfExport" interface="com.jdh.o2oservice.export.support.CallJsfExport"
                  protocol="jsf" alias="${common.o2o.alias}" timeout="5000">
        <jsf:parameter key="token" value="${common.o2o.token}" hide="true" />
    </jsf:consumer>

    <!-- 运营端视图 -->
    <jsf:consumer id="manViaJsfExport" interface="com.jdh.o2oservice.export.support.ManViaJsfExport"
                  protocol="jsf" alias="${common.o2o.alias}" timeout="5000">
        <jsf:parameter key="token" value="${common.o2o.token}" hide="true" />
    </jsf:consumer>

    <!-- 履约服务 -->
    <jsf:consumer id="promiseJsfExport" interface="com.jdh.o2oservice.export.promise.PromiseJsfExport"
                  protocol="jsf" alias="${common.o2o.alias}" timeout="5000">
        <jsf:parameter key="token" value="${common.o2o.token}" hide="true" />
    </jsf:consumer>

    <!-- 交易服务 -->
    <jsf:consumer id="tradeJsfExport" interface="com.jdh.o2oservice.export.trade.TradeJsfExport"
                  protocol="jsf" alias="${common.o2o.alias}" timeout="5000">
        <jsf:parameter key="token" value="${common.o2o.token}" hide="true" />
    </jsf:consumer>

    <!-- 文件导出服务 -->
    <jsf:consumer id="jdhExportToolsExportService" interface="com.jdh.o2oservice.export.support.JdhExportToolsExportService"
                  protocol="jsf" alias="${common.o2o.alias}" timeout="10000">
        <jsf:parameter key="token" value="${common.o2o.token}" hide="true" />
    </jsf:consumer>

    <!-- 文件管理服务 -->
    <jsf:consumer id="fileManageJsfExport" interface="com.jdh.o2oservice.export.support.FileManageJsfExport"
                  protocol="jsf" alias="${common.o2o.alias}" timeout="10000">
        <jsf:parameter key="token" value="${common.o2o.token}" hide="true" />
    </jsf:consumer>

    <!-- 派单服务 -->
    <jsf:consumer id="dispatchJsfExport" interface="com.jdh.o2oservice.export.dispatch.DispatchJsfExport"
                  protocol="jsf" alias="${common.o2o.alias}" timeout="5000">
        <jsf:parameter key="token" value="${common.o2o.token}" hide="true" />
    </jsf:consumer>

    <!-- 虚拟pin -->
    <jsf:consumer id="pinExportService" interface="com.jd.health.ares.open.platform.export.service.PinExportService"
                  protocol="jsf" alias="${com.jd.health.ares.open.platform.export.service.PinExportService.alias}" timeout="5000">
        <jsf:parameter key="token" value="${com.jd.health.ares.open.platform.export.service.PinExportService.token}" hide="true" />
    </jsf:consumer>

    <!-- 商品sku服务 -->
    <jsf:consumer id="productJsfExport" interface="com.jdh.o2oservice.export.product.ProductJsfExport"
                  protocol="jsf" alias="${common.o2o.alias}" timeout="5000">
        <jsf:parameter key="token" value="${common.o2o.token}" hide="true" />
    </jsf:consumer>

    <!-- 合同信息 -->
    <jsf:consumer id="contractQueryApi" interface="com.jd.contract.api.ContractQueryApi"
                  protocol="jsf" alias="${com.jd.contract.api.ContractQueryApi.alias}" timeout="5000" retries="3">
    </jsf:consumer>

    <!--  uim  -->
    <jsf:consumer id="uim2MenuFacade" interface="com.jd.uim2.facade.jsf.Uim2MenuFacade"
                  protocol="jsf" alias="${com.jd.uim2.facade.jsf.alias}" timeout="5000">
    </jsf:consumer>

    <!--  uim  -->
    <jsf:consumer id="uim2DimPermissionFacade" interface="com.jd.uim2.facade.jsf.Uim2DimPermissionFacade"
                  protocol="jsf" alias="${com.jd.uim2.facade.jsf.alias}" timeout="5000">
    </jsf:consumer>

    <!--  uim  -->
    <jsf:consumer id="uim2RoleFacade" interface="com.jd.uim2.facade.jsf.Uim2RoleFacade"
                  protocol="jsf" alias="${com.jd.uim2.facade.jsf.alias}" timeout="5000">
    </jsf:consumer>


    <jsf:consumer id="hrUserService" interface="com.jd.enterprise.api.client.HrUserService" protocol="jsf"
                  alias="${com.jd.enterprise.api.client.hr.alias}" timeout="5000" retries="0">
    </jsf:consumer>

    <!--智能文本解析接口 https://lbsapi.jd.com/iframe.html?childURL=docid=2-75&childNav=1-17-->
    <jsf:consumer id="textParsingService" interface="com.jd.lbs.jdlbsapi.c2c.TextParsingService"
                  protocol="jsf" alias="${com.jd.lbs.jdlbsapi.c2c.TextParsingService.alias}"
                  timeout="5000" retries="1">
        <jsf:parameter key="token" value="${com.jd.lbs.jdlbsapi.c2c.TextParsingService.token}" hide="true" />
    </jsf:consumer>

    <!--四级地址服务接口 https://cf.jd.com/pages/viewpage.action?pageId=148161039 -->
    <jsf:consumer id="jDAddressDistrictService" interface="com.jd.addresstranslation.api.address.JDAddressDistrictService"
                  protocol="jsf" alias="${com.jd.addresstranslation.api.address.JDAddressDistrictService.alias}"
                  timeout="5000" retries="1">
        <jsf:parameter key="token" value="${com.jd.addresstranslation.api.address.JDAddressDistrictService.token}" hide="true" />
    </jsf:consumer>

    <!-- 获取用户信息 -->
    <jsf:consumer id="userInfoExportServiceJsf" protocol="jsf"
                  interface="com.jd.user.sdk.export.UserInfoExportService"
                  alias="${com.jd.user.sdk.export.UserInfoExportService.alias}" timeout="5000">
        <jsf:parameter key="source" value="${com.jd.user.sdk.export.UserInfoExportService.source}" hide="true" />
    </jsf:consumer>

    <!-- 工单服务 -->
    <jsf:consumer id="angelWorkReadGwExport" interface="com.jdh.o2oservice.export.angelpromise.AngelWorkReadGwExport"
                  protocol="jsf" alias="${common.o2o.alias}" timeout="5000">
        <jsf:parameter key="token" value="${common.o2o.token}" hide="true" />
    </jsf:consumer>

    <!-- 工单服务 -->
    <jsf:consumer id="angelServiceRecordJsfExport" interface="com.jdh.o2oservice.export.angelpromise.AngelServiceRecordJsfExport"
                  protocol="jsf" alias="${common.o2o.alias}" timeout="5000">
        <jsf:parameter key="token" value="${common.o2o.token}" hide="true" />
    </jsf:consumer>

    <!-- 题库 -->
    <jsf:consumer id="questionReadExport" interface="com.jdh.o2oservice.export.product.QuestionReadExport"
                  protocol="jsf" alias="${common.o2o.alias}" timeout="5000">
        <jsf:parameter key="token" value="${common.o2o.token}" hide="true" />
    </jsf:consumer>

</beans>