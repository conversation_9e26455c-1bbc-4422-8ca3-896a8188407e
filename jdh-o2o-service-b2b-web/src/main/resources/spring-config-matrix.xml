<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean id="jdh-o2oservice-b2b-matrix" class="com.jd.matrix.core.Matrix">
        <property name="exportClassConfig">
            <bean class="com.jd.matrix.core.spec.ExportClassConfig">
                <!--扩展点定义以及相关类的包名 -->
                <property name="packageSet">
                    <set>
                        <value>com.jdh.o2oservice</value>
                    </set>
                </property>
            </bean>
        </property>

        <property name="matrixEnvConfig">
            <bean class="com.jd.matrix.core.spec.MatrixEnvConfig">
                <!--在藏经阁中应用名称，扩展点相关信息自动注册到藏经阁 -->
                <property name="cjgAppName">
                    <value>jdos_jdh-o2o-service-b2b</value>
                </property>
                <!--ump中应用名称 -->
                <property name="umpAppName">
                    <value>jdos_jdh-o2o-service-b2b</value>
                </property>
                <!--jone或者jdos 应用分组名称 用于后续垂直业务身份热部署 -->
                <property name="deployGroupName">
                    <value>jdos_jdh-o2o-service-b2b</value>
                </property>
                <!--matrix所在环境 -->
                <!--                <property name="environment">-->
                <!--                    <value>DEV</value>-->
                <!--                </property>-->
                <!--matrix所在环境 -->
                <property name="environment">
                    <value>ON_LINE</value>
                </property>


                <!--                <property name="ignoreDucc">-->
                <!--                    <value>false</value>-->
                <!--                </property>-->

            </bean>
        </property>
    </bean>

    <!-- 流程编排配置 -->

    <!--并行执行线程池定义-->
    <bean id="threadPoolExector" class="java.util.concurrent.ThreadPoolExecutor">
        <constructor-arg index="0" value="5"></constructor-arg>
        <constructor-arg index="1" value="5"></constructor-arg>
        <constructor-arg index="2" value="1000"></constructor-arg>
        <constructor-arg index="3" value="SECONDS"
                         type="java.util.concurrent.TimeUnit" />
        <constructor-arg index="4">
            <bean class="java.util.concurrent.ArrayBlockingQueue">
                <constructor-arg value="1000" />
            </bean>
        </constructor-arg>
    </bean>

</beans>