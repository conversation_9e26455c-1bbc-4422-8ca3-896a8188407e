<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>jdh-o2o-service-b2b</artifactId>
        <groupId>com.jdh.o2oservice.b2b</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>jdh-o2o-service-b2b-web</artifactId>
    <version>${revision}</version>
    <packaging>war</packaging>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.jdh.o2oservice.b2b</groupId>
            <artifactId>jdh-o2o-service-b2b-application</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>

        <!-- SpringBoot test -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.jsqlparser</groupId>
            <artifactId>jsqlparser</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.security.configsec</groupId>
            <artifactId>spring-configsec-sdk</artifactId>
            <version>1.0.3.11</version>
        </dependency>

        <!-- 数据库 start -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jd.security</groupId>
            <artifactId>jd-security-tomcat</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.tomcat</groupId>
            <artifactId>tomcat-jasper</artifactId>
            <scope>provided</scope>
        </dependency>


        <dependency>
            <groupId>org.apache.tomcat</groupId>
            <artifactId>tomcat-catalina</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.jd.cybertron</groupId>
            <artifactId>cybertron-client-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.jd.laf.config</groupId>
                    <artifactId>laf-config-client-jd</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.jd.laf.config</groupId>
                    <artifactId>laf-config-client-jd-spring</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.dubbo</groupId>
                    <artifactId>dubbo-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.jd</groupId>
                    <artifactId>jsf</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-collections4</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
            </exclusions>
        </dependency>

    </dependencies>

    <profiles>
        <profile>
            <id>dev</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <profiles.active>dev</profiles.active>
            </properties>
        </profile>
        <profile>
            <id>yfb</id>
            <properties>
                <profiles.active>yfb</profiles.active>
            </properties>
        </profile>
        <profile>
            <id>production</id>
            <properties>
                <profiles.active>production</profiles.active>
            </properties>
        </profile>
    </profiles>
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.5.14</version>
                <configuration>
                    <fork>true</fork> <!-- 如果没有该配置，devtools不会生效 -->
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!-- maven打包成war包,如果是老的web项目理论上讲必须有web.xml,如果没有择报错,但是springboot是没有web.xml,所以就不校验了-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>3.2.3</version>
                <configuration>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.2.0</version>
            </plugin>
        </plugins>
        <finalName>${project.artifactId}</finalName>

        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <excludes>
                    <exclude>dev/*</exclude>
                    <exclude>yfb/*</exclude>
                    <exclude>production/*</exclude>
                </excludes>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                    <include>**/*.yml</include>
                    <include>**/*.txt</include>
                </includes>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/resources/${profiles.active}</directory>
                <includes>
                    <include>**/*.yml</include>
                    <include>**/*.xml</include>
                    <include>**/*.properties</include>
                    <include>**/*.txt</include>
                </includes>
                <filtering>false</filtering>
            </resource>
        </resources>
    </build>


</project>